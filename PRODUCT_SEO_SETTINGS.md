# 产品SEO设置功能

## 🎯 功能概述

为产品管理系统添加了完整的SEO设置功能，管理员可以为每个产品自定义SEO标题、描述、关键词以及社交媒体分享信息，提升产品页面的搜索引擎优化效果。

## 📋 实现内容

### 1. 数据库支持
产品模型已包含完整的SEO字段：

```sql
-- SEO基础字段
seoTitle       String?  -- SEO标题
seoDescription String?  -- SEO描述
seoKeywords    String?  -- SEO关键词

-- Open Graph社交媒体字段
ogTitle        String?  -- OG标题
ogDescription  String?  -- OG描述
ogImage        String?  -- OG图片URL

-- 结构化数据字段
schemaData     String?  -- JSON-LD结构化数据
```

### 2. 管理界面

#### 产品创建页面 (`/admin/products/new`)
- ✅ **SEO Settings**部分已添加
- ✅ **基础SEO字段**：标题、描述、关键词
- ✅ **Open Graph字段**：社交媒体分享优化
- ✅ **字符计数提示**：实时显示字符长度
- ✅ **最佳实践提示**：SEO优化建议

#### 产品编辑页面 (`/admin/products/[id]/edit`)
- ✅ **完整SEO设置**：与创建页面功能一致
- ✅ **数据回填**：正确显示现有SEO数据
- ✅ **实时更新**：修改后立即保存

### 3. 前端SEO应用

#### 产品详情页面 (`/products/[category]/[slug]`)
- ✅ **动态Meta标签**：根据SEO设置生成
- ✅ **Open Graph标签**：社交媒体分享优化
- ✅ **结构化数据**：产品信息的结构化标记
- ✅ **智能回退**：SEO字段为空时使用产品基础信息

## 🔧 技术实现

### SEO字段配置
```typescript
interface ProductForm {
  // 基础产品信息...
  
  // SEO字段
  seoTitle?: string        // SEO标题
  seoDescription?: string  // SEO描述
  seoKeywords?: string     // SEO关键词
  ogTitle?: string         // OG标题
  ogDescription?: string   // OG描述
  ogImage?: string         // OG图片URL
}
```

### 管理界面实现
```tsx
{/* SEO Settings */}
<div className="bg-white shadow rounded-lg p-6">
  <h2 className="text-lg font-medium text-gray-900 mb-4">SEO Settings</h2>
  
  <div className="space-y-4">
    {/* SEO Title */}
    <div>
      <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-700">
        SEO Title
      </label>
      <input
        type="text"
        name="seoTitle"
        value={formData.seoTitle}
        onChange={handleInputChange}
        placeholder="Custom title for search engines"
      />
      <p className="mt-1 text-sm text-gray-500">
        Recommended length: 50-60 characters. Current: {formData.seoTitle?.length || 0}
      </p>
    </div>
    
    {/* 其他SEO字段... */}
  </div>
</div>
```

### 前端Meta标签生成
```typescript
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const product = await getProduct(params.slug)
  
  return {
    title: product.seoTitle || `${product.name} | Enterprise CMS`,
    description: product.seoDescription || product.excerpt || product.description,
    keywords: product.seoKeywords,
    openGraph: {
      title: product.ogTitle || product.name,
      description: product.ogDescription || product.excerpt || product.description,
      type: 'website',
      images: product.images?.length > 0 ? [{ url: product.images[0].url }] : [],
    },
    other: {
      'product:price:amount': product.price?.toString() || '',
      'product:price:currency': 'USD',
      'product:availability': product.status === 'ACTIVE' ? 'in stock' : 'out of stock',
    },
  }
}
```

## 🎨 界面特性

### SEO Settings部分
```
┌─────────────────────────────────────────────────────┐
│ SEO Settings                                        │
├─────────────────────────────────────────────────────┤
│ SEO Title                                           │
│ [Custom title for search engines...            ]   │
│ Recommended length: 50-60 characters. Current: 45  │
│                                                     │
│ SEO Description                                     │
│ [Meta description for search engines...        ]   │
│ [                                               ]   │
│ [                                               ]   │
│ Recommended length: 150-160 characters. Current: 128│
│                                                     │
│ SEO Keywords                                        │
│ [cms, content management, website builder      ]   │
│ Separate keywords with commas. Focus on relevant   │
│ terms for your product.                             │
│                                                     │
│ ─────────────────────────────────────────────────── │
│ Open Graph (Social Media)                          │
│                                                     │
│ OG Title                                            │
│ [Title for social media sharing...             ]   │
│                                                     │
│ OG Description                                      │
│ [Description for social media sharing...       ]   │
│ [                                               ]   │
│                                                     │
│ OG Image URL                                        │
│ [https://example.com/image.jpg                 ]   │
│ Recommended size: 1200x630 pixels for optimal      │
│ social media display.                               │
└─────────────────────────────────────────────────────┘
```

## 🌟 功能特性

### 1. **智能回退机制**
- **SEO标题**：为空时使用产品名称
- **SEO描述**：为空时使用产品摘要或描述
- **OG标题**：为空时使用SEO标题
- **OG描述**：为空时使用SEO描述
- **OG图片**：为空时使用第一张产品图片

### 2. **实时字符计数**
- **SEO标题**：显示当前字符数，推荐50-60字符
- **SEO描述**：显示当前字符数，推荐150-160字符
- **实时更新**：输入时实时更新字符计数

### 3. **最佳实践提示**
- **字符长度建议**：提供SEO最佳实践的字符长度建议
- **关键词指导**：提供关键词使用的指导说明
- **图片尺寸建议**：提供社交媒体图片的最佳尺寸

### 4. **完整的Open Graph支持**
- **社交媒体优化**：完整的OG标签支持
- **图片优化**：支持自定义社交媒体分享图片
- **描述优化**：独立的社交媒体描述设置

## 🚀 使用指南

### 管理员操作流程

#### 创建产品时设置SEO
1. **填写基础信息**：产品名称、描述等
2. **滚动到SEO Settings**：找到SEO设置部分
3. **设置SEO标题**：
   - 输入针对搜索引擎优化的标题
   - 注意字符长度建议（50-60字符）
   - 留空则使用产品名称
4. **设置SEO描述**：
   - 输入吸引用户点击的描述
   - 注意字符长度建议（150-160字符）
   - 留空则使用产品描述
5. **设置关键词**：
   - 输入相关的搜索关键词
   - 用逗号分隔多个关键词
   - 专注于产品相关的术语
6. **设置Open Graph**：
   - 设置社交媒体分享标题
   - 设置社交媒体分享描述
   - 设置社交媒体分享图片URL

#### 编辑现有产品SEO
1. **进入产品编辑页面**：`/admin/products/[id]/edit`
2. **查看现有SEO设置**：系统会显示当前的SEO配置
3. **修改SEO信息**：根据需要更新各项SEO设置
4. **保存更改**：点击"Update Product"保存修改

### SEO最佳实践

#### 标题优化
- **包含主要关键词**：在标题中包含产品的主要关键词
- **保持简洁**：控制在50-60字符以内
- **避免关键词堆砌**：自然地使用关键词
- **品牌一致性**：保持与品牌风格一致

#### 描述优化
- **吸引点击**：编写能吸引用户点击的描述
- **包含关键信息**：突出产品的主要特点和优势
- **控制长度**：保持在150-160字符以内
- **行动号召**：适当包含行动号召语句

#### 关键词策略
- **相关性优先**：选择与产品高度相关的关键词
- **长尾关键词**：包含一些长尾关键词
- **竞争分析**：研究竞争对手使用的关键词
- **定期更新**：根据搜索趋势定期更新关键词

## 📊 SEO效果监控

### 前端应用验证
1. **查看页面源码**：检查Meta标签是否正确生成
2. **社交媒体测试**：在社交媒体平台测试分享效果
3. **搜索引擎测试**：使用Google Search Console验证
4. **结构化数据测试**：使用Google结构化数据测试工具

### Meta标签示例
```html
<!-- 基础SEO标签 -->
<title>InstAI CMS Pro | Enterprise Content Management | Enterprise CMS</title>
<meta name="description" content="Professional content management system designed for enterprise-level operations with advanced features and scalability." />
<meta name="keywords" content="cms, content management, enterprise, website builder, digital platform" />

<!-- Open Graph标签 -->
<meta property="og:title" content="InstAI CMS Pro - Enterprise Solution" />
<meta property="og:description" content="Transform your digital presence with our enterprise-grade content management system." />
<meta property="og:image" content="https://example.com/product-image.jpg" />
<meta property="og:type" content="website" />

<!-- 产品特定标签 -->
<meta property="product:price:amount" content="299.99" />
<meta property="product:price:currency" content="USD" />
<meta property="product:availability" content="in stock" />
```

现在产品管理系统具备了完整的SEO优化功能！管理员可以为每个产品设置专业的SEO信息，提升搜索引擎排名和社交媒体分享效果。🎉
