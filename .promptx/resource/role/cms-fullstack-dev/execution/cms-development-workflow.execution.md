<execution>
  <constraint>
    ## 项目技术约束
    - **Next.js 15 App Router**：必须使用最新的应用路由器架构，不使用Pages Router
    - **TypeScript严格模式**：启用严格类型检查，确保类型安全
    - **Prisma ORM限制**：所有数据库操作必须通过Prisma进行，禁止原生SQL
    - **认证机制固定**：使用JWT + Cookie双重认证，不改变现有认证架构
    - **API响应格式**：必须遵循统一的ApiResponse接口格式
    
    ## 代码质量约束
    - **ESLint规则**：严格遵循项目配置的ESLint规则
    - **组件命名规范**：React组件使用PascalCase，文件名与组件名一致
    - **API路由规范**：API路由文件必须导出标准的HTTP方法函数
    - **错误处理统一**：所有API都必须包含统一的错误处理机制
  </constraint>

  <rule>
    ## 开发流程强制规则
    - **功能开发顺序**：数据模型 → API接口 → 前端组件 → 集成测试
    - **代码提交规范**：每个功能必须包含相应的类型定义和错误处理
    - **测试覆盖要求**：核心业务逻辑必须有相应的测试用例
    - **文档同步更新**：新增功能必须同步更新API文档和使用说明
    - **安全审查必须**：涉及用户数据的功能必须进行安全审查
    
    ## 代码审查规则
    - **类型安全检查**：确保所有变量和函数都有明确的类型定义
    - **错误边界验证**：验证所有可能的错误情况都有适当的处理
    - **性能影响评估**：评估新代码对系统性能的潜在影响
    - **安全漏洞检查**：检查是否存在SQL注入、XSS等安全漏洞
  </rule>

  <guideline>
    ## 开发最佳实践指南
    - **组件设计原则**：保持组件的单一职责，提高可复用性
    - **状态管理策略**：优先使用React内置状态管理，复杂状态考虑Context API
    - **API设计原则**：遵循RESTful设计，保持接口的一致性和可预测性
    - **错误处理策略**：提供用户友好的错误信息，同时记录详细的错误日志
    - **性能优化建议**：使用React.memo、useMemo、useCallback优化渲染性能
    - **代码组织建议**：相关功能的代码放在同一目录下，便于维护和查找
  </guideline>

  <process>
    ## CMS功能开发标准流程
    
    ### Step 1: 需求分析与设计 (25%)
    ```mermaid
    flowchart TD
        A[需求收集] --> B[用户故事编写]
        B --> C[数据模型设计]
        C --> D[API接口设计]
        D --> E[UI/UX设计]
        E --> F[技术方案确认]
    ```
    
    **关键输出**：
    - 用户故事和验收标准
    - 数据库模型定义
    - API接口文档
    - UI原型和交互设计
    
    ### Step 2: 后端开发 (35%)
    ```mermaid
    flowchart TD
        A[数据库模型更新] --> B[Prisma Schema修改]
        B --> C[数据库迁移]
        C --> D[API路由实现]
        D --> E[业务逻辑开发]
        E --> F[错误处理实现]
        F --> G[API测试]
    ```
    
    **开发检查清单**：
    - [ ] Prisma模型定义正确
    - [ ] 数据库迁移成功执行
    - [ ] API接口遵循RESTful规范
    - [ ] 包含完整的错误处理
    - [ ] 通过API测试验证
    
    ### Step 3: 前端开发 (30%)
    ```mermaid
    flowchart TD
        A[组件结构设计] --> B[TypeScript类型定义]
        B --> C[UI组件开发]
        C --> D[状态管理实现]
        D --> E[API集成]
        E --> F[用户交互优化]
        F --> G[响应式适配]
    ```
    
    **前端开发标准**：
    - [ ] 组件具有清晰的props接口
    - [ ] 包含loading和error状态处理
    - [ ] 实现响应式设计
    - [ ] 通过可访问性检查
    - [ ] 性能优化到位
    
    ### Step 4: 集成测试与优化 (10%)
    ```mermaid
    flowchart TD
        A[功能集成测试] --> B[用户体验测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[浏览器兼容性测试]
        E --> F[移动端测试]
        F --> G[优化和修复]
    ```
    
    **测试验收标准**：
    - [ ] 所有用户故事通过验收测试
    - [ ] 页面加载时间 < 3秒
    - [ ] 移动端体验良好
    - [ ] 无安全漏洞
    - [ ] 跨浏览器兼容
    
    ## 代码质量保证流程
    
    ### 开发阶段质量检查
    ```mermaid
    graph LR
        A[编写代码] --> B[ESLint检查]
        B --> C[TypeScript编译]
        C --> D[单元测试]
        D --> E[代码审查]
        E --> F[集成测试]
    ```
    
    ### 部署前质量门禁
    ```mermaid
    graph TD
        A[代码合并] --> B{自动化测试}
        B -->|通过| C{性能测试}
        B -->|失败| D[修复问题]
        C -->|通过| E{安全扫描}
        C -->|失败| D
        E -->|通过| F[部署准备]
        E -->|失败| D
        D --> A
    ```
    
    ## 问题诊断与解决流程
    
    ### 问题分类与优先级
    ```mermaid
    graph TD
        A[问题报告] --> B{影响范围}
        B -->|系统崩溃| C[P0 - 立即修复]
        B -->|功能异常| D[P1 - 24小时内]
        B -->|体验问题| E[P2 - 一周内]
        B -->|优化建议| F[P3 - 下个版本]
    ```
    
    ### 问题解决标准流程
    ```mermaid
    flowchart TD
        A[问题复现] --> B[根因分析]
        B --> C[解决方案设计]
        C --> D[代码修复]
        D --> E[测试验证]
        E --> F[部署修复]
        F --> G[问题跟踪]
    ```
  </process>

  <criteria>
    ## 开发质量评价标准
    
    ### 代码质量指标
    - ✅ TypeScript类型覆盖率 ≥ 95%
    - ✅ ESLint规则通过率 = 100%
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ 代码重复率 ≤ 5%
    - ✅ 函数复杂度 ≤ 10
    
    ### 性能质量指标
    - ✅ 首屏加载时间 ≤ 3秒
    - ✅ API响应时间 ≤ 500ms
    - ✅ 页面交互响应 ≤ 100ms
    - ✅ 移动端性能评分 ≥ 90
    - ✅ 内存使用稳定无泄漏
    
    ### 用户体验指标
    - ✅ 可访问性评分 ≥ 95
    - ✅ 移动端适配完美
    - ✅ 跨浏览器兼容性良好
    - ✅ 错误处理用户友好
    - ✅ 加载状态反馈及时
    
    ### 安全质量指标
    - ✅ 无SQL注入漏洞
    - ✅ 无XSS攻击风险
    - ✅ 认证授权机制完善
    - ✅ 敏感数据加密存储
    - ✅ API访问控制严格
    
    ### 维护性指标
    - ✅ 代码结构清晰合理
    - ✅ 文档完整准确
    - ✅ 组件复用率高
    - ✅ 配置管理规范
    - ✅ 错误日志详细
  </criteria>
</execution>
