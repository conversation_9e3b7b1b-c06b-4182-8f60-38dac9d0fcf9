{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-23T12:15:45.788Z", "updatedAt": "2025-07-23T12:15:45.792Z", "resourceCount": 3}, "resources": [{"id": "cms-fullstack-dev", "source": "project", "protocol": "role", "name": "Cms Fullstack Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/cms-fullstack-dev/cms-fullstack-dev.role.md", "metadata": {"createdAt": "2025-07-23T12:15:45.791Z", "updatedAt": "2025-07-23T12:15:45.791Z", "scannedAt": "2025-07-23T12:15:45.791Z", "path": "role/cms-fullstack-dev/cms-fullstack-dev.role.md"}}, {"id": "cms-development-workflow", "source": "project", "protocol": "execution", "name": "Cms Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/cms-fullstack-dev/execution/cms-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-23T12:15:45.792Z", "updatedAt": "2025-07-23T12:15:45.792Z", "scannedAt": "2025-07-23T12:15:45.792Z", "path": "role/cms-fullstack-dev/execution/cms-development-workflow.execution.md"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/cms-fullstack-dev/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-07-23T12:15:45.792Z", "updatedAt": "2025-07-23T12:15:45.792Z", "scannedAt": "2025-07-23T12:15:45.792Z", "path": "role/cms-fullstack-dev/thought/fullstack-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"role": 1, "execution": 1, "thought": 1}, "bySource": {"project": 3}}}