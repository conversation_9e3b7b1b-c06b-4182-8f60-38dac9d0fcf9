const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testFeaturedBlog() {
  try {
    console.log('🧪 Testing Featured Blog functionality...\n')

    // 1. 检查数据库模型是否包含featured字段
    console.log('1. Checking database schema...')
    const posts = await prisma.post.findMany({
      select: {
        id: true,
        title: true,
        featured: true,
        status: true
      },
      take: 5
    })
    
    console.log(`✅ Found ${posts.length} posts with featured field`)
    posts.forEach(post => {
      console.log(`   - ${post.title}: featured=${post.featured}, status=${post.status}`)
    })
    console.log()

    // 2. 测试创建Featured文章
    console.log('2. Testing featured post creation...')
    const testPost = await prisma.post.create({
      data: {
        title: 'Test Featured Post',
        slug: 'test-featured-post-' + Date.now(),
        content: 'This is a test featured post content.',
        excerpt: 'Test excerpt for featured post',
        featured: true,
        status: 'PUBLISHED',
        publishedAt: new Date(),
        authorId: posts[0]?.id ? (await prisma.user.findFirst())?.id : 'default-user-id'
      }
    })
    console.log(`✅ Created test featured post: ${testPost.title} (featured=${testPost.featured})`)
    console.log()

    // 3. 测试Featured文章查询
    console.log('3. Testing featured post queries...')
    const featuredPosts = await prisma.post.findMany({
      where: {
        featured: true,
        status: 'PUBLISHED'
      },
      select: {
        id: true,
        title: true,
        featured: true,
        publishedAt: true
      },
      orderBy: {
        publishedAt: 'desc'
      }
    })
    
    console.log(`✅ Found ${featuredPosts.length} featured posts:`)
    featuredPosts.forEach(post => {
      console.log(`   - ${post.title} (${post.publishedAt?.toISOString()})`)
    })
    console.log()

    // 4. 测试更新Featured状态
    console.log('4. Testing featured status update...')
    const updatedPost = await prisma.post.update({
      where: { id: testPost.id },
      data: { featured: false }
    })
    console.log(`✅ Updated post featured status: ${updatedPost.title} (featured=${updatedPost.featured})`)
    console.log()

    // 5. 测试API端点
    console.log('5. Testing API endpoints...')
    
    // 测试获取文章列表
    const response = await fetch('http://localhost:3000/api/posts?status=PUBLISHED&limit=5')
    const result = await response.json()
    
    if (result.success && result.data) {
      console.log(`✅ API posts endpoint working: ${result.data.length} posts returned`)
      const featuredInAPI = result.data.filter(post => post.featured)
      console.log(`   - Featured posts in API: ${featuredInAPI.length}`)
      featuredInAPI.forEach(post => {
        console.log(`     * ${post.title}`)
      })
    } else {
      console.log('❌ API posts endpoint failed')
    }
    console.log()

    // 6. 清理测试数据
    console.log('6. Cleaning up test data...')
    await prisma.post.delete({
      where: { id: testPost.id }
    })
    console.log('✅ Test post deleted')
    console.log()

    // 7. 验证前台逻辑
    console.log('7. Testing frontend logic simulation...')
    const allPosts = await prisma.post.findMany({
      where: { status: 'PUBLISHED' },
      select: {
        id: true,
        title: true,
        featured: true,
        publishedAt: true
      },
      orderBy: {
        publishedAt: 'desc'
      }
    })

    // 模拟前台选择Featured Post的逻辑
    const featuredPost = allPosts.find(post => post.featured) || allPosts[0]
    const otherPosts = featuredPost ? allPosts.filter(post => post.id !== featuredPost.id) : allPosts

    console.log(`✅ Frontend logic simulation:`)
    console.log(`   - Featured post: ${featuredPost?.title || 'None'}`)
    console.log(`   - Other posts: ${otherPosts.length}`)
    console.log()

    console.log('🎉 All Featured Blog tests passed!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error(error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testFeaturedBlog()
