// 测试搜索防抖功能的脚本
// 在浏览器控制台中运行此脚本

console.log('🔍 Testing Search Debounce Functionality...\n');

// 模拟快速输入的函数
function simulateTyping(inputElement, text, delay = 100) {
  return new Promise((resolve) => {
    let index = 0;
    const typeChar = () => {
      if (index < text.length) {
        inputElement.value = text.substring(0, index + 1);
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        index++;
        setTimeout(typeChar, delay);
      } else {
        resolve();
      }
    };
    typeChar();
  });
}

// 监控网络请求
let requestCount = 0;
const originalFetch = window.fetch;
window.fetch = function(...args) {
  if (args[0].includes('/api/products')) {
    requestCount++;
    console.log(`📡 API Request #${requestCount}: ${args[0]}`);
  }
  return originalFetch.apply(this, args);
};

async function testSearchDebounce() {
  try {
    // 1. 找到搜索输入框
    const searchInput = document.querySelector('input[placeholder*="Search"]');
    if (!searchInput) {
      console.error('❌ Search input not found');
      return;
    }

    console.log('✅ Found search input element');

    // 2. 清空输入框
    searchInput.value = '';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const initialRequestCount = requestCount;
    console.log(`📊 Initial request count: ${initialRequestCount}`);

    // 3. 模拟快速输入
    console.log('\n🚀 Simulating fast typing: "design"...');
    await simulateTyping(searchInput, 'design', 50); // 50ms between characters

    // 4. 等待防抖延迟
    console.log('⏳ Waiting for debounce delay (500ms)...');
    await new Promise(resolve => setTimeout(resolve, 600));

    const finalRequestCount = requestCount;
    const newRequests = finalRequestCount - initialRequestCount;

    console.log(`\n📊 Final request count: ${finalRequestCount}`);
    console.log(`📈 New requests made: ${newRequests}`);

    // 5. 分析结果
    if (newRequests <= 2) {
      console.log('✅ PASS: Debounce is working correctly!');
      console.log('   Expected: 1-2 requests (initial + final)');
      console.log(`   Actual: ${newRequests} requests`);
    } else {
      console.log('❌ FAIL: Too many requests, debounce may not be working');
      console.log('   Expected: 1-2 requests');
      console.log(`   Actual: ${newRequests} requests`);
    }

    // 6. 测试搜索结果
    console.log('\n🔍 Checking search results...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const productCards = document.querySelectorAll('[class*="product"]');
    console.log(`📦 Found ${productCards.length} product elements`);

    // 7. 测试清空搜索
    console.log('\n🧹 Testing search clear...');
    searchInput.value = '';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    await new Promise(resolve => setTimeout(resolve, 600));
    console.log('✅ Search cleared');

    console.log('\n🎉 Debounce test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// 运行测试
testSearchDebounce();

// 提供手动测试指导
console.log(`
📋 Manual Testing Guide:
1. Type quickly in the search box
2. Watch the network tab for API requests
3. You should see only 1-2 requests instead of one per character
4. The search should feel smooth without flickering
5. Results should appear after you stop typing

🔧 Expected Behavior:
- No API call while typing
- API call 500ms after stopping
- Smooth user experience
- No loading flicker during typing
`);
