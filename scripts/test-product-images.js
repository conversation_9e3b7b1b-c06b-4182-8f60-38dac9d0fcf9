// 使用Node.js 18+内置的fetch

const BASE_URL = 'http://localhost:3000';

async function testProductImagesAPI() {
  console.log('🧪 Testing Product Images API functionality...\n');

  try {
    // 1. 使用示例图片URL进行测试
    console.log('1. 📁 Using sample image for testing...');
    const testImage = {
      url: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop',
      filename: 'sample-product.jpg',
      mimeType: 'image/jpeg'
    };

    console.log(`📸 Using test image: ${testImage.filename}`);

    // 2. 测试创建带图片的产品
    console.log('\n2. 🆕 Testing product creation with images...');
    const productData = {
      name: 'Test Product with Images',
      description: 'A test product with multiple images',
      content: 'This is a detailed description of the test product with images.',
      price: 99.99,
      sku: 'TEST-IMG-001',
      status: 'ACTIVE',
      featured: true,
      images: [
        {
          url: testImage.url,
          alt: 'Main product image',
          caption: 'Primary view of the product',
          sortOrder: 0
        },
        {
          url: testImage.url,
          alt: 'Secondary product image',
          caption: 'Alternative view of the product',
          sortOrder: 1
        }
      ]
    };

    const createResponse = await fetch(`${BASE_URL}/api/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData)
    });

    const createResult = await createResponse.json();
    
    if (!createResult.success) {
      console.log('❌ Failed to create product:', createResult.error);
      return;
    }

    console.log('✅ Product created successfully');
    console.log(`📦 Product ID: ${createResult.data.id}`);
    console.log(`🖼️  Images count: ${createResult.data.images.length}`);
    
    const productId = createResult.data.id;

    // 3. 测试获取产品详情（包含图片）
    console.log('\n3. 📋 Testing product details with images...');
    const detailResponse = await fetch(`${BASE_URL}/api/products/${productId}`);
    const detailResult = await detailResponse.json();
    
    if (!detailResult.success) {
      console.log('❌ Failed to get product details:', detailResult.error);
      return;
    }

    console.log('✅ Product details retrieved successfully');
    console.log(`🖼️  Images in product: ${detailResult.data.images.length}`);
    
    detailResult.data.images.forEach((img, index) => {
      console.log(`   Image ${index + 1}: ${img.alt} (Sort: ${img.sortOrder})`);
    });

    // 4. 测试更新产品图片
    console.log('\n4. 🔄 Testing product image update...');
    const updateData = {
      ...productData,
      name: 'Updated Test Product with Images',
      images: [
        {
          url: testImage.url,
          alt: 'Updated main image',
          caption: 'Updated primary view',
          sortOrder: 0
        },
        {
          url: testImage.url,
          alt: 'New additional image',
          caption: 'New view of the product',
          sortOrder: 1
        },
        {
          url: testImage.url,
          alt: 'Third image',
          caption: 'Another angle',
          sortOrder: 2
        }
      ]
    };

    const updateResponse = await fetch(`${BASE_URL}/api/products/${productId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });

    const updateResult = await updateResponse.json();
    
    if (!updateResult.success) {
      console.log('❌ Failed to update product:', updateResult.error);
      return;
    }

    console.log('✅ Product updated successfully');
    console.log(`🖼️  Updated images count: ${updateResult.data.images.length}`);
    
    updateResult.data.images.forEach((img, index) => {
      console.log(`   Image ${index + 1}: ${img.alt} (Sort: ${img.sortOrder})`);
    });

    // 5. 测试产品列表（包含图片）
    console.log('\n5. 📜 Testing products list with images...');
    const listResponse = await fetch(`${BASE_URL}/api/products?page=1&limit=5`);
    const listResult = await listResponse.json();
    
    if (!listResult.success) {
      console.log('❌ Failed to get products list:', listResult.error);
      return;
    }

    console.log('✅ Products list retrieved successfully');
    const productsWithImages = listResult.data.filter(p => p.images && p.images.length > 0);
    console.log(`🖼️  Products with images: ${productsWithImages.length}/${listResult.data.length}`);

    // 6. 清理测试数据
    console.log('\n6. 🧹 Cleaning up test data...');
    const deleteResponse = await fetch(`${BASE_URL}/api/products/${productId}`, {
      method: 'DELETE'
    });

    const deleteResult = await deleteResponse.json();
    
    if (!deleteResult.success) {
      console.log('❌ Failed to delete test product:', deleteResult.error);
      return;
    }

    console.log('✅ Test product deleted successfully');

    console.log('\n🎉 All Product Images API tests passed!');
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Media files retrieval');
    console.log('   ✅ Product creation with images');
    console.log('   ✅ Product details with images');
    console.log('   ✅ Product image updates');
    console.log('   ✅ Products list with images');
    console.log('   ✅ Test data cleanup');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// 运行测试
testProductImagesAPI();
