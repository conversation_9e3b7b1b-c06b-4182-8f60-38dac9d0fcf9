const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding media files...')

  // Create uploads directory if it doesn't exist
  const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
    console.log('✓ Created uploads directory')
  }

  // Get the first user (admin) to assign as author
  const admin = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  })

  if (!admin) {
    console.log('❌ No admin user found. Please create an admin user first.')
    return
  }

  // Sample media files data
  const mediaFiles = [
    {
      filename: 'sample-image-1.jpg',
      originalName: 'beautiful-landscape.jpg',
      mimeType: 'image/jpeg',
      size: 1024000,
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      alt: 'Beautiful mountain landscape',
      caption: 'A stunning view of mountains during sunset'
    },
    {
      filename: 'sample-image-2.jpg',
      originalName: 'modern-office.jpg',
      mimeType: 'image/jpeg',
      size: 856000,
      url: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop',
      alt: 'Modern office workspace',
      caption: 'Clean and modern office environment'
    },
    {
      filename: 'sample-image-3.jpg',
      originalName: 'technology-concept.jpg',
      mimeType: 'image/jpeg',
      size: 945000,
      url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
      alt: 'Technology and innovation concept',
      caption: 'Digital technology and innovation'
    },
    {
      filename: 'sample-image-4.jpg',
      originalName: 'team-collaboration.jpg',
      mimeType: 'image/jpeg',
      size: 1156000,
      url: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=600&fit=crop',
      alt: 'Team collaboration and teamwork',
      caption: 'People working together as a team'
    },
    {
      filename: 'sample-image-5.jpg',
      originalName: 'business-growth.jpg',
      mimeType: 'image/jpeg',
      size: 789000,
      url: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
      alt: 'Business growth and analytics',
      caption: 'Charts and graphs showing business growth'
    },
    {
      filename: 'sample-image-6.jpg',
      originalName: 'creative-workspace.jpg',
      mimeType: 'image/jpeg',
      size: 1234000,
      url: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=800&h=600&fit=crop',
      alt: 'Creative workspace with design tools',
      caption: 'Designer workspace with creative tools'
    },
    {
      filename: 'sample-doc-1.pdf',
      originalName: 'company-brochure.pdf',
      mimeType: 'application/pdf',
      size: 2048000,
      url: '/uploads/sample-doc-1.pdf',
      alt: 'Company brochure document',
      caption: 'Official company brochure and information'
    },
    {
      filename: 'sample-doc-2.docx',
      originalName: 'product-specifications.docx',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: 512000,
      url: '/uploads/sample-doc-2.docx',
      alt: 'Product specifications document',
      caption: 'Detailed product specifications and features'
    }
  ]

  // Create media records
  for (const mediaData of mediaFiles) {
    try {
      // Check if media already exists by URL
      const existing = await prisma.media.findFirst({
        where: { url: mediaData.url }
      })

      if (existing) {
        console.log(`⚠ Media already exists: ${mediaData.originalName}`)
        continue
      }

      await prisma.media.create({
        data: mediaData
      })
      console.log(`✓ Created media: ${mediaData.originalName}`)
    } catch (error) {
      console.log(`✗ Failed to create media ${mediaData.originalName}:`, error.message)
    }
  }

  console.log('Media seeding completed!')
  console.log('\n📁 Sample media files have been added to the database.')
  console.log('🖼️ You can now test the media selector in the post creation/editing pages.')
  console.log('\n💡 Note: The image URLs use Unsplash for demonstration purposes.')
  console.log('   In production, you would upload actual files to your server.')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
