// 使用Node.js 18+内置的fetch

const BASE_URL = 'http://localhost:3000';

async function testProductsSearch() {
  console.log('🧪 Testing Products Search API...\n');

  try {
    // 1. 测试登录获取token
    console.log('1. 🔐 Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('❌ Login failed:', loginResult.error);
      return;
    }

    console.log('✅ Login successful');
    
    // 获取cookie
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('🍪 Cookies:', cookies);

    // 2. 测试产品列表API（无搜索）
    console.log('\n2. 📋 Testing products list...');
    const listResponse = await fetch(`${BASE_URL}/api/products`, {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const listResult = await listResponse.json();
    
    if (!listResult.success) {
      console.log('❌ Products list failed:', listResult.error);
      return;
    }

    console.log('✅ Products list successful');
    console.log(`📦 Found ${listResult.data.length} products`);

    // 3. 测试产品搜索API
    console.log('\n3. 🔍 Testing products search...');
    const searchResponse = await fetch(`${BASE_URL}/api/products?search=design`, {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const searchResult = await searchResponse.json();
    
    if (!searchResult.success) {
      console.log('❌ Products search failed:', searchResult.error);
      console.log('Response status:', searchResponse.status);
      return;
    }

    console.log('✅ Products search successful');
    console.log(`🔍 Found ${searchResult.data.length} products matching "design"`);

    // 4. 测试不同搜索词
    console.log('\n4. 🔍 Testing different search terms...');
    const searchTerms = ['workspace', 'test', 'nonexistent'];
    
    for (const term of searchTerms) {
      const response = await fetch(`${BASE_URL}/api/products?search=${term}`, {
        headers: {
          'Cookie': cookies || ''
        }
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ Search "${term}": ${result.data.length} results`);
      } else {
        console.log(`❌ Search "${term}" failed:`, result.error);
      }
    }

    // 5. 测试状态筛选
    console.log('\n5. 📊 Testing status filter...');
    const statusResponse = await fetch(`${BASE_URL}/api/products?status=ACTIVE`, {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const statusResult = await statusResponse.json();
    
    if (statusResult.success) {
      console.log(`✅ Status filter: ${statusResult.data.length} active products`);
    } else {
      console.log('❌ Status filter failed:', statusResult.error);
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// 运行测试
testProductsSearch();
