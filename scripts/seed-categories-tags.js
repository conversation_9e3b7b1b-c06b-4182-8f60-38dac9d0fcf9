const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding categories and tags...')

  // Create categories
  const categories = [
    {
      name: 'Technology',
      slug: 'technology',
      description: 'Latest technology news and trends',
      color: '#3B82F6'
    },
    {
      name: 'Business',
      slug: 'business',
      description: 'Business insights and strategies',
      color: '#10B981'
    },
    {
      name: 'Lifestyle',
      slug: 'lifestyle',
      description: 'Lifestyle tips and advice',
      color: '#F59E0B'
    },
    {
      name: 'Health',
      slug: 'health',
      description: 'Health and wellness articles',
      color: '#EF4444'
    }
  ]

  for (const category of categories) {
    try {
      await prisma.category.upsert({
        where: { slug: category.slug },
        update: category,
        create: category
      })
      console.log(`✓ Created/updated category: ${category.name}`)
    } catch (error) {
      console.log(`✗ Failed to create category ${category.name}:`, error.message)
    }
  }

  // Create tags
  const tags = [
    { name: 'JavaScript', slug: 'javascript', color: '#F7DF1E' },
    { name: 'React', slug: 'react', color: '#61DAFB' },
    { name: 'Next.js', slug: 'nextjs', color: '#000000' },
    { name: 'AI', slug: 'ai', color: '#FF6B6B' },
    { name: 'Machine Learning', slug: 'machine-learning', color: '#4ECDC4' },
    { name: 'Web Development', slug: 'web-development', color: '#45B7D1' },
    { name: 'Mobile', slug: 'mobile', color: '#96CEB4' },
    { name: 'Design', slug: 'design', color: '#FFEAA7' },
    { name: 'Marketing', slug: 'marketing', color: '#DDA0DD' },
    { name: 'Productivity', slug: 'productivity', color: '#98D8C8' }
  ]

  for (const tag of tags) {
    try {
      await prisma.tag.upsert({
        where: { slug: tag.slug },
        update: tag,
        create: tag
      })
      console.log(`✓ Created/updated tag: ${tag.name}`)
    } catch (error) {
      console.log(`✗ Failed to create tag ${tag.name}:`, error.message)
    }
  }

  console.log('Seeding completed!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
