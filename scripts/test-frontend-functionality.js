// 前端功能测试脚本
// 在浏览器控制台中运行此脚本来测试Product Images功能

console.log('🧪 Testing Product Images Frontend Functionality...\n');

// 测试1: 检查页面元素是否存在
function testPageElements() {
  console.log('1. 🔍 Checking page elements...');
  
  // 检查Product Images区域是否在第一位
  const firstSection = document.querySelector('form .bg-white');
  if (firstSection) {
    const title = firstSection.querySelector('h3');
    if (title && title.textContent.includes('Product Images')) {
      console.log('✅ Product Images section is in first position');
    } else {
      console.log('❌ Product Images section not found in first position');
      console.log('First section title:', title ? title.textContent : 'No title found');
    }
  } else {
    console.log('❌ No form sections found');
  }
  
  // 检查Add Image按钮
  const addImageBtn = document.querySelector('button[type="button"]');
  if (addImageBtn && addImageBtn.textContent.includes('Add Image')) {
    console.log('✅ Add Image button found');
  } else {
    console.log('❌ Add Image button not found');
  }
  
  // 检查空状态提示
  const emptyState = document.querySelector('.border-dashed');
  if (emptyState) {
    console.log('✅ Empty state container found');
  } else {
    console.log('❌ Empty state container not found');
  }
}

// 测试2: 模拟点击Add Image按钮
function testAddImageButton() {
  console.log('\n2. 🖱️ Testing Add Image button...');
  
  const addImageBtn = Array.from(document.querySelectorAll('button')).find(btn => 
    btn.textContent.includes('Add Image')
  );
  
  if (addImageBtn) {
    console.log('✅ Found Add Image button, attempting to click...');
    addImageBtn.click();
    
    // 检查是否打开了媒体选择器
    setTimeout(() => {
      const modal = document.querySelector('.fixed.inset-0');
      if (modal) {
        console.log('✅ Media selector modal opened');
        
        // 检查模态框内容
        const modalTitle = modal.querySelector('h2');
        if (modalTitle && modalTitle.textContent.includes('Select')) {
          console.log('✅ Modal has correct title');
        }
        
        // 关闭模态框
        const closeBtn = modal.querySelector('button');
        if (closeBtn) {
          closeBtn.click();
          console.log('✅ Modal closed');
        }
      } else {
        console.log('❌ Media selector modal did not open');
      }
    }, 1000);
  } else {
    console.log('❌ Add Image button not found');
  }
}

// 测试3: 检查表单字段
function testFormFields() {
  console.log('\n3. 📝 Checking form fields...');
  
  const requiredFields = ['name', 'description', 'content'];
  let allFieldsFound = true;
  
  requiredFields.forEach(fieldName => {
    const field = document.querySelector(`[name="${fieldName}"]`);
    if (field) {
      console.log(`✅ ${fieldName} field found`);
    } else {
      console.log(`❌ ${fieldName} field not found`);
      allFieldsFound = false;
    }
  });
  
  if (allFieldsFound) {
    console.log('✅ All required form fields present');
  }
}

// 测试4: 检查布局顺序
function testLayoutOrder() {
  console.log('\n4. 📐 Checking layout order...');
  
  const sections = document.querySelectorAll('form > div.bg-white');
  if (sections.length >= 3) {
    const titles = Array.from(sections).map(section => {
      const h2 = section.querySelector('h2');
      const h3 = section.querySelector('h3');
      return h2 ? h2.textContent : (h3 ? h3.textContent : 'Unknown');
    });
    
    console.log('Section order:', titles);
    
    if (titles[0].includes('Product Images')) {
      console.log('✅ Product Images is first section');
    } else {
      console.log('❌ Product Images is not first section');
    }
    
    if (titles[1].includes('Basic Information')) {
      console.log('✅ Basic Information is second section');
    } else {
      console.log('❌ Basic Information is not second section');
    }
  } else {
    console.log('❌ Not enough sections found');
  }
}

// 运行所有测试
function runAllTests() {
  testPageElements();
  testFormFields();
  testLayoutOrder();
  
  // 延迟执行需要交互的测试
  setTimeout(() => {
    testAddImageButton();
  }, 500);
  
  console.log('\n🎯 Frontend tests completed!');
  console.log('\n💡 To manually test:');
  console.log('   1. Click "Add Image" button');
  console.log('   2. Select an image from the media library');
  console.log('   3. Fill in alt text and caption');
  console.log('   4. Add multiple images and test reordering');
  console.log('   5. Fill in product details and submit');
}

// 自动运行测试
if (typeof window !== 'undefined') {
  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    runAllTests();
  }
} else {
  console.log('This script should be run in a browser environment');
}
