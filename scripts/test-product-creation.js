// 新增产品功能完整测试脚本
// 在浏览器控制台中运行此脚本

console.log('🧪 开始新增产品功能测试...\n');

// 测试配置
const TEST_CONFIG = {
  testProductName: 'Test Product ' + Date.now(),
  testDescription: 'This is a test product created by automated testing script',
  testContent: 'Detailed description of the test product with features and specifications.',
  testPrice: 99.99,
  testSku: 'TEST-' + Date.now()
};

// 工具函数：等待元素出现
function waitForElement(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// 工具函数：模拟用户输入
function simulateInput(element, value) {
  element.focus();
  element.value = value;
  element.dispatchEvent(new Event('input', { bubbles: true }));
  element.dispatchEvent(new Event('change', { bubbles: true }));
}

// 测试1: 检查页面布局和元素
async function testPageLayout() {
  console.log('1. 🔍 检查页面布局和元素...');
  
  try {
    // 检查页面标题
    const pageTitle = document.querySelector('h1');
    if (pageTitle && pageTitle.textContent.includes('Create New Product')) {
      console.log('✅ 页面标题正确');
    } else {
      console.log('❌ 页面标题不正确');
    }

    // 检查表单存在
    const form = document.querySelector('form');
    if (form) {
      console.log('✅ 表单元素存在');
    } else {
      console.log('❌ 表单元素不存在');
      return false;
    }

    // 检查Product Images区域是否在第一位
    const firstSection = document.querySelector('form > div.bg-white');
    if (firstSection) {
      const hasImageSelector = firstSection.querySelector('[class*="MultiImageSelector"]') || 
                              firstSection.textContent.includes('Product Images') ||
                              firstSection.querySelector('button[type="button"]');
      if (hasImageSelector) {
        console.log('✅ Product Images区域位于第一位');
      } else {
        console.log('❌ Product Images区域不在第一位');
      }
    }

    // 检查必填字段
    const requiredFields = ['name', 'description', 'content'];
    let allFieldsPresent = true;
    
    requiredFields.forEach(fieldName => {
      const field = document.querySelector(`[name="${fieldName}"]`);
      if (field) {
        console.log(`✅ ${fieldName} 字段存在`);
      } else {
        console.log(`❌ ${fieldName} 字段不存在`);
        allFieldsPresent = false;
      }
    });

    return allFieldsPresent;
  } catch (error) {
    console.log('❌ 页面布局检查失败:', error.message);
    return false;
  }
}

// 测试2: 测试Product Images功能
async function testProductImages() {
  console.log('\n2. 📸 测试Product Images功能...');
  
  try {
    // 查找Add Image按钮
    const addImageBtn = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent.includes('Add Image')
    );
    
    if (!addImageBtn) {
      console.log('❌ Add Image按钮不存在');
      return false;
    }
    
    console.log('✅ 找到Add Image按钮');
    
    // 点击按钮打开媒体选择器
    addImageBtn.click();
    console.log('🖱️ 点击Add Image按钮');
    
    // 等待模态框出现
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
      console.log('✅ 媒体选择器模态框已打开');
      
      // 关闭模态框
      const closeBtn = modal.querySelector('button') || 
                      Array.from(modal.querySelectorAll('button')).find(btn => 
                        btn.textContent.includes('×') || btn.textContent.includes('Close')
                      );
      
      if (closeBtn) {
        closeBtn.click();
        console.log('✅ 模态框已关闭');
      }
    } else {
      console.log('❌ 媒体选择器模态框未打开');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Product Images测试失败:', error.message);
    return false;
  }
}

// 测试3: 填写产品信息
async function testProductForm() {
  console.log('\n3. 📝 测试产品表单填写...');
  
  try {
    // 填写产品名称
    const nameField = document.querySelector('[name="name"]');
    if (nameField) {
      simulateInput(nameField, TEST_CONFIG.testProductName);
      console.log('✅ 产品名称已填写');
      
      // 检查slug是否自动生成
      await new Promise(resolve => setTimeout(resolve, 500));
      const slugField = document.querySelector('[name="slug"]');
      if (slugField && slugField.value) {
        console.log('✅ URL Slug自动生成');
      }
    } else {
      console.log('❌ 产品名称字段不存在');
      return false;
    }

    // 填写描述
    const descField = document.querySelector('[name="description"]');
    if (descField) {
      simulateInput(descField, TEST_CONFIG.testDescription);
      console.log('✅ 产品描述已填写');
    }

    // 填写详细内容
    const contentField = document.querySelector('[name="content"]');
    if (contentField) {
      simulateInput(contentField, TEST_CONFIG.testContent);
      console.log('✅ 产品详细内容已填写');
    }

    // 填写价格
    const priceField = document.querySelector('[name="price"]');
    if (priceField) {
      simulateInput(priceField, TEST_CONFIG.testPrice.toString());
      console.log('✅ 产品价格已填写');
    }

    // 填写SKU
    const skuField = document.querySelector('[name="sku"]');
    if (skuField) {
      simulateInput(skuField, TEST_CONFIG.testSku);
      console.log('✅ 产品SKU已填写');
    }

    // 设置为特色产品
    const featuredCheckbox = document.querySelector('[name="featured"]');
    if (featuredCheckbox && !featuredCheckbox.checked) {
      featuredCheckbox.click();
      console.log('✅ 设置为特色产品');
    }

    return true;
  } catch (error) {
    console.log('❌ 表单填写失败:', error.message);
    return false;
  }
}

// 测试4: 验证表单验证
async function testFormValidation() {
  console.log('\n4. ✅ 测试表单验证...');
  
  try {
    // 检查必填字段是否有值
    const requiredFields = [
      { name: 'name', label: '产品名称' },
      { name: 'description', label: '产品描述' },
      { name: 'content', label: '详细内容' }
    ];

    let allValid = true;
    
    requiredFields.forEach(field => {
      const element = document.querySelector(`[name="${field.name}"]`);
      if (element && element.value.trim()) {
        console.log(`✅ ${field.label}已填写`);
      } else {
        console.log(`❌ ${field.label}未填写`);
        allValid = false;
      }
    });

    // 检查提交按钮状态
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
      console.log('✅ 提交按钮存在');
      if (!submitBtn.disabled) {
        console.log('✅ 提交按钮可用');
      } else {
        console.log('⚠️ 提交按钮被禁用');
      }
    }

    return allValid;
  } catch (error) {
    console.log('❌ 表单验证测试失败:', error.message);
    return false;
  }
}

// 测试5: 模拟表单提交（不实际提交）
async function testFormSubmission() {
  console.log('\n5. 🚀 测试表单提交准备...');
  
  try {
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');
    
    if (!form || !submitBtn) {
      console.log('❌ 表单或提交按钮不存在');
      return false;
    }

    // 检查表单数据
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    console.log('📋 表单数据预览:');
    console.log('   产品名称:', data.name || '未填写');
    console.log('   URL Slug:', data.slug || '未生成');
    console.log('   描述:', data.description ? '已填写' : '未填写');
    console.log('   内容:', data.content ? '已填写' : '未填写');
    console.log('   价格:', data.price || '未填写');
    console.log('   SKU:', data.sku || '未填写');
    console.log('   状态:', data.status || 'ACTIVE');
    console.log('   特色产品:', data.featured ? '是' : '否');

    console.log('✅ 表单数据准备完成');
    console.log('💡 注意: 实际提交需要用户手动点击提交按钮');
    
    return true;
  } catch (error) {
    console.log('❌ 表单提交测试失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runProductCreationTest() {
  console.log('🎯 开始新增产品完整测试流程\n');
  
  const results = {
    pageLayout: false,
    productImages: false,
    formFilling: false,
    formValidation: false,
    formSubmission: false
  };

  try {
    // 等待页面加载完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行所有测试
    results.pageLayout = await testPageLayout();
    results.productImages = await testProductImages();
    results.formFilling = await testProductForm();
    results.formValidation = await testFormValidation();
    results.formSubmission = await testFormSubmission();
    
    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log('==================');
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ 通过' : '❌ 失败';
      const testName = {
        pageLayout: '页面布局检查',
        productImages: 'Product Images功能',
        formFilling: '表单填写功能',
        formValidation: '表单验证功能',
        formSubmission: '表单提交准备'
      }[test];
      console.log(`${testName}: ${status}`);
    });
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 项测试通过`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！新增产品功能正常工作');
      console.log('\n📝 下一步操作:');
      console.log('   1. 手动点击"Create Product"按钮提交表单');
      console.log('   2. 验证产品是否成功创建');
      console.log('   3. 检查产品列表页面');
    } else {
      console.log('⚠️ 部分测试失败，请检查相关功能');
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
  }
}

// 自动运行测试
if (typeof window !== 'undefined') {
  // 检查是否在正确的页面
  if (window.location.pathname.includes('/admin/products/new')) {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', runProductCreationTest);
    } else {
      runProductCreationTest();
    }
  } else {
    console.log('❌ 请在产品创建页面 (/admin/products/new) 运行此测试');
  }
} else {
  console.log('此脚本需要在浏览器环境中运行');
}
