const fetch = require('node-fetch')

async function testPostCreation() {
  console.log('Testing post creation API...')
  
  // First, let's get categories and tags
  try {
    const categoriesRes = await fetch('http://localhost:3000/api/categories')
    const categoriesData = await categoriesRes.json()
    console.log('Categories:', categoriesData.data?.length || 0)
    
    const tagsRes = await fetch('http://localhost:3000/api/tags')
    const tagsData = await tagsRes.json()
    console.log('Tags:', tagsData.data?.length || 0)
    
    if (categoriesData.data?.length > 0 && tagsData.data?.length > 0) {
      console.log('✓ Categories and tags are available')
      console.log('Sample category:', categoriesData.data[0].name)
      console.log('Sample tag:', tagsData.data[0].name)
    } else {
      console.log('⚠ No categories or tags found')
    }
    
  } catch (error) {
    console.error('Error testing APIs:', error.message)
  }
}

testPostCreation()
