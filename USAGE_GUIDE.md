# 企业官网CMS系统使用指南 | Enterprise CMS Usage Guide

## 🚪 登录和退出 | Login and Logout

### 如何登录管理后台 | How to Login to Admin Dashboard

1. **访问登录页面 | Access Login Page**
   ```
   http://localhost:3000/auth/login
   ```

2. **使用演示账号 | Use Demo Accounts**
   - **管理员账号 | Admin Account**
     - 邮箱 | Email: `<EMAIL>`
     - 密码 | Password: `admin123`
     - 权限 | Permissions: 完全访问权限 | Full access

   - **编辑员账号 | Editor Account**
     - 邮箱 | Email: `<EMAIL>`
     - 密码 | Password: `editor123`
     - 权限 | Permissions: 内容编辑权限 | Content editing

3. **登录成功后 | After Successful Login**
   - 自动跳转到管理后台 | Auto-redirect to admin dashboard
   - 右上角显示用户信息 | User info displayed in top-right corner
   - 显示红色"Logout"退出按钮 | Red "Logout" button appears

### 如何退出登录 | How to Logout

1. **找到退出按钮 | Find Logout Button**
   - 位置：管理后台页面右上角 | Location: Top-right corner of admin dashboard
   - 外观：红色背景，白色"Logout"文字 | Appearance: Red background, white "Logout" text

2. **点击退出 | Click Logout**
   - 点击"Logout"按钮 | Click the "Logout" button
   - 系统自动清除认证信息 | System automatically clears authentication
   - 自动跳转到登录页面 | Auto-redirect to login page

### 访问控制 | Access Control

- **未登录用户 | Unauthenticated Users**
  - 无法访问管理后台 | Cannot access admin dashboard
  - 显示"Access Denied"页面 | Shows "Access Denied" page
  - 提供"Go to Login"按钮 | Provides "Go to Login" button

- **已登录用户 | Authenticated Users**
  - 可以正常访问管理功能 | Can access admin functions normally
  - 根据角色显示不同权限 | Different permissions based on role

## 📊 管理后台功能 | Admin Dashboard Features

### 仪表板 | Dashboard (`/admin`)
- 系统概览和统计信息 | System overview and statistics
- 快速访问各个管理模块 | Quick access to management modules

### 内容管理 | Content Management

#### 文章管理 | Posts Management (`/admin/posts`)
- 创建、编辑、删除文章 | Create, edit, delete articles
- 文章分类和标签管理 | Article categories and tags
- 发布状态控制 | Publication status control
- SEO元数据设置 | SEO metadata settings

#### 分类管理 | Categories Management (`/admin/categories`)
- 创建和管理文章分类 | Create and manage article categories
- 层级分类支持 | Hierarchical category support
- 分类描述和SEO设置 | Category descriptions and SEO

#### 标签管理 | Tags Management (`/admin/tags`)
- 创建和管理标签 | Create and manage tags
- 标签使用统计 | Tag usage statistics
- 批量操作支持 | Batch operations support

### 产品管理 | Product Management

#### 产品管理 | Products Management (`/admin/products`)
- 产品信息管理 | Product information management
- 产品图片上传 | Product image upload
- 价格和库存管理 | Price and inventory management
- 产品SEO优化 | Product SEO optimization

#### 产品分类 | Product Categories (`/admin/product-categories`)
- 产品分类体系管理 | Product category system management
- 层级分类结构 | Hierarchical category structure
- 分类属性设置 | Category attribute settings

### 客户关系管理 | Customer Relations

#### 询盘管理 | Inquiries Management (`/admin/inquiries`)
- 查看客户询盘 | View customer inquiries
- 询盘状态跟踪 | Inquiry status tracking
- 客户信息管理 | Customer information management
- 询盘统计分析 | Inquiry statistics and analysis

#### 评论管理 | Comments Management (`/admin/comments`)
- 评论审核和管理 | Comment moderation and management
- 垃圾评论过滤 | Spam comment filtering
- 评论回复功能 | Comment reply functionality

### 用户管理 | User Management (`/admin/users`)
- 用户账号管理 | User account management
- 角色权限分配 | Role and permission assignment
- 用户状态控制 | User status control

### 媒体管理 | Media Management (`/admin/media`)
- 文件上传和管理 | File upload and management
- 图片优化和压缩 | Image optimization and compression
- 媒体库组织 | Media library organization

### 系统设置 | System Settings

#### 主题配置 | Theme Configuration (`/admin/themes`)
- 网站外观自定义 | Website appearance customization
- 颜色主题设置 | Color theme settings
- 字体和布局配置 | Font and layout configuration

#### 系统设置 | System Settings (`/admin/settings`)
- 网站基本信息 | Basic website information
- 邮件服务配置 | Email service configuration
- SEO设置 | SEO settings
- 社交媒体链接 | Social media links

#### 系统状态 | System Status (`/admin/system-status`)
- 系统健康检查 | System health checks
- API状态监控 | API status monitoring
- 性能指标显示 | Performance metrics display

## 🌐 前端公开页面 | Frontend Public Pages

### 首页 | Homepage (`/`)
- 企业介绍和展示 | Company introduction and showcase
- 最新文章和产品 | Latest articles and products
- 联系信息和导航 | Contact information and navigation

### 博客页面 | Blog Page (`/blog`)
- 文章列表展示 | Article list display
- 分类和标签筛选 | Category and tag filtering
- 文章搜索功能 | Article search functionality

### 产品页面 | Products Page (`/products`)
- 产品展示和分类 | Product display and categorization
- 产品详情查看 | Product detail viewing
- 产品搜索和筛选 | Product search and filtering

### 联系页面 | Contact Page (`/contact`)
- 联系表单 | Contact form
- 公司联系信息 | Company contact information
- 地图和位置信息 | Map and location information

## 🔧 常见问题解决 | Troubleshooting

### 登录问题 | Login Issues

**问题：无法登录管理后台**
- 检查用户名和密码是否正确 | Check username and password
- 确认账号是否已激活 | Confirm account is activated
- 清除浏览器缓存和Cookie | Clear browser cache and cookies

**问题：登录后立即退出**
- 检查JWT_SECRET环境变量 | Check JWT_SECRET environment variable
- 确认数据库连接正常 | Confirm database connection
- 检查服务器时间设置 | Check server time settings

### 权限问题 | Permission Issues

**问题：无法访问某些管理功能**
- 确认用户角色权限 | Confirm user role permissions
- 检查API权限设置 | Check API permission settings
- 联系管理员分配权限 | Contact admin for permission assignment

### 性能问题 | Performance Issues

**问题：页面加载缓慢**
- 检查数据库查询性能 | Check database query performance
- 优化图片大小和格式 | Optimize image size and format
- 启用缓存机制 | Enable caching mechanisms

## 📱 移动端使用 | Mobile Usage

### 响应式设计 | Responsive Design
- 支持手机和平板访问 | Supports mobile and tablet access
- 自适应屏幕尺寸 | Adaptive screen sizes
- 触摸友好的界面 | Touch-friendly interface

### 移动端管理 | Mobile Management
- 管理后台支持移动端访问 | Admin dashboard supports mobile access
- 简化的移动端界面 | Simplified mobile interface
- 核心功能完整支持 | Full support for core functions

## 🔒 安全最佳实践 | Security Best Practices

### 密码安全 | Password Security
- 使用强密码 | Use strong passwords
- 定期更换密码 | Change passwords regularly
- 启用双因素认证（如果可用）| Enable 2FA if available

### 数据安全 | Data Security
- 定期备份数据 | Regular data backups
- 限制管理员权限 | Limit admin permissions
- 监控异常登录活动 | Monitor unusual login activity

### 系统安全 | System Security
- 保持系统更新 | Keep system updated
- 使用HTTPS连接 | Use HTTPS connections
- 定期安全审计 | Regular security audits

---

**需要更多帮助？| Need More Help?**
- 查看完整文档：README.md | Check full documentation: README.md
- 提交问题：GitHub Issues | Submit issues: GitHub Issues
- 技术支持：联系开发团队 | Technical support: Contact development team
