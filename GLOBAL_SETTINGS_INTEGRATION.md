# 全局设置集成功能

## 🎯 问题解决

解决了之前硬编码"Enterprise CMS"的问题，现在所有页面的标题和SEO信息都使用`/admin/settings`中的全局设置，实现了真正的全局配置管理。

## 📋 实现内容

### 1. 全局设置Hook
创建了`useGlobalSettings.ts` hook，提供统一的全局设置访问接口：

```typescript
// app/hooks/useGlobalSettings.ts
export function useGlobalSettings() {
  const getSiteName = () => getSetting('site_name', 'InstAI CMS')
  const getDefaultMetaTitle = () => getSetting('default_meta_title', 'InstAI CMS - Professional Content Management System')
  const generatePageTitle = (pageTitle?: string) => {
    const siteName = getSiteName()
    if (!pageTitle) return getDefaultMetaTitle()
    return `${pageTitle} | ${siteName}`
  }
  // ...
}
```

### 2. 公开设置API
创建了`/api/settings/public`端点，允许前端无需认证获取基本站点设置：

```typescript
// app/api/settings/public/route.ts
const PUBLIC_SETTINGS_KEYS = [
  'site_name',
  'site_url', 
  'site_description',
  'default_meta_title',
  'default_meta_description',
  'default_meta_keywords',
  'og_image',
  'twitter_handle',
  'facebook_url',
  'twitter_url',
  'linkedin_url',
  'instagram_url'
]
```

### 3. 页面Metadata更新
更新了所有页面的metadata生成，使用全局设置而非硬编码：

#### 首页 (`/`)
```typescript
export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const title = globalSettings.default_meta_title?.value || 'InstAI CMS - Professional Content Management System'
  const description = globalSettings.default_meta_description?.value || 'InstAI CMS is a powerful, flexible content management system...'
  
  return { title, description, openGraph: { title, description, type: 'website' } }
}
```

#### 产品页面 (`/products`)
```typescript
export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  return {
    title: `Products | ${siteName}`,
    description: 'Browse our complete product catalog with detailed specifications and features.',
    openGraph: {
      title: `Products | ${siteName}`,
      description: 'Browse our complete product catalog with detailed specifications and features.',
      type: 'website',
    },
  }
}
```

#### 产品详情页面 (`/products/[category]/[slug]`)
```typescript
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const [product, globalSettings] = await Promise.all([
    getProduct(slug),
    getGlobalSettings()
  ])
  
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  return {
    title: product.seoTitle || `${product.name} | ${siteName}`,
    description: product.seoDescription || product.excerpt || product.description,
    keywords: product.seoKeywords,
    openGraph: {
      title: product.ogTitle || product.name,
      description: product.ogDescription || product.excerpt || product.description,
      type: 'website',
      images: product.ogImage ? [{ url: product.ogImage }] : (product.images?.length > 0 ? [{ url: product.images[0].url }] : []),
    },
  }
}
```

#### 产品类别页面 (`/products/category/[slug]`)
```typescript
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const [category, globalSettings] = await Promise.all([
    getCategory(slug),
    getGlobalSettings()
  ])
  
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  return {
    title: `${category.name} | Products | ${siteName}`,
    description: category.seoDescription || category.description || `Browse ${category.name} products in our catalog.`,
    keywords: category.seoKeywords,
    openGraph: {
      title: category.ogTitle || `${category.name} | Products`,
      description: category.ogDescription || category.description,
      type: 'website',
      images: category.image ? [{ url: category.image }] : [],
    },
  }
}
```

#### 热门产品页面 (`/products/featured`)
```typescript
export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  return {
    title: `Hot Products | ${siteName}`,
    description: 'Discover our most popular and featured products with exclusive deals and premium features.',
    openGraph: {
      title: `Hot Products | ${siteName}`,
      description: 'Discover our most popular and featured products with exclusive deals and premium features.',
      type: 'website',
    },
  }
}
```

## 🔧 技术实现

### 全局设置数据结构
```typescript
interface GlobalSettings {
  site_name?: { value: string; type: string }
  site_url?: { value: string; type: string }
  site_description?: { value: string; type: string }
  admin_email?: { value: string; type: string }
  default_meta_title?: { value: string; type: string }
  default_meta_description?: { value: string; type: string }
  default_meta_keywords?: { value: string; type: string }
  og_image?: { value: string; type: string }
  twitter_handle?: { value: string; type: string }
  facebook_url?: { value: string; type: string }
  twitter_url?: { value: string; type: string }
  linkedin_url?: { value: string; type: string }
  instagram_url?: { value: string; type: string }
}
```

### 设置获取函数
```typescript
// 获取全局设置（服务端）
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}
```

### 智能回退机制
```typescript
// 默认设置确保系统始终有可用的配置
const DEFAULT_SETTINGS: GlobalSettings = {
  site_name: { value: 'InstAI CMS', type: 'string' },
  site_url: { value: 'https://instai-cms.com', type: 'string' },
  site_description: { value: 'Professional Content Management System', type: 'string' },
  default_meta_title: { value: 'InstAI CMS - Professional Content Management System', type: 'string' },
  default_meta_description: { value: 'A powerful, flexible content management system that helps businesses create, manage, and scale their digital presence with ease.', type: 'string' },
}
```

## 🎨 管理界面

### 设置页面 (`/admin/settings`)
管理员可以在设置页面配置以下全局信息：

```
┌─────────────────────────────────────────────────────┐
│ Site Settings                                       │
├─────────────────────────────────────────────────────┤
│ Site Name: [Your Company Name                 ]    │
│ Site URL:  [https://yourcompany.com           ]    │
│ Site Description: [Professional CMS Platform  ]    │
│                                                     │
│ SEO Settings                                        │
│ Default Meta Title: [Your Company - Professional...] │
│ Default Meta Description: [Powerful CMS solution...] │
│ Default Meta Keywords: [cms, content management...] │
│                                                     │
│ Social Media                                        │
│ OG Image: [https://yourcompany.com/og-image.jpg]   │
│ Twitter Handle: [@yourcompany                 ]    │
│ Facebook URL: [https://facebook.com/yourcompany]   │
│ Twitter URL: [https://twitter.com/yourcompany ]    │
│ LinkedIn URL: [https://linkedin.com/company/...  ] │
│ Instagram URL: [https://instagram.com/yourcompany] │
└─────────────────────────────────────────────────────┘
```

## 🌟 功能特性

### 1. **统一配置管理**
- 所有页面标题使用全局设置
- 统一的品牌名称管理
- 集中的SEO配置

### 2. **智能回退机制**
- API失败时使用默认设置
- 确保页面始终有合适的标题
- 渐进式增强体验

### 3. **性能优化**
- 服务端获取设置，避免客户端闪烁
- 适当的缓存策略
- 最小化API调用

### 4. **安全性**
- 公开API只暴露必要的设置
- 敏感信息保持私有
- 无需认证的基础设置访问

## 🚀 使用指南

### 管理员操作
1. **访问设置页面**：`/admin/settings`
2. **配置站点信息**：
   - 设置站点名称（如"Your Company Name"）
   - 设置站点URL和描述
   - 配置默认SEO信息
3. **配置社交媒体**：
   - 设置各平台的URL
   - 配置Open Graph图片
4. **保存设置**：点击"Save Settings"
5. **验证效果**：在前台页面查看标题变化

### 开发者集成
```typescript
// 在新页面中使用全局设置
export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  return {
    title: `Page Title | ${siteName}`,
    description: 'Page description...',
  }
}
```

## 📊 更新页面列表

### ✅ 已更新的页面
- **首页** (`/`) - 使用default_meta_title和default_meta_description
- **产品页面** (`/products`) - 使用site_name
- **产品详情** (`/products/[category]/[slug]`) - 使用site_name
- **产品类别** (`/products/category/[slug]`) - 使用site_name
- **热门产品** (`/products/featured`) - 使用site_name

### 🔄 待更新的页面
- **博客页面** (`/blog`) - 当前为客户端组件，需要添加metadata
- **博客详情** (`/blog/[slug]`) - 需要检查是否使用全局设置
- **关于页面** (`/about`) - 如果存在，需要更新
- **联系页面** (`/contact`) - 如果存在，需要更新

## 🌟 核心优势

### ✅ **品牌一致性**
- 统一的站点名称管理
- 一处修改，全站生效
- 避免硬编码问题

### ✅ **SEO优化**
- 集中的SEO配置管理
- 智能的标题生成
- 完整的Open Graph支持

### ✅ **维护便利**
- 管理员可视化配置
- 无需修改代码
- 即时生效

### ✅ **扩展性**
- 易于添加新的全局设置
- 标准化的设置访问接口
- 支持不同类型的设置值

现在系统真正实现了全局配置管理！管理员在`/admin/settings`中设置的站点名称和SEO信息会自动应用到所有页面，不再有硬编码的"Enterprise CMS"问题。🎉
