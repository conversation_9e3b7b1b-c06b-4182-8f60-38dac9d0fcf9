import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      name: 'Admin User',
      password: hashedPassword,
      role: 'ADMIN',
      bio: 'System administrator',
    },
  })

  console.log('✅ Created admin user:', adminUser.email)

  // Create editor user
  const editorPassword = await bcrypt.hash('editor123', 12)
  
  const editorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'editor',
      name: 'Editor User',
      password: editorPassword,
      role: 'EDITOR',
      bio: 'Content editor',
    },
  })

  console.log('✅ Created editor user:', editorUser.email)

  // Create categories
  const categories = [
    {
      name: 'Technology',
      slug: 'technology',
      description: 'Latest technology trends and news',
      color: '#3B82F6',
    },
    {
      name: 'Development',
      slug: 'development',
      description: 'Web development tutorials and tips',
      color: '#10B981',
    },
    {
      name: 'Programming',
      slug: 'programming',
      description: 'Programming languages and best practices',
      color: '#8B5CF6',
    },
    {
      name: 'Design',
      slug: 'design',
      description: 'UI/UX design and creative inspiration',
      color: '#F59E0B',
    },
  ]

  const createdCategories = []
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdCategories.push(created)
    console.log('✅ Created category:', created.name)
  }

  // Create tags
  const tags = [
    { name: 'Next.js', slug: 'nextjs', color: '#000000' },
    { name: 'React', slug: 'react', color: '#61DAFB' },
    { name: 'TypeScript', slug: 'typescript', color: '#3178C6' },
    { name: 'JavaScript', slug: 'javascript', color: '#F7DF1E' },
    { name: 'CSS', slug: 'css', color: '#1572B6' },
    { name: 'HTML', slug: 'html', color: '#E34F26' },
    { name: 'Node.js', slug: 'nodejs', color: '#339933' },
    { name: 'Prisma', slug: 'prisma', color: '#2D3748' },
    { name: 'Tailwind CSS', slug: 'tailwindcss', color: '#06B6D4' },
    { name: 'API', slug: 'api', color: '#FF6B6B' },
  ]

  const createdTags = []
  for (const tag of tags) {
    const created = await prisma.tag.upsert({
      where: { slug: tag.slug },
      update: {},
      create: tag,
    })
    createdTags.push(created)
    console.log('✅ Created tag:', created.name)
  }

  // Create sample posts
  const posts = [
    {
      title: 'Getting Started with Next.js 15',
      slug: 'getting-started-with-nextjs-15',
      content: `
# Getting Started with Next.js 15

Next.js 15 brings exciting new features and improvements that make building React applications even better. In this comprehensive guide, we'll explore the key features and how to get started.

## What's New in Next.js 15

- Improved performance with better caching
- Enhanced developer experience
- New App Router features
- Better TypeScript support

## Installation

\`\`\`bash
npx create-next-app@latest my-app
cd my-app
npm run dev
\`\`\`

## Key Features

### App Router
The App Router provides a new way to structure your Next.js applications with improved performance and developer experience.

### Server Components
Server Components allow you to render components on the server, reducing the JavaScript bundle size and improving performance.

## Conclusion

Next.js 15 is a significant step forward in React development. Start building your next project today!
      `,
      excerpt: 'Learn how to build modern web applications with the latest version of Next.js. This comprehensive guide covers everything from setup to deployment.',
      status: 'PUBLISHED',
      publishedAt: new Date('2024-01-15T10:00:00Z'),
      metaTitle: 'Getting Started with Next.js 15 - Complete Guide',
      metaDescription: 'Learn Next.js 15 from scratch with this comprehensive tutorial covering installation, key features, and best practices.',
      authorId: adminUser.id,
      categoryId: createdCategories[0].id, // Technology
    },
    {
      title: 'Building a Modern CMS with TypeScript',
      slug: 'building-modern-cms-typescript',
      content: `
# Building a Modern CMS with TypeScript

Content Management Systems (CMS) are essential for managing digital content. In this tutorial, we'll build a modern CMS using TypeScript, Prisma, and Next.js.

## Why TypeScript?

TypeScript provides:
- Type safety
- Better developer experience
- Improved code quality
- Enhanced IDE support

## Architecture Overview

Our CMS will include:
- User authentication
- Content management
- Media handling
- SEO optimization

## Getting Started

Let's start by setting up our project structure and dependencies.

\`\`\`bash
npm install typescript @types/node prisma @prisma/client
\`\`\`

## Database Design

We'll use Prisma to design our database schema with the following models:
- User
- Post
- Category
- Tag
- Media

## Conclusion

Building a CMS with TypeScript provides a robust foundation for content management.
      `,
      excerpt: 'Discover how to create a powerful content management system using TypeScript, Prisma, and modern web technologies.',
      status: 'PUBLISHED',
      publishedAt: new Date('2024-01-14T15:30:00Z'),
      metaTitle: 'Building a Modern CMS with TypeScript - Tutorial',
      metaDescription: 'Step-by-step guide to building a content management system with TypeScript, Prisma, and Next.js.',
      authorId: editorUser.id,
      categoryId: createdCategories[1].id, // Development
    },
    {
      title: 'Best Practices for React Development',
      slug: 'best-practices-react-development',
      content: `
# Best Practices for React Development

React is a powerful library for building user interfaces. Following best practices ensures your code is maintainable, performant, and scalable.

## Component Design

### Keep Components Small and Focused
Each component should have a single responsibility and be easy to understand.

### Use Functional Components with Hooks
Modern React development favors functional components with hooks over class components.

## State Management

### Use Local State When Possible
Not everything needs to be in global state. Use local state for component-specific data.

### Consider Context for Shared State
React Context is great for sharing state across multiple components without prop drilling.

## Performance Optimization

### Use React.memo for Pure Components
Prevent unnecessary re-renders with React.memo.

### Optimize with useMemo and useCallback
Use these hooks to memoize expensive calculations and functions.

## Code Organization

### Consistent File Structure
Organize your files in a logical and consistent manner.

### Use TypeScript
TypeScript helps catch errors early and improves code quality.

## Conclusion

Following these best practices will help you build better React applications.
      `,
      excerpt: 'Learn the essential best practices for React development that will make your code more maintainable and performant.',
      status: 'PUBLISHED',
      publishedAt: new Date('2024-01-13T09:15:00Z'),
      metaTitle: 'React Development Best Practices - Complete Guide',
      metaDescription: 'Essential best practices for React development including component design, state management, and performance optimization.',
      authorId: adminUser.id,
      categoryId: createdCategories[2].id, // Programming
    },
  ]

  for (let i = 0; i < posts.length; i++) {
    const post = posts[i]
    const created = await prisma.post.upsert({
      where: { slug: post.slug },
      update: {},
      create: {
        ...post,
        tags: {
          connect: createdTags.slice(i * 3, (i + 1) * 3).map(tag => ({ id: tag.id })),
        },
      },
    })
    console.log('✅ Created post:', created.title)
  }

  // Create sample inquiries
  const inquiries = [
    {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      company: 'Tech Solutions Inc.',
      message: 'Hi, I\'m interested in your CMS solution for our company. Could you provide more information about pricing and features?',
      status: 'NEW',
      source: 'Website Contact Form',
    },
    {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0456',
      company: 'StartupCo',
      message: 'We\'re looking for a content management system that can scale with our growing business. Would love to schedule a demo.',
      status: 'CONTACTED',
      source: 'Google Ads',
    },
    {
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '******-0789',
      company: 'Digital Agency Pro',
      message: 'Our agency needs a white-label CMS solution for our clients. Can you provide information about licensing and customization options?',
      status: 'QUALIFIED',
      source: 'Referral',
    },
    {
      name: 'Lisa Rodriguez',
      email: '<EMAIL>',
      phone: '******-0321',
      company: 'E-commerce Plus',
      message: 'We need a CMS that integrates well with e-commerce platforms. Do you have any case studies or examples?',
      status: 'NEW',
      source: 'Social Media',
    },
  ]

  for (const inquiry of inquiries) {
    await prisma.inquiry.create({
      data: inquiry,
    })
    console.log('✅ Created inquiry:', inquiry.name)
  }

  // Create system settings
  const settings = [
    { key: 'siteName', value: 'NextJS CMS', type: 'string' },
    { key: 'siteDescription', value: 'A modern content management system built with Next.js', type: 'string' },
    { key: 'siteUrl', value: 'http://localhost:3000', type: 'string' },
    { key: 'adminEmail', value: '<EMAIL>', type: 'string' },
    { key: 'postsPerPage', value: '10', type: 'number' },
    { key: 'allowComments', value: 'true', type: 'boolean' },
    { key: 'moderateComments', value: 'true', type: 'boolean' },
    { key: 'allowRegistration', value: 'true', type: 'boolean' },
  ]

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: {},
      create: setting,
    })
    console.log('✅ Created setting:', setting.key)
  }

  console.log('🎉 Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
