// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  name      String?
  password  String
  role      String   @default("USER")
  avatar    String?
  bio       String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts     Post[]
  comments  Comment[]
  sessions  Session[]
  accounts  Account[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  color       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("tags")
}

model Post {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String?
  featuredImage String?
  featured    Boolean     @default(false)
  status      String      @default("DRAFT")
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // SEO fields
  metaTitle       String?
  metaDescription String?
  
  // Author
  authorId String
  author   User   @relation(fields: [authorId], references: [id])
  
  // Category
  categoryId String?
  category   Category? @relation(fields: [categoryId], references: [id])
  
  // Tags (many-to-many)
  tags Tag[]
  
  // Comments
  comments Comment[]
  
  // Media
  media Media[]

  @@map("posts")
}

model Comment {
  id        String        @id @default(cuid())
  content   String
  status    String        @default("PENDING")
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Author
  authorId String?
  author   User?   @relation(fields: [authorId], references: [id])
  
  // Guest info (for non-registered users)
  guestName  String?
  guestEmail String?
  
  // Post
  postId String
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  // Parent comment (for replies)
  parentId String?
  parent   Comment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

model Media {
  id        String    @id @default(cuid())
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  alt       String?
  caption   String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  posts Post[]
  products Product[]

  @@map("media")
}

model ProductCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  image       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // SEO fields
  seoTitle       String?
  seoDescription String?
  seoKeywords    String?

  // Hierarchy support
  parentId String?
  parent   ProductCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children ProductCategory[] @relation("CategoryHierarchy")

  // Relations
  products Product[]

  @@map("product_categories")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String
  content     String
  excerpt     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Product specific fields
  price       Decimal?
  sku         String?
  status      String   @default("ACTIVE") // ACTIVE, INACTIVE, DISCONTINUED
  featured    Boolean  @default(false)

  // SEO fields (复用Post模型的SEO设计)
  seoTitle       String?
  seoDescription String?
  seoKeywords    String?
  ogTitle        String?
  ogDescription  String?
  ogImage        String?
  schemaData     String?

  // Relations
  categoryId String?
  category   ProductCategory? @relation(fields: [categoryId], references: [id])
  images     ProductImage[]
  attributes ProductAttribute[]
  media      Media[]
  inquiries  Inquiry[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  caption   String?
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductAttribute {
  id    String @id @default(cuid())
  name  String
  value String
  type  String @default("text") // text, number, boolean, select

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_attributes")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String @default("string") // string, number, boolean, json

  @@map("settings")
}

model Inquiry {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  company   String?
  message   String
  status    String   @default("NEW") // NEW, CONTACTED, QUALIFIED, CLOSED
  source    String?  // 来源页面
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  productId String?
  product   Product? @relation(fields: [productId], references: [id])

  @@map("inquiries")
}

// Enum values as constants for type safety
// Role: "USER" | "ADMIN" | "EDITOR"
// PostStatus: "DRAFT" | "PUBLISHED" | "ARCHIVED"
// CommentStatus: "PENDING" | "APPROVED" | "REJECTED"
// ProductStatus: "ACTIVE" | "INACTIVE" | "DISCONTINUED"
// ProductAttributeType: "text" | "number" | "boolean" | "select"
// InquiryStatus: "NEW" | "CONTACTED" | "QUALIFIED" | "CLOSED"
