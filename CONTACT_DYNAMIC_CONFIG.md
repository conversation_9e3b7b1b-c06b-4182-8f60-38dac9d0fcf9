# 联系页面动态配置功能

## 🎯 功能概述

为联系页面(/contact)添加了完整的动态配置功能，管理员可以通过管理后台自定义联系页面的所有文案内容，包括页面标题、联系信息、表单设置等，无需修改代码即可调整页面内容。

## 📋 实现内容

### 1. 管理后台配置界面

#### 页面头部配置
- **页面标题**：自定义联系页面主标题
- **页面副标题**：自定义联系页面副标题  
- **页面描述**：自定义联系页面描述文字

#### 联系信息配置
- **公司地址**：支持多行地址信息
- **联系电话**：客服电话号码
- **联系邮箱**：客服邮箱地址
- **工作时间**：支持多行工作时间说明

#### 联系表单设置
- **显示表单**：控制是否显示联系表单
- **表单标题**：自定义表单标题
- **表单副标题**：自定义表单描述文字

### 2. 前端页面动态渲染

#### 配置获取
- 页面加载时自动从 `/api/themes/config` 获取联系页面配置
- 使用默认配置作为后备方案
- 实时应用配置更改

#### 动态显示
- 根据配置动态显示页面标题、副标题和描述
- 联系信息自动格式化显示（支持多行文本）
- 表单标题和描述动态更新
- 支持表单的显示/隐藏控制

## 🔧 技术实现

### API 支持
- 扩展了 `/api/themes/config` API 支持联系页面配置
- 提供完整的默认配置确保向后兼容
- 支持配置的读取和更新

### 前端组件
- 将联系页面改为客户端组件以支持动态配置
- 使用 React Hooks 管理配置状态
- 实现配置获取和应用逻辑
- 条件渲染确保最佳用户体验

### 组件增强
- 扩展了 `InquiryForm` 组件支持自定义标题和副标题
- 添加了加载状态处理
- 支持多行文本的正确显示

## 📝 配置结构

### 默认配置示例
```json
{
  "contact": {
    "header": {
      "title": "Contact Our Team",
      "subtitle": "Professional Support & Consultation",
      "description": "With over 10 years of experience serving enterprise clients, our team is ready to help you find the perfect content management solution for your organization."
    },
    "info": {
      "address": "InstAI Technology Center\n1234 Enterprise Boulevard\nSuite 500\nSan Francisco, CA 94105",
      "phone": "+****************",
      "email": "<EMAIL>",
      "workingHours": "Mon - Fri: 8:00 AM - 7:00 PM PST\nSaturday: 9:00 AM - 5:00 PM PST\nSunday: Emergency Support Only"
    },
    "form": {
      "showForm": true,
      "title": "Send us an inquiry",
      "subtitle": "Fill out the form below and we'll get back to you as soon as possible."
    }
  }
}
```

## 🎨 功能特性

### 多行文本支持
- **地址信息**：支持多行地址显示，自动换行
- **工作时间**：支持多行工作时间说明
- **描述文字**：支持长文本描述

### 智能链接生成
- **电话链接**：自动生成可点击的电话链接
- **邮箱链接**：自动生成邮箱链接
- **社交媒体**：动态更新社交媒体链接

### 表单控制
- **显示控制**：可以完全隐藏联系表单
- **动态标题**：表单标题和描述可自定义
- **向后兼容**：支持产品询价表单的原有功能

## 🚀 使用指南

### 管理员配置
1. **登录管理后台** → `/admin/themes`
2. **切换到联系页标签** → 找到"Contact"配置
3. **配置页面头部**：
   - 设置页面标题和副标题
   - 编写页面描述文字
4. **配置联系信息**：
   - 输入公司地址（支持多行）
   - 设置联系电话和邮箱
   - 编写工作时间说明
5. **配置联系表单**：
   - 选择是否显示表单
   - 自定义表单标题和描述
6. **保存配置** → 立即生效

### 前端显示
1. **自动加载**：页面自动获取最新配置
2. **动态渲染**：所有文案内容动态显示
3. **响应式**：在所有设备上都有良好的显示效果
4. **加载状态**：配置加载时显示友好的加载动画

## 🔄 向后兼容

### 现有功能
- **产品询价**：保持原有的产品询价功能
- **表单验证**：保持原有的表单验证逻辑
- **邮件发送**：保持原有的邮件发送功能

### 默认值处理
- **配置缺失**：使用合理的默认值
- **API错误**：优雅降级到默认配置
- **网络问题**：本地缓存和错误处理

## 📊 性能优化

### 加载优化
- **客户端渲染**：避免服务端渲染的配置问题
- **配置缓存**：合理的配置缓存策略
- **错误处理**：完善的错误边界处理

### 用户体验
- **加载状态**：友好的加载指示器
- **错误提示**：清晰的错误信息
- **响应式设计**：适配所有设备尺寸

## 🎯 核心优势

- 🎨 **完全可配置**：无需修改代码即可自定义所有内容
- 📱 **响应式设计**：适配所有设备屏幕
- ⚡ **实时更新**：配置更改立即生效
- 🔒 **安全可靠**：完善的错误处理和默认值机制
- 🔄 **向后兼容**：现有功能不受影响
- 🎯 **用户友好**：直观的管理界面

现在联系页面已经具备了企业级CMS应有的完全可配置能力！
