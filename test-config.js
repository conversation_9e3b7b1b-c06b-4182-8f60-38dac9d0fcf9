// 测试配置API的简单脚本
const fetch = require('node-fetch');

async function testConfig() {
  try {
    const response = await fetch('http://localhost:3000/api/themes/config');
    const data = await response.json();
    
    console.log('✅ API响应成功');
    console.log('📦 Products配置:');
    console.log('  - Header Title:', data.data.products.header.title);
    console.log('  - Header Subtitle:', data.data.products.header.subtitle);
    console.log('  - Header Description:', data.data.products.header.description);
    console.log('  - Sidebar Categories Title:', data.data.products.sidebar.categoriesTitle);
    console.log('  - All Products Text:', data.data.products.sidebar.allProductsText);
    console.log('  - Hot Products Text:', data.data.products.sidebar.hotProductsText);
    console.log('  - Featured Badge Text:', data.data.products.card.featuredBadgeText);
    console.log('  - Learn More Button Text:', data.data.products.card.learnMoreButtonText);
    console.log('  - Empty State Title:', data.data.products.emptyState.title);
    console.log('  - Featured Page Title:', data.data.products.featured.pageTitle);
    console.log('  - Featured Empty Title:', data.data.products.featured.emptyTitle);
    console.log('  - Category Back Text:', data.data.products.category.backToProductsText);
    
    console.log('\n🎉 所有配置都已正确加载！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testConfig();
