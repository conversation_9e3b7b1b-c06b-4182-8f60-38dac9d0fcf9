export default function TestBackgroundPage() {
  return (
    <div 
      className="min-h-screen flex items-center justify-center"
      style={{
        backgroundImage: 'url(/images/page-bk.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center center',
        backgroundAttachment: 'fixed',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="bg-white/90 backdrop-blur-sm p-8 rounded-lg shadow-lg">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">背景图片测试页面</h1>
        <p className="text-gray-700 mb-4">
          如果您能看到这个页面的背景图片，说明图片加载成功。
        </p>
        <div className="space-y-2 text-sm text-gray-600">
          <p>图片路径: /images/page-bk.jpg</p>
          <p>背景设置: cover, center, fixed</p>
        </div>
        <div className="mt-6">
          <a 
            href="/" 
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            返回首页
          </a>
        </div>
      </div>
    </div>
  )
}
