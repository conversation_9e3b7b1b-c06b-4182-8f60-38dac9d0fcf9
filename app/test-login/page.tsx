'use client'

import { useState } from 'react'
import { api } from '@/app/lib/api-client'

export default function TestLoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('admin123')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const handleLogin = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('Attempting login with:', { email, password })
      const response = await api.login({ email, password })
      console.log('Login response:', response)
      setResult(response)
    } catch (error) {
      console.error('Login error:', error)
      setResult({ success: false, error: 'Network error' })
    } finally {
      setLoading(false)
    }
  }

  const handleTestWrongPassword = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('Testing wrong password')
      const response = await api.login({ email, password: 'wrongpassword' })
      console.log('Wrong password response:', response)
      setResult(response)
    } catch (error) {
      console.error('Login error:', error)
      setResult({ success: false, error: 'Network error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">Login Test Page</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div className="space-y-2">
            <button
              onClick={handleLogin}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Correct Login'}
            </button>
            
            <button
              onClick={handleTestWrongPassword}
              disabled={loading}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Wrong Password'}
            </button>
          </div>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-gray-100 rounded-md">
            <h3 className="font-medium mb-2">API Response:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
        
        <div className="mt-6 text-sm text-gray-600">
          <p><strong>Demo Accounts:</strong></p>
          <p>Admin: <EMAIL> / admin123</p>
          <p>Editor: <EMAIL> / editor123</p>
        </div>
      </div>
    </div>
  )
}
