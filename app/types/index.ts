import { User, Post, Category, Tag, Comment, Media, Setting, Product, ProductCategory, ProductImage, ProductAttribute, Inquiry } from '@prisma/client'

// Enum constants for type safety
export const Role = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  EDITOR: 'EDITOR',
} as const

export const PostStatus = {
  DRAFT: 'DRAFT',
  PUBLISHED: 'PUBLISHED',
  ARCHIVED: 'ARCHIVED',
} as const

export const CommentStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
} as const

export const ProductStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DISCONTINUED: 'DISCONTINUED',
} as const

export const ProductAttributeType = {
  TEXT: 'text',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  SELECT: 'select',
} as const

export const InquiryStatus = {
  NEW: 'NEW',
  CONTACTED: 'CONTACTED',
  QUALIFIED: 'QUALIFIED',
  CLOSED: 'CLOSED',
} as const

export type RoleType = typeof Role[keyof typeof Role]
export type PostStatusType = typeof PostStatus[keyof typeof PostStatus]
export type CommentStatusType = typeof CommentStatus[keyof typeof CommentStatus]
export type ProductStatusType = typeof ProductStatus[keyof typeof ProductStatus]
export type ProductAttributeTypeType = typeof ProductAttributeType[keyof typeof ProductAttributeType]
export type InquiryStatusType = typeof InquiryStatus[keyof typeof InquiryStatus]

// Extended types with relations
export type PostWithRelations = Post & {
  author: User
  category?: Category | null
  tags: Tag[]
  comments: Comment[]
  media: Media[]
  _count?: {
    comments: number
  }
}

export type CommentWithRelations = Comment & {
  author?: User | null
  post: Post
  parent?: Comment | null
  replies: Comment[]
}

export type UserWithRelations = User & {
  posts: Post[]
  comments: Comment[]
  _count?: {
    posts: number
    comments: number
  }
}

// Product related types with relations
export type ProductWithRelations = Product & {
  category?: ProductCategory | null
  images: ProductImage[]
  attributes: ProductAttribute[]
  media: Media[]
  _count?: {
    images: number
    attributes: number
  }
}

export type ProductCategoryWithRelations = ProductCategory & {
  parent?: ProductCategory | null
  children: ProductCategory[]
  products: Product[]
  _count?: {
    products: number
    children: number
    totalProducts?: number
  }
}

export type InquiryWithRelations = Inquiry & {
  product?: Product | null
}

export type MediaWithRelations = Media & {
  posts?: Post[]
  products?: Product[]
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  username: string
  name: string
  password: string
  confirmPassword: string
}

export interface PostForm {
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImage?: string
  featured?: boolean
  status: PostStatusType
  categoryId?: string
  tagIds: string[]
  metaTitle?: string
  metaDescription?: string
}

export interface CategoryForm {
  name: string
  slug: string
  description?: string
  color?: string
}

export interface TagForm {
  name: string
  slug: string
  color?: string
}

export interface CommentForm {
  content: string
  postId: string
  parentId?: string
  guestName?: string
  guestEmail?: string
}

// Product form types
export interface ProductForm {
  name: string
  slug: string
  description: string
  content: string
  excerpt?: string
  price?: number
  sku?: string
  status: ProductStatusType
  featured: boolean
  categoryId?: string
  images?: ProductImageForm[]
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
}

export interface ProductImageForm {
  id?: string
  url: string
  alt?: string
  caption?: string
  sortOrder: number
}

export interface ProductCategoryForm {
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string
}

export interface ProductAttributeForm {
  name: string
  value: string
  type: ProductAttributeTypeType
}

export interface InquiryForm {
  name: string
  email: string
  phone?: string
  company?: string
  message: string
  productId?: string
  source?: string
}

// Settings
export interface SiteSettings {
  siteName: string
  siteDescription: string
  siteUrl: string
  adminEmail: string
  postsPerPage: number
  allowComments: boolean
  moderateComments: boolean
  allowRegistration: boolean
}

// Navigation
export interface NavItem {
  title: string
  href: string
  icon?: string
  children?: NavItem[]
}

// Dashboard stats
export interface DashboardStats {
  totalPosts: number
  totalUsers: number
  totalComments: number
  totalCategories: number
  totalProducts: number
  totalProductCategories: number
  totalInquiries: number
  recentPosts: PostWithRelations[]
  recentComments: CommentWithRelations[]
  recentProducts: ProductWithRelations[]
  recentInquiries: InquiryWithRelations[]
}
