'use client'

import { useEffect, useState } from 'react'
import { CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react'

interface SystemCheck {
  name: string
  status: 'success' | 'error' | 'warning' | 'pending'
  message: string
  details?: string
}

export default function SystemStatusPage() {
  const [checks, setChecks] = useState<SystemCheck[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    performSystemChecks()
  }, [])

  const performSystemChecks = async () => {
    setLoading(true)
    const systemChecks: SystemCheck[] = []

    // Check API endpoints
    const apiEndpoints = [
      { name: 'Authentication API', url: '/api/auth/me' },
      { name: 'Posts API', url: '/api/posts' },
      { name: 'Categories API', url: '/api/categories' },
      { name: 'Tags API', url: '/api/tags' },
      { name: 'Products API', url: '/api/products' },
      { name: 'Product Categories API', url: '/api/product-categories' },
      { name: 'Inquiries API', url: '/api/inquiries' },
      { name: 'Users API', url: '/api/users' },
      { name: 'Comments API', url: '/api/comments' },
      { name: 'Settings API', url: '/api/settings' },
      { name: 'Themes API', url: '/api/themes/config' },
      { name: 'Media API', url: '/api/media' },
    ]

    for (const endpoint of apiEndpoints) {
      try {
        const response = await fetch(endpoint.url)
        if (response.status === 401) {
          // 401 is expected for protected endpoints
          systemChecks.push({
            name: endpoint.name,
            status: 'success',
            message: 'API endpoint accessible (authentication required)',
          })
        } else if (response.ok) {
          systemChecks.push({
            name: endpoint.name,
            status: 'success',
            message: 'API endpoint working correctly',
          })
        } else {
          systemChecks.push({
            name: endpoint.name,
            status: 'error',
            message: `API endpoint returned ${response.status}`,
          })
        }
      } catch (error) {
        systemChecks.push({
          name: endpoint.name,
          status: 'error',
          message: 'API endpoint not accessible',
          details: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }

    // Check admin pages
    const adminPages = [
      { name: 'Dashboard', url: '/admin' },
      { name: 'Posts Management', url: '/admin/posts' },
      { name: 'Categories Management', url: '/admin/categories' },
      { name: 'Tags Management', url: '/admin/tags' },
      { name: 'Products Management', url: '/admin/products' },
      { name: 'Product Categories', url: '/admin/product-categories' },
      { name: 'Inquiries Management', url: '/admin/inquiries' },
      { name: 'Users Management', url: '/admin/users' },
      { name: 'Comments Management', url: '/admin/comments' },
      { name: 'Media Library', url: '/admin/media' },
      { name: 'Theme Configuration', url: '/admin/themes' },
      { name: 'System Settings', url: '/admin/settings' },
    ]

    for (const page of adminPages) {
      try {
        const response = await fetch(page.url, { method: 'HEAD' })
        if (response.ok) {
          systemChecks.push({
            name: page.name,
            status: 'success',
            message: 'Admin page accessible',
          })
        } else {
          systemChecks.push({
            name: page.name,
            status: 'error',
            message: `Admin page returned ${response.status}`,
          })
        }
      } catch (error) {
        systemChecks.push({
          name: page.name,
          status: 'error',
          message: 'Admin page not accessible',
        })
      }
    }

    // Check public pages
    const publicPages = [
      { name: 'Homepage', url: '/' },
      { name: 'Blog', url: '/blog' },
      { name: 'Products', url: '/products' },
      { name: 'Contact', url: '/contact' },
      { name: 'Sitemap', url: '/sitemap.xml' },
      { name: 'Robots.txt', url: '/robots.txt' },
    ]

    for (const page of publicPages) {
      try {
        const response = await fetch(page.url, { method: 'HEAD' })
        if (response.ok) {
          systemChecks.push({
            name: page.name,
            status: 'success',
            message: 'Public page accessible',
          })
        } else {
          systemChecks.push({
            name: page.name,
            status: 'error',
            message: `Public page returned ${response.status}`,
          })
        }
      } catch (error) {
        systemChecks.push({
          name: page.name,
          status: 'error',
          message: 'Public page not accessible',
        })
      }
    }

    setChecks(systemChecks)
    setLoading(false)
  }

  const getStatusIcon = (status: SystemCheck['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case 'pending':
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: SystemCheck['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'pending':
        return 'bg-gray-50 border-gray-200'
    }
  }

  const successCount = checks.filter(check => check.status === 'success').length
  const errorCount = checks.filter(check => check.status === 'error').length
  const warningCount = checks.filter(check => check.status === 'warning').length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">System Status</h1>
        <p className="mt-1 text-sm text-gray-500">
          Complete system health check and feature overview
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Successful</p>
              <p className="text-2xl font-bold text-gray-900">{successCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <XCircle className="w-8 h-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Errors</p>
              <p className="text-2xl font-bold text-gray-900">{errorCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <AlertCircle className="w-8 h-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Warnings</p>
              <p className="text-2xl font-bold text-gray-900">{warningCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-bold">%</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Health Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round((successCount / checks.length) * 100)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* System Checks */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">System Checks</h2>
          <p className="text-sm text-gray-500">
            Detailed status of all system components
          </p>
        </div>

        <div className="divide-y divide-gray-200">
          {checks.map((check, index) => (
            <div
              key={index}
              className={`p-4 ${getStatusColor(check.status)}`}
            >
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {getStatusIcon(check.status)}
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium text-gray-900">
                    {check.name}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {check.message}
                  </p>
                  {check.details && (
                    <p className="text-xs text-gray-500 mt-1">
                      {check.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Feature Overview */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Feature Overview</h2>
          <p className="text-sm text-gray-500">
            Complete list of implemented features
          </p>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Content Management</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Blog Posts & Pages
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Categories & Tags
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Rich Text Editor
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Media Library
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  SEO Optimization
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-3">E-commerce</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Product Management
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Product Categories
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Product Attributes
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Inquiry System
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Contact Forms
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-3">Administration</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  User Management
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Role-based Access
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Comment Moderation
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  System Settings
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  Theme Configuration
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="mt-6 flex justify-center">
        <button
          onClick={performSystemChecks}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Checking...' : 'Refresh Status'}
        </button>
      </div>
    </div>
  )
}
