'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import LogoSelector from '@/app/components/LogoSelector'
import { api } from '@/app/lib/api-client'
import MediaInput from '@/app/components/MediaInput'

interface CarouselSlide {
  id: string
  title: string
  subtitle: string
  description: string
  image: string
  primaryButton: {
    text: string
    link: string
  }
  secondaryButton: {
    text: string
    link: string
  }
  isActive: boolean
}

interface ThemeConfig {
  header: {
    topBar: {
      leftText: string
      rightEmail: string
      rightPhone: string
    }
    brand: {
      name: string
      tagline: string
      logo: {
        type: 'text' | 'image' | 'icon'
        value: string
        alt?: string
      }
    }
    navigation: Array<{
      name: string
      href: string
      enabled: boolean
    }>
    ctaButton: {
      text: string
      href: string
    }
  }
  footer: {
    brand: {
      name: string
      tagline: string
      description: string
    }
    socialMedia: Array<{
      name: string
      href: string
      enabled: boolean
    }>
    productLinks: {
      title: string
      links: Array<{
        name: string
        href: string
        enabled: boolean
      }>
    }
    supportLinks: {
      title: string
      links: Array<{
        name: string
        href: string
        enabled: boolean
      }>
    }
    customGroups?: Array<{
      title: string
      enabled: boolean
      links: Array<{
        name: string
        href: string
        enabled: boolean
      }>
    }>
    bottomBar: {
      copyright: string
      legalLinks: Array<{
        name: string
        href: string
        enabled: boolean
      }>
    }
  }
  homepage: {
    hero: {
      carousel: {
        autoPlay: boolean
        autoPlayInterval: number
        showDots: boolean
        showArrows: boolean
        slides: CarouselSlide[]
      }
    }
    productShowcase: {
      title: string
      subtitle: string
      description: string
      viewAllButtonText: string
    }
    companyIntro: {
      title: string
      description1: string
      description2: string
      missionTitle: string
      missionDescription: string
      visionTitle: string
      visionDescription: string
      moreInfoButtonText: string
      moreInfoButtonLink: string
      contactButtonText: string
      contactButtonLink: string
    }
    hotSaleProducts: {
      title: string
      subtitle: string
      description: string
      ctaTitle: string
      ctaDescription: string
      viewAllButtonText: string
      viewAllButtonLink: string
      contactSalesButtonText: string
      contactSalesButtonLink: string
    }
  }
  products: {
    header: {
      title: string
      subtitle: string
      description: string
    }
    sidebar: {
      categoriesTitle: string
      allProductsText: string
      hotProductsText: string
      byCategoryText: string
    }
    card: {
      featuredBadgeText: string
      learnMoreButtonText: string
      contactForPriceText: string
      productCodeLabel: string
    }
    emptyState: {
      title: string
      description: string
      contactButtonText: string
    }
    featured: {
      pageTitle: string
      pageSubtitle: string
      pageDescription: string
      badgeTitle: string
      badgeDescription: string
      emptyTitle: string
      emptyDescription: string
      browseAllButtonText: string
    }
    category: {
      backToProductsText: string
      productsInCategoryText: string
      emptyTitle: string
      emptyDescription: string
      browseAllButtonText: string
      paginationPrevText: string
      paginationNextText: string
    }
    detail: {
      backToCategoryText: string
      backToProductsText: string
      priceLabel: string
      skuLabel: string
      categoryLabel: string
      statusLabel: string
      inquiryFormTitle: string
      relatedProductsTitle: string
      specificationsTitle: string
      descriptionTitle: string
      imagesTitle: string
      inquirySection: {
        title: string
        description: string
        features: string[]
      }
    }
    filters: {
      showCategories: boolean
      showSearch: boolean
      showSort: boolean
    }
    display: {
      itemsPerPage: number
      showPagination: boolean
      gridColumns: number
    }
  }
  about: {
    hero: {
      title: string
      subtitle: string
      description: string
    }
    company: {
      title: string
      foundingStory: {
        title: string
        description: string
      }
      timeline: Array<{
        period: string
        description: string
      }>
    }
    mission: {
      title: string
      items: Array<{
        title: string
        image: string
        description: string
      }>
    }
    statistics: {
      title: string
      items: Array<{
        value: string
        label: string
      }>
    }
    features: {
      title: string
      items: Array<{
        title: string
        image: string
        description: string
      }>
    }
    cta: {
      title: string
      description: string
      primaryButton: {
        text: string
        link: string
      }
      secondaryButton: {
        text: string
        link: string
      }
    }
  }
  blog: {
    header: {
      title: string
      subtitle: string
      description: string
    }
    display: {
      itemsPerPage: number
      showCategories: boolean
      showTags: boolean
      showAuthor: boolean
      showDate: boolean
      showComments: boolean
    }
    sidebar: {
      showRecentPosts: boolean
      showCategories: boolean
      showTags: boolean
    }
  }
  contact: {
    header: {
      title: string
      subtitle: string
      description: string
    }
    info: {
      address: string
      workingHours: string
      contactGroups: Array<{
        id: string
        name: string
        description: string
        phones: Array<{
          label: string
          number: string
          description?: string
        }>
        emails: Array<{
          label: string
          address: string
          description?: string
        }>
      }>
    }
    socialMedia: {
      showSocialMedia: boolean
      title: string
      platforms: Array<{
        name: string
        url: string
        icon: string
        enabled: boolean
      }>
    }
    form: {
      showForm: boolean
      title: string
      subtitle: string
    }
    faq: {
      showFaq: boolean
      title: string
      subtitle: string
      questions: Array<{
        question: string
        answer: string
      }>
    }
    map: {
      showMap: boolean
      latitude: string
      longitude: string
    }
  }
}

export default function ThemesPage() {
  const [config, setConfig] = useState<ThemeConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('homepage')

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      setLoading(true)
      const data = await api.getThemeConfig()

      if (data.success) {
        setConfig(data.data)
      } else {
        console.error('Error fetching theme config:', data.error)
      }
    } catch (error) {
      console.error('Error fetching theme config:', error)
    } finally {
      setLoading(false)
    }
  }





  const handleCarouselConfigChange = (key: string, value: any) => {
    if (!config) return

    const updatedConfig = { ...config }
    if (!updatedConfig.homepage?.hero?.carousel) {
      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
      if (!updatedConfig.homepage.hero) updatedConfig.homepage.hero = {} as any
      updatedConfig.homepage.hero.carousel = {
        autoPlay: true,
        autoPlayInterval: 5,
        showDots: true,
        showArrows: true,
        slides: []
      }
    }

    updatedConfig.homepage.hero.carousel = {
      ...updatedConfig.homepage.hero.carousel,
      [key]: value
    }

    setConfig(updatedConfig)
  }

  const handleAddSlide = () => {
    if (!config) return

    const newSlide: CarouselSlide = {
      id: `slide-${Date.now()}`,
      title: 'New Slide',
      subtitle: 'Subtitle',
      description: 'Enter your slide description here',
      image: '/images/lb01.jpg',
      primaryButton: {
        text: 'Get Started',
        link: '/products'
      },
      secondaryButton: {
        text: 'Learn More',
        link: '/about'
      },
      isActive: true
    }

    const updatedConfig = { ...config }
    if (!updatedConfig.homepage?.hero?.carousel) {
      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
      if (!updatedConfig.homepage.hero) updatedConfig.homepage.hero = {} as any
      updatedConfig.homepage.hero.carousel = {
        autoPlay: true,
        autoPlayInterval: 5,
        showDots: true,
        showArrows: true,
        slides: []
      }
    }

    updatedConfig.homepage.hero.carousel.slides = [...updatedConfig.homepage.hero.carousel.slides, newSlide]
    setConfig(updatedConfig)
  }

  const handleSlideChange = (slideId: string, key: string, value: any) => {
    if (!config || !config.homepage?.hero?.carousel) return

    const updatedConfig = { ...config }
    updatedConfig.homepage.hero.carousel.slides = updatedConfig.homepage.hero.carousel.slides.map(slide => {
      if (slide.id === slideId) {
        if (key.startsWith('primaryButton.')) {
          const buttonKey = key.replace('primaryButton.', '')
          return {
            ...slide,
            primaryButton: {
              ...slide.primaryButton,
              [buttonKey]: value
            }
          }
        } else if (key.startsWith('secondaryButton.')) {
          const buttonKey = key.replace('secondaryButton.', '')
          return {
            ...slide,
            secondaryButton: {
              ...slide.secondaryButton,
              [buttonKey]: value
            }
          }
        } else {
          return { ...slide, [key]: value }
        }
      }
      return slide
    })

    setConfig(updatedConfig)
  }

  const handleDeleteSlide = (slideId: string) => {
    if (!config || !config.homepage?.hero?.carousel) return
    if (!confirm('Are you sure you want to delete this slide?')) return

    const updatedConfig = { ...config }
    updatedConfig.homepage.hero.carousel.slides = updatedConfig.homepage.hero.carousel.slides.filter(slide => slide.id !== slideId)
    setConfig(updatedConfig)
  }

  const handleSave = async () => {
    if (!config) return

    try {
      setSaving(true)
      const data = await api.updateThemeConfig(config)

      if (data.success) {
        alert('Theme configuration saved successfully!')
      } else {
        alert('Error saving theme configuration: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving theme config:', error)
      alert('Error saving theme configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = async () => {
    if (!confirm('Are you sure you want to reset to default theme configuration?')) {
      return
    }

    try {
      setSaving(true)
      const data = await api.resetThemeConfig()

      if (data.success) {
        setConfig(data.data)
        alert('Theme configuration reset to defaults!')
      } else {
        alert('Error resetting theme configuration: ' + data.error)
      }
    } catch (error) {
      console.error('Error resetting theme config:', error)
      alert('Error resetting theme configuration')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Failed to load theme configuration.</p>
        <Button onClick={fetchConfig} className="mt-4">
          Retry
        </Button>
      </div>
    )
  }

  const tabs = [
    { id: 'header', label: '头部', icon: '🎯' },
    { id: 'footer', label: '底部', icon: '🦶' },
    { id: 'homepage', label: '首页', icon: '🏠' },
    { id: 'products', label: '产品页', icon: '📦' },
    { id: 'about', label: '关于页', icon: 'ℹ️' },
    { id: 'blog', label: '博客页', icon: '📝' },
    { id: 'contact', label: '联系页', icon: '📞' },
  ]

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Theme Configuration</h1>
          <p className="mt-1 text-sm text-gray-500">
            Customize the appearance and branding of your website
          </p>
        </div>
        <div className="mt-4 sm:mt-0 space-x-3">
          <Button
            onClick={handleReset}
            variant="outline"
            disabled={saving}
          >
            Reset to Defaults
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">








        {/* Header Tab */}
        {activeTab === 'header' && (
          <div className="space-y-6">
            {/* Top Bar Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📢 Top Bar Settings</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Left Text</label>
                  <Input
                    type="text"
                    value={config.header?.topBar?.leftText || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.topBar) updatedConfig.header.topBar = {} as any
                      updatedConfig.header.topBar.leftText = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Professional Content Management System · Trusted by 50K+ Websites"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <Input
                      type="email"
                      value={config.header?.topBar?.rightEmail || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.header) updatedConfig.header = {} as any
                        if (!updatedConfig.header.topBar) updatedConfig.header.topBar = {} as any
                        updatedConfig.header.topBar.rightEmail = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., <EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <Input
                      type="tel"
                      value={config.header?.topBar?.rightPhone || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.header) updatedConfig.header = {} as any
                        if (!updatedConfig.header.topBar) updatedConfig.header.topBar = {} as any
                        updatedConfig.header.topBar.rightPhone = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., +****************"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Brand Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🏷️ Brand Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Brand Name</label>
                  <Input
                    type="text"
                    value={config.header?.brand?.name || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.brand) updatedConfig.header.brand = {} as any
                      updatedConfig.header.brand.name = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., InstAI CMS"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Tagline</label>
                  <Input
                    type="text"
                    value={config.header?.brand?.tagline || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.brand) updatedConfig.header.brand = {} as any
                      updatedConfig.header.brand.tagline = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Content Management System"
                  />
                </div>
                <div className="space-y-2">
                  <LogoSelector
                    value={config.header?.brand?.logo || { type: 'text', value: 'I', alt: 'Logo' }}
                    onChange={(logoConfig) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.brand) updatedConfig.header.brand = {} as any
                      updatedConfig.header.brand.logo = logoConfig
                      setConfig(updatedConfig)
                    }}
                    label="Logo"
                  />
                </div>
              </div>
            </div>

            {/* CTA Button Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🎯 CTA Button Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Button Text</label>
                  <Input
                    type="text"
                    value={config.header?.ctaButton?.text || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.ctaButton) updatedConfig.header.ctaButton = {} as any
                      updatedConfig.header.ctaButton.text = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Get Started"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Button URL</label>
                  <Input
                    type="text"
                    value={config.header?.ctaButton?.href || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.header) updatedConfig.header = {} as any
                      if (!updatedConfig.header.ctaButton) updatedConfig.header.ctaButton = {} as any
                      updatedConfig.header.ctaButton.href = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., /contact"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer Tab */}
        {activeTab === 'footer' && (
          <div className="space-y-6">
            {/* Brand Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🏷️ Footer Brand Settings</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Brand Name</label>
                    <Input
                      type="text"
                      value={config.footer?.brand?.name || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.footer) updatedConfig.footer = {} as any
                        if (!updatedConfig.footer.brand) updatedConfig.footer.brand = {} as any
                        updatedConfig.footer.brand.name = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., InstAI CMS"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Tagline</label>
                    <Input
                      type="text"
                      value={config.footer?.brand?.tagline || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.footer) updatedConfig.footer = {} as any
                        if (!updatedConfig.footer.brand) updatedConfig.footer.brand = {} as any
                        updatedConfig.footer.brand.tagline = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Content Management System"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={config.footer?.brand?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.footer) updatedConfig.footer = {} as any
                      if (!updatedConfig.footer.brand) updatedConfig.footer.brand = {} as any
                      updatedConfig.footer.brand.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., InstAI CMS is a powerful, flexible content management system..."
                  />
                </div>
              </div>
            </div>

            {/* Product Links Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📦 Product Links Settings</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.footer?.productLinks?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.footer) updatedConfig.footer = {} as any
                      if (!updatedConfig.footer.productLinks) updatedConfig.footer.productLinks = { title: '', links: [] }
                      updatedConfig.footer.productLinks.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Product"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">Product Links</label>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.footer) updatedConfig.footer = {} as any
                        if (!updatedConfig.footer.productLinks) updatedConfig.footer.productLinks = { title: 'Product', links: [] }

                        updatedConfig.footer.productLinks.links.push({
                          name: 'New Link',
                          href: '/',
                          enabled: true
                        })
                        setConfig(updatedConfig)
                      }}
                    >
                      + Add Product Link
                    </Button>
                  </div>
                  {config.footer?.productLinks?.links?.map((link, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={link.enabled}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.productLinks?.links?.[index]) {
                              updatedConfig.footer.productLinks.links[index].enabled = e.target.checked
                              setConfig(updatedConfig)
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="col-span-4">
                        <Input
                          type="text"
                          value={link.name}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.productLinks?.links?.[index]) {
                              updatedConfig.footer.productLinks.links[index].name = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Link name"
                        />
                      </div>
                      <div className="col-span-6">
                        <Input
                          type="text"
                          value={link.href}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.productLinks?.links?.[index]) {
                              updatedConfig.footer.productLinks.links[index].href = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="URL"
                        />
                      </div>
                      <div className="col-span-1">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.productLinks?.links) {
                              updatedConfig.footer.productLinks.links.splice(index, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Support Links Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🛠️ Support Links Settings</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.footer?.supportLinks?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.footer) updatedConfig.footer = {} as any
                      if (!updatedConfig.footer.supportLinks) updatedConfig.footer.supportLinks = { title: '', links: [] }
                      updatedConfig.footer.supportLinks.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Support"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">Support Links</label>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.footer) updatedConfig.footer = {} as any
                        if (!updatedConfig.footer.supportLinks) updatedConfig.footer.supportLinks = { title: 'Support', links: [] }

                        updatedConfig.footer.supportLinks.links.push({
                          name: 'New Link',
                          href: '/',
                          enabled: true
                        })
                        setConfig(updatedConfig)
                      }}
                    >
                      + Add Support Link
                    </Button>
                  </div>
                  {config.footer?.supportLinks?.links?.map((link, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={link.enabled}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.supportLinks?.links?.[index]) {
                              updatedConfig.footer.supportLinks.links[index].enabled = e.target.checked
                              setConfig(updatedConfig)
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="col-span-4">
                        <Input
                          type="text"
                          value={link.name}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.supportLinks?.links?.[index]) {
                              updatedConfig.footer.supportLinks.links[index].name = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Link name"
                        />
                      </div>
                      <div className="col-span-6">
                        <Input
                          type="text"
                          value={link.href}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.supportLinks?.links?.[index]) {
                              updatedConfig.footer.supportLinks.links[index].href = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="URL"
                        />
                      </div>
                      <div className="col-span-1">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.supportLinks?.links) {
                              updatedConfig.footer.supportLinks.links.splice(index, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Custom Link Groups Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🔗 Custom Link Groups</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">Custom Link Groups</label>
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.footer) updatedConfig.footer = {} as any
                      if (!updatedConfig.footer.customGroups) updatedConfig.footer.customGroups = []

                      updatedConfig.footer.customGroups.push({
                        title: 'New Group',
                        enabled: true,
                        links: [
                          {
                            name: 'New Link',
                            href: '/',
                            enabled: true
                          }
                        ]
                      })
                      setConfig(updatedConfig)
                    }}
                  >
                    + Add Link Group
                  </Button>
                </div>

                {config.footer?.customGroups?.map((group, groupIndex) => (
                  <div key={groupIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3 flex-1">
                        <input
                          type="checkbox"
                          checked={group.enabled}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.customGroups?.[groupIndex]) {
                              updatedConfig.footer.customGroups[groupIndex].enabled = e.target.checked
                              setConfig(updatedConfig)
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <Input
                          type="text"
                          value={group.title}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.customGroups?.[groupIndex]) {
                              updatedConfig.footer.customGroups[groupIndex].title = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Group title"
                          className="flex-1"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.customGroups?.[groupIndex]) {
                              updatedConfig.footer.customGroups[groupIndex].links.push({
                                name: 'New Link',
                                href: '/',
                                enabled: true
                              })
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          + Link
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.customGroups) {
                              updatedConfig.footer.customGroups.splice(groupIndex, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          Delete Group
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2 ml-6">
                      {group.links?.map((link, linkIndex) => (
                        <div key={linkIndex} className="grid grid-cols-12 gap-2 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={link.enabled}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.footer?.customGroups?.[groupIndex]?.links?.[linkIndex]) {
                                  updatedConfig.footer.customGroups[groupIndex].links[linkIndex].enabled = e.target.checked
                                  setConfig(updatedConfig)
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </div>
                          <div className="col-span-4">
                            <Input
                              type="text"
                              value={link.name}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.footer?.customGroups?.[groupIndex]?.links?.[linkIndex]) {
                                  updatedConfig.footer.customGroups[groupIndex].links[linkIndex].name = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Link name"
                            />
                          </div>
                          <div className="col-span-6">
                            <Input
                              type="text"
                              value={link.href}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.footer?.customGroups?.[groupIndex]?.links?.[linkIndex]) {
                                  updatedConfig.footer.customGroups[groupIndex].links[linkIndex].href = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="URL"
                            />
                          </div>
                          <div className="col-span-1">
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.footer?.customGroups?.[groupIndex]?.links) {
                                  updatedConfig.footer.customGroups[groupIndex].links.splice(linkIndex, 1)
                                  setConfig(updatedConfig)
                                }
                              }}
                            >
                              ×
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Bottom Bar Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📄 Bottom Bar Settings</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Copyright Text</label>
                  <Input
                    type="text"
                    value={config.footer?.bottomBar?.copyright || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.footer) updatedConfig.footer = {} as any
                      if (!updatedConfig.footer.bottomBar) updatedConfig.footer.bottomBar = {} as any
                      updatedConfig.footer.bottomBar.copyright = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., © 2024 InstAI CMS. All rights reserved."
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">Legal Links</label>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.footer) updatedConfig.footer = {} as any
                        if (!updatedConfig.footer.bottomBar) updatedConfig.footer.bottomBar = {} as any
                        if (!updatedConfig.footer.bottomBar.legalLinks) updatedConfig.footer.bottomBar.legalLinks = []

                        updatedConfig.footer.bottomBar.legalLinks.push({
                          name: 'New Link',
                          href: '/',
                          enabled: true
                        })
                        setConfig(updatedConfig)
                      }}
                    >
                      + Add Legal Link
                    </Button>
                  </div>
                  {config.footer?.bottomBar?.legalLinks?.map((link, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={link.enabled}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.bottomBar?.legalLinks?.[index]) {
                              updatedConfig.footer.bottomBar.legalLinks[index].enabled = e.target.checked
                              setConfig(updatedConfig)
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="col-span-4">
                        <Input
                          type="text"
                          value={link.name}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.bottomBar?.legalLinks?.[index]) {
                              updatedConfig.footer.bottomBar.legalLinks[index].name = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Link name"
                        />
                      </div>
                      <div className="col-span-6">
                        <Input
                          type="text"
                          value={link.href}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.bottomBar?.legalLinks?.[index]) {
                              updatedConfig.footer.bottomBar.legalLinks[index].href = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="URL"
                        />
                      </div>
                      <div className="col-span-1">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.footer?.bottomBar?.legalLinks) {
                              updatedConfig.footer.bottomBar.legalLinks.splice(index, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
        {/* Homepage Tab */}
        {activeTab === 'homepage' && (
          <div className="space-y-6">
            {/* Hero Carousel Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🎠 Hero Carousel</h3>

              {/* Carousel Global Settings */}
              <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Global Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.homepage?.hero?.carousel?.autoPlay || false}
                        onChange={(e) => handleCarouselConfigChange('autoPlay', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Auto Play</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Auto Play Interval (seconds)
                    </label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.homepage?.hero?.carousel?.autoPlayInterval || 5}
                      onChange={(e) => handleCarouselConfigChange('autoPlayInterval', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.homepage?.hero?.carousel?.showDots || true}
                        onChange={(e) => handleCarouselConfigChange('showDots', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Dots</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.homepage?.hero?.carousel?.showArrows || true}
                        onChange={(e) => handleCarouselConfigChange('showArrows', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Arrows</span>
                    </label>
                  </div>
                </div>
              </div>

            {/* Carousel Slides */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-medium text-gray-900">Carousel Slides</h4>
                <Button
                  onClick={handleAddSlide}
                  variant="outline"
                  size="sm"
                >
                  + Add Slide
                </Button>
              </div>

              {config.homepage?.hero?.carousel?.slides?.map((slide, index) => (
                <div key={slide.id} className="border rounded-lg p-4 bg-white">
                  <div className="flex items-center justify-between mb-4">
                    <h5 className="text-sm font-medium text-gray-900">
                      Slide {index + 1}
                      {slide.isActive && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      )}
                    </h5>
                    <div className="flex items-center space-x-2">
                      <label className="flex items-center space-x-1">
                        <input
                          type="checkbox"
                          checked={slide.isActive}
                          onChange={(e) => handleSlideChange(slide.id, 'isActive', e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-xs text-gray-600">Active</span>
                      </label>
                      <Button
                        onClick={() => handleDeleteSlide(slide.id)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Title</label>
                      <Input
                        type="text"
                        value={slide.title}
                        onChange={(e) => handleSlideChange(slide.id, 'title', e.target.value)}
                        placeholder="Enter slide title"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Subtitle</label>
                      <Input
                        type="text"
                        value={slide.subtitle}
                        onChange={(e) => handleSlideChange(slide.id, 'subtitle', e.target.value)}
                        placeholder="Enter slide subtitle"
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        value={slide.description}
                        onChange={(e) => handleSlideChange(slide.id, 'description', e.target.value)}
                        rows={3}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter slide description"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Image URL</label>
                      <Input
                        type="text"
                        value={slide.image}
                        onChange={(e) => handleSlideChange(slide.id, 'image', e.target.value)}
                        placeholder="Enter image URL"
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-3">Primary Button (Main CTA)</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Button Text</label>
                          <Input
                            type="text"
                            value={slide.primaryButton.text}
                            onChange={(e) => handleSlideChange(slide.id, 'primaryButton.text', e.target.value)}
                            placeholder="e.g., Get Started"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Button Link</label>
                          <Input
                            type="text"
                            value={slide.primaryButton.link}
                            onChange={(e) => handleSlideChange(slide.id, 'primaryButton.link', e.target.value)}
                            placeholder="e.g., /products"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-3">Secondary Button (Optional)</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Button Text</label>
                          <Input
                            type="text"
                            value={slide.secondaryButton.text}
                            onChange={(e) => handleSlideChange(slide.id, 'secondaryButton.text', e.target.value)}
                            placeholder="e.g., Learn More"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Button Link</label>
                          <Input
                            type="text"
                            value={slide.secondaryButton.link}
                            onChange={(e) => handleSlideChange(slide.id, 'secondaryButton.link', e.target.value)}
                            placeholder="e.g., /about"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Slide Preview */}
                  {slide.image && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-600 mb-2">Preview:</p>
                      <div className="flex items-center space-x-3">
                        <img
                          src={slide.image}
                          alt={slide.title}
                          className="w-16 h-10 object-cover rounded"
                          onError={(e) => {
                            e.currentTarget.src = '/images/placeholder.jpg'
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">{slide.title}</p>
                          <p className="text-xs text-gray-500 truncate">{slide.subtitle}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )) || (
                <div className="text-center py-8 text-gray-500">
                  <p>No slides configured. Click "Add Slide" to get started.</p>
                </div>
              )}
            </div>
          </div>

          {/* Product Showcase Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">📦 Product Showcase</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Main Title</label>
                  <Input
                    type="text"
                    value={config.homepage?.productShowcase?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.productShowcase) updatedConfig.homepage.productShowcase = {} as any
                      updatedConfig.homepage.productShowcase.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., WELCOME TO InstAI CMS!"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Subtitle</label>
                  <Input
                    type="text"
                    value={config.homepage?.productShowcase?.subtitle || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.productShowcase) updatedConfig.homepage.productShowcase = {} as any
                      updatedConfig.homepage.productShowcase.subtitle = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Discover our flagship products"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <Input
                  type="text"
                  value={config.homepage?.productShowcase?.description || ''}
                  onChange={(e) => {
                    const updatedConfig = { ...config }
                    if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                    if (!updatedConfig.homepage.productShowcase) updatedConfig.homepage.productShowcase = {} as any
                    updatedConfig.homepage.productShowcase.description = e.target.value
                    setConfig(updatedConfig)
                  }}
                  placeholder="e.g., Discover our flagship products designed to power your digital transformation"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">View All Button Text</label>
                <Input
                  type="text"
                  value={config.homepage?.productShowcase?.viewAllButtonText || ''}
                  onChange={(e) => {
                    const updatedConfig = { ...config }
                    if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                    if (!updatedConfig.homepage.productShowcase) updatedConfig.homepage.productShowcase = {} as any
                    updatedConfig.homepage.productShowcase.viewAllButtonText = e.target.value
                    setConfig(updatedConfig)
                  }}
                  placeholder="e.g., 全部产品"
                />
              </div>
            </div>
          </div>

          {/* Company Intro Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">🏢 Company Introduction</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Section Title</label>
                <Input
                  type="text"
                  value={config.homepage?.companyIntro?.title || ''}
                  onChange={(e) => {
                    const updatedConfig = { ...config }
                    if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                    if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                    updatedConfig.homepage.companyIntro.title = e.target.value
                    setConfig(updatedConfig)
                  }}
                  placeholder="e.g., ABOUT OUR COMPANY"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description 1</label>
                  <textarea
                    value={config.homepage?.companyIntro?.description1 || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                      updatedConfig.homepage.companyIntro.description1 = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="First paragraph of company description"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description 2</label>
                  <textarea
                    value={config.homepage?.companyIntro?.description2 || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                      updatedConfig.homepage.companyIntro.description2 = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Second paragraph of company description"
                  />
                </div>
              </div>

              {/* Mission & Vision Section */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Mission & Vision</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Mission Title</label>
                    <Input
                      type="text"
                      value={config.homepage?.companyIntro?.missionTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                        updatedConfig.homepage.companyIntro.missionTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Mission"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Vision Title</label>
                    <Input
                      type="text"
                      value={config.homepage?.companyIntro?.visionTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                        updatedConfig.homepage.companyIntro.visionTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Vision"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Mission Description</label>
                    <textarea
                      value={config.homepage?.companyIntro?.missionDescription || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                        updatedConfig.homepage.companyIntro.missionDescription = e.target.value
                        setConfig(updatedConfig)
                      }}
                      rows={4}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Mission statement description"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Vision Description</label>
                    <textarea
                      value={config.homepage?.companyIntro?.visionDescription || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                        updatedConfig.homepage.companyIntro.visionDescription = e.target.value
                        setConfig(updatedConfig)
                      }}
                      rows={4}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Vision statement description"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons Section */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Action Buttons</h4>
                <div className="space-y-4">
                  {/* More Info Button */}
                  <div className="p-3 bg-white rounded border">
                    <h5 className="text-sm font-medium text-gray-800 mb-3">More Info Button</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Text</label>
                        <Input
                          type="text"
                          value={config.homepage?.companyIntro?.moreInfoButtonText || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                            if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                            updatedConfig.homepage.companyIntro.moreInfoButtonText = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="e.g., More Info"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Link</label>
                        <Input
                          type="text"
                          value={config.homepage?.companyIntro?.moreInfoButtonLink || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                            if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                            updatedConfig.homepage.companyIntro.moreInfoButtonLink = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="e.g., /about"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contact Button */}
                  <div className="p-3 bg-white rounded border">
                    <h5 className="text-sm font-medium text-gray-800 mb-3">Contact Button</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Text</label>
                        <Input
                          type="text"
                          value={config.homepage?.companyIntro?.contactButtonText || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                            if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                            updatedConfig.homepage.companyIntro.contactButtonText = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="e.g., Contact Us"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Link</label>
                        <Input
                          type="text"
                          value={config.homepage?.companyIntro?.contactButtonLink || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                            if (!updatedConfig.homepage.companyIntro) updatedConfig.homepage.companyIntro = {} as any
                            updatedConfig.homepage.companyIntro.contactButtonLink = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="e.g., /contact"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Hot Sale Products Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">🔥 Hot Sale Products</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Main Title</label>
                  <Input
                    type="text"
                    value={config.homepage?.hotSaleProducts?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                      updatedConfig.homepage.hotSaleProducts.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., HOT SALE PRODUCTS"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <Input
                    type="text"
                    value={config.homepage?.hotSaleProducts?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                      if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                      updatedConfig.homepage.hotSaleProducts.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., To be competitive, InstAI CMS will have new products every year"
                  />
                </div>
              </div>

              {/* Bottom CTA Section */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Bottom Call-to-Action</h4>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">CTA Title</label>
                    <Input
                      type="text"
                      value={config.homepage?.hotSaleProducts?.ctaTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                        updatedConfig.homepage.hotSaleProducts.ctaTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., 🔥 Limited Time Offers!"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">CTA Description</label>
                    <textarea
                      value={config.homepage?.hotSaleProducts?.ctaDescription || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                        if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                        updatedConfig.homepage.hotSaleProducts.ctaDescription = e.target.value
                        setConfig(updatedConfig)
                      }}
                      rows={3}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Don't miss out on these exclusive deals..."
                    />
                  </div>
                  <div className="space-y-4">
                    {/* View All Button */}
                    <div className="p-3 bg-white rounded border">
                      <h5 className="text-sm font-medium text-gray-800 mb-3">View All Products Button</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Button Text</label>
                          <Input
                            type="text"
                            value={config.homepage?.hotSaleProducts?.viewAllButtonText || ''}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                              if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                              updatedConfig.homepage.hotSaleProducts.viewAllButtonText = e.target.value
                              setConfig(updatedConfig)
                            }}
                            placeholder="e.g., View All Products"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Button Link</label>
                          <Input
                            type="text"
                            value={config.homepage?.hotSaleProducts?.viewAllButtonLink || ''}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                              if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                              updatedConfig.homepage.hotSaleProducts.viewAllButtonLink = e.target.value
                              setConfig(updatedConfig)
                            }}
                            placeholder="e.g., /products"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Contact Sales Button */}
                    <div className="p-3 bg-white rounded border">
                      <h5 className="text-sm font-medium text-gray-800 mb-3">Contact Sales Button</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Button Text</label>
                          <Input
                            type="text"
                            value={config.homepage?.hotSaleProducts?.contactSalesButtonText || ''}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                              if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                              updatedConfig.homepage.hotSaleProducts.contactSalesButtonText = e.target.value
                              setConfig(updatedConfig)
                            }}
                            placeholder="e.g., Contact Sales"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Button Link</label>
                          <Input
                            type="text"
                            value={config.homepage?.hotSaleProducts?.contactSalesButtonLink || ''}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.homepage) updatedConfig.homepage = {} as any
                              if (!updatedConfig.homepage.hotSaleProducts) updatedConfig.homepage.hotSaleProducts = {} as any
                              updatedConfig.homepage.hotSaleProducts.contactSalesButtonLink = e.target.value
                              setConfig(updatedConfig)
                            }}
                            placeholder="e.g., /contact"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="space-y-6">
            {/* Header Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📦 Products Page Header</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Main Title</label>
                    <Input
                      type="text"
                      value={config.products?.header?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.header) updatedConfig.products.header = {} as any
                        updatedConfig.products.header.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Product Catalog"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Subtitle</label>
                    <Input
                      type="text"
                      value={config.products?.header?.subtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.header) updatedConfig.products.header = {} as any
                        updatedConfig.products.header.subtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Enterprise Solutions & Services"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={config.products?.header?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.products) updatedConfig.products = {} as any
                      if (!updatedConfig.products.header) updatedConfig.products.header = {} as any
                      updatedConfig.products.header.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Comprehensive content management solutions designed for enterprise-level operations..."
                  />
                </div>
              </div>
            </div>

            {/* Sidebar Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🗂️ Sidebar Configuration</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Categories Section Title</label>
                  <Input
                    type="text"
                    value={config.products?.sidebar?.categoriesTitle || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.products) updatedConfig.products = {} as any
                      if (!updatedConfig.products.sidebar) updatedConfig.products.sidebar = {} as any
                      updatedConfig.products.sidebar.categoriesTitle = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Product Categories"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">All Products Text</label>
                    <Input
                      type="text"
                      value={config.products?.sidebar?.allProductsText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.sidebar) updatedConfig.products.sidebar = {} as any
                        updatedConfig.products.sidebar.allProductsText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., All Products"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Hot Products Text</label>
                    <Input
                      type="text"
                      value={config.products?.sidebar?.hotProductsText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.sidebar) updatedConfig.products.sidebar = {} as any
                        updatedConfig.products.sidebar.hotProductsText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Hot Products"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Category Section Label</label>
                  <Input
                    type="text"
                    value={config.products?.sidebar?.byCategoryText || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.products) updatedConfig.products = {} as any
                      if (!updatedConfig.products.sidebar) updatedConfig.products.sidebar = {} as any
                      updatedConfig.products.sidebar.byCategoryText = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., By Category"
                  />
                </div>
              </div>
            </div>

            {/* Product Card Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🏷️ Product Card Configuration</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Featured Badge Text</label>
                    <Input
                      type="text"
                      value={config.products?.card?.featuredBadgeText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.card) updatedConfig.products.card = {} as any
                        updatedConfig.products.card.featuredBadgeText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., FEATURED"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Learn More Button Text</label>
                    <Input
                      type="text"
                      value={config.products?.card?.learnMoreButtonText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.card) updatedConfig.products.card = {} as any
                        updatedConfig.products.card.learnMoreButtonText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Learn More →"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Price Contact Text</label>
                    <Input
                      type="text"
                      value={config.products?.card?.contactForPriceText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.card) updatedConfig.products.card = {} as any
                        updatedConfig.products.card.contactForPriceText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Contact for Price"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Product Code Label</label>
                    <Input
                      type="text"
                      value={config.products?.card?.productCodeLabel || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.card) updatedConfig.products.card = {} as any
                        updatedConfig.products.card.productCodeLabel = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Product Code:"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Empty State Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📭 Empty State Configuration</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Empty State Title</label>
                    <Input
                      type="text"
                      value={config.products?.emptyState?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.emptyState) updatedConfig.products.emptyState = {} as any
                        updatedConfig.products.emptyState.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Product Catalog Coming Soon"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Contact Button Text</label>
                    <Input
                      type="text"
                      value={config.products?.emptyState?.contactButtonText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.emptyState) updatedConfig.products.emptyState = {} as any
                        updatedConfig.products.emptyState.contactButtonText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Contact Sales Team"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Empty State Description</label>
                  <textarea
                    value={config.products?.emptyState?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.products) updatedConfig.products = {} as any
                      if (!updatedConfig.products.emptyState) updatedConfig.products.emptyState = {} as any
                      updatedConfig.products.emptyState.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={2}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="We're currently preparing our comprehensive product catalog..."
                  />
                </div>
              </div>
            </div>

            {/* Featured Products Page Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🔥 Featured Products Page</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Page Title</label>
                    <Input
                      type="text"
                      value={config.products?.featured?.pageTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.pageTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Hot Products"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Page Subtitle</label>
                    <Input
                      type="text"
                      value={config.products?.featured?.pageSubtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.pageSubtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Featured & Popular Items"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Page Description</label>
                  <textarea
                    value={config.products?.featured?.pageDescription || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.products) updatedConfig.products = {} as any
                      if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                      updatedConfig.products.featured.pageDescription = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={2}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Discover our most popular and featured products..."
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Badge Title</label>
                    <Input
                      type="text"
                      value={config.products?.featured?.badgeTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.badgeTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., 🔥 Hot Products"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Empty State Title</label>
                    <Input
                      type="text"
                      value={config.products?.featured?.emptyTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.emptyTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., No Hot Products Available"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Empty State Description</label>
                    <textarea
                      value={config.products?.featured?.emptyDescription || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.emptyDescription = e.target.value
                        setConfig(updatedConfig)
                      }}
                      rows={2}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="We're currently updating our featured products..."
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Browse All Button Text</label>
                    <Input
                      type="text"
                      value={config.products?.featured?.browseAllButtonText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.featured) updatedConfig.products.featured = {} as any
                        updatedConfig.products.featured.browseAllButtonText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Browse All Products"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Category Page Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📂 Category Pages</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Back to Products Text</label>
                    <Input
                      type="text"
                      value={config.products?.category?.backToProductsText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.category) updatedConfig.products.category = {} as any
                        updatedConfig.products.category.backToProductsText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., ← Back to Products"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Products in Category Text</label>
                    <Input
                      type="text"
                      value={config.products?.category?.productsInCategoryText || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.category) updatedConfig.products.category = {} as any
                        updatedConfig.products.category.productsInCategoryText = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Products in"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Empty Category Title</label>
                    <Input
                      type="text"
                      value={config.products?.category?.emptyTitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.category) updatedConfig.products.category = {} as any
                        updatedConfig.products.category.emptyTitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., No Products in This Category"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Empty Category Description</label>
                    <Input
                      type="text"
                      value={config.products?.category?.emptyDescription || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.category) updatedConfig.products.category = {} as any
                        updatedConfig.products.category.emptyDescription = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="This category is currently empty..."
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Display Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">⚙️ Display Settings</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Items Per Page</label>
                    <Input
                      type="number"
                      min="6"
                      max="24"
                      value={config.products?.display?.itemsPerPage || 12}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.display) updatedConfig.products.display = {} as any
                        updatedConfig.products.display.itemsPerPage = parseInt(e.target.value)
                        setConfig(updatedConfig)
                      }}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Grid Columns</label>
                    <select
                      value={config.products?.display?.gridColumns || 2}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.display) updatedConfig.products.display = {} as any
                        updatedConfig.products.display.gridColumns = parseInt(e.target.value)
                        setConfig(updatedConfig)
                      }}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={1}>1 Column</option>
                      <option value={2}>2 Columns</option>
                      <option value={3}>3 Columns</option>
                      <option value={4}>4 Columns</option>
                    </select>
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <input
                      type="checkbox"
                      id="showPagination"
                      checked={config.products?.display?.showPagination ?? true}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.products) updatedConfig.products = {} as any
                        if (!updatedConfig.products.display) updatedConfig.products.display = {} as any
                        updatedConfig.products.display.showPagination = e.target.checked
                        setConfig(updatedConfig)
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="showPagination" className="text-sm font-medium text-gray-700">
                      Show Pagination
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Product Detail Page Configuration */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📄 Product Detail Page</h3>
              <div className="space-y-6">
                {/* Inquiry Section */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-md font-medium text-gray-800 mb-4">💬 Inquiry Section</h4>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Section Title</label>
                      <Input
                        type="text"
                        value={config.products?.detail?.inquirySection?.title || ''}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.products) updatedConfig.products = {} as any
                          if (!updatedConfig.products.detail) updatedConfig.products.detail = {} as any
                          if (!updatedConfig.products.detail.inquirySection) updatedConfig.products.detail.inquirySection = {} as any
                          updatedConfig.products.detail.inquirySection.title = e.target.value
                          setConfig(updatedConfig)
                        }}
                        placeholder="Interested in this product?"
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        value={config.products?.detail?.inquirySection?.description || ''}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.products) updatedConfig.products = {} as any
                          if (!updatedConfig.products.detail) updatedConfig.products.detail = {} as any
                          if (!updatedConfig.products.detail.inquirySection) updatedConfig.products.detail.inquirySection = {} as any
                          updatedConfig.products.detail.inquirySection.description = e.target.value
                          setConfig(updatedConfig)
                        }}
                        rows={3}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Get detailed information, pricing, and availability for {productName}. Our team will respond to your inquiry within 24 hours."
                      />
                      <p className="text-xs text-gray-500">Use {'{productName}'} as placeholder for product name</p>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Features List</label>
                      <div className="space-y-2">
                        {(config.products?.detail?.inquirySection?.features || []).map((feature: string, index: number) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Input
                              type="text"
                              value={feature}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.products) updatedConfig.products = {} as any
                                if (!updatedConfig.products.detail) updatedConfig.products.detail = {} as any
                                if (!updatedConfig.products.detail.inquirySection) updatedConfig.products.detail.inquirySection = {} as any
                                if (!updatedConfig.products.detail.inquirySection.features) updatedConfig.products.detail.inquirySection.features = []
                                updatedConfig.products.detail.inquirySection.features[index] = e.target.value
                                setConfig(updatedConfig)
                              }}
                              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.products) updatedConfig.products = {} as any
                                if (!updatedConfig.products.detail) updatedConfig.products.detail = {} as any
                                if (!updatedConfig.products.detail.inquirySection) updatedConfig.products.detail.inquirySection = {} as any
                                if (!updatedConfig.products.detail.inquirySection.features) updatedConfig.products.detail.inquirySection.features = []
                                updatedConfig.products.detail.inquirySection.features.splice(index, 1)
                                setConfig(updatedConfig)
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.products) updatedConfig.products = {} as any
                            if (!updatedConfig.products.detail) updatedConfig.products.detail = {} as any
                            if (!updatedConfig.products.detail.inquirySection) updatedConfig.products.detail.inquirySection = {} as any
                            if (!updatedConfig.products.detail.inquirySection.features) updatedConfig.products.detail.inquirySection.features = []
                            updatedConfig.products.detail.inquirySection.features.push('New feature')
                            setConfig(updatedConfig)
                          }}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          + Add Feature
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* About Tab */}
        {activeTab === 'about' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🏠 Hero Section</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Hero Title</label>
                    <Input
                      type="text"
                      value={config.about?.hero?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.hero) updatedConfig.about.hero = {} as any
                        updatedConfig.about.hero.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="About InstAI CMS"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Hero Subtitle</label>
                    <Input
                      type="text"
                      value={config.about?.hero?.subtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.hero) updatedConfig.about.hero = {} as any
                        updatedConfig.about.hero.subtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="A Decade of Enterprise Excellence"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Hero Description</label>
                  <textarea
                    value={config.about?.hero?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.hero) updatedConfig.about.hero = {} as any
                      updatedConfig.about.hero.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Since 2014, InstAI CMS has been at the forefront of enterprise content management..."
                  />
                </div>
              </div>
            </div>

            {/* Company Story Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📖 Company Story</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.about?.company?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.company) updatedConfig.about.company = {} as any
                      updatedConfig.about.company.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="Our Story"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Founding Story Title</label>
                    <Input
                      type="text"
                      value={config.about?.company?.foundingStory?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.company) updatedConfig.about.company = {} as any
                        if (!updatedConfig.about.company.foundingStory) updatedConfig.about.company.foundingStory = {} as any
                        updatedConfig.about.company.foundingStory.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="Founded in 2014"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Founding Story Description</label>
                    <textarea
                      value={config.about?.company?.foundingStory?.description || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.company) updatedConfig.about.company = {} as any
                        if (!updatedConfig.about.company.foundingStory) updatedConfig.about.company.foundingStory = {} as any
                        updatedConfig.about.company.foundingStory.description = e.target.value
                        setConfig(updatedConfig)
                      }}
                      rows={3}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="InstAI CMS was founded with a vision..."
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Mission & Values Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🎯 Mission & Values</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.about?.mission?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                      updatedConfig.about.mission.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="Mission & Values"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Mission Items</label>
                  <div className="space-y-3">
                    {(config.about?.mission?.items || []).map((item: any, index: number) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Title</label>
                            <Input
                              type="text"
                              value={item.title || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                                if (!updatedConfig.about.mission.items) updatedConfig.about.mission.items = []
                                updatedConfig.about.mission.items[index] = { ...item, title: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              placeholder="Our Mission"
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div className="space-y-2">
                            <MediaInput
                              label="Image"
                              value={item.image || ''}
                              onChange={(url) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                                if (!updatedConfig.about.mission.items) updatedConfig.about.mission.items = []
                                updatedConfig.about.mission.items[index] = { ...item, image: url }
                                setConfig(updatedConfig)
                              }}
                              placeholder="Select or enter image URL"
                              description="Choose an image to represent this mission item (SVG, PNG, JPG supported)"
                              allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']}
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Description</label>
                            <textarea
                              value={item.description || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                                if (!updatedConfig.about.mission.items) updatedConfig.about.mission.items = []
                                updatedConfig.about.mission.items[index] = { ...item, description: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              rows={3}
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                              placeholder="To empower enterprises..."
                            />
                          </div>
                        </div>
                        <div className="mt-3 flex justify-end">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.about) updatedConfig.about = {} as any
                              if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                              if (!updatedConfig.about.mission.items) updatedConfig.about.mission.items = []
                              updatedConfig.about.mission.items.splice(index, 1)
                              setConfig(updatedConfig)
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.mission) updatedConfig.about.mission = {} as any
                        if (!updatedConfig.about.mission.items) updatedConfig.about.mission.items = []
                        updatedConfig.about.mission.items.push({
                          title: 'New Mission Item',
                          image: '/images/mission-default.svg',
                          description: 'Description for the new mission item'
                        })
                        setConfig(updatedConfig)
                      }}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      + Add Mission Item
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📊 Statistics</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.about?.statistics?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.statistics) updatedConfig.about.statistics = {} as any
                      updatedConfig.about.statistics.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="By the Numbers"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Statistics Items</label>
                  <div className="space-y-3">
                    {(config.about?.statistics?.items || []).map((item: any, index: number) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Value</label>
                            <Input
                              type="text"
                              value={item.value || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.statistics) updatedConfig.about.statistics = {} as any
                                if (!updatedConfig.about.statistics.items) updatedConfig.about.statistics.items = []
                                updatedConfig.about.statistics.items[index] = { ...item, value: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              placeholder="10+"
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Label</label>
                            <Input
                              type="text"
                              value={item.label || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.statistics) updatedConfig.about.statistics = {} as any
                                if (!updatedConfig.about.statistics.items) updatedConfig.about.statistics.items = []
                                updatedConfig.about.statistics.items[index] = { ...item, label: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              placeholder="Years of Excellence"
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                        <div className="mt-3 flex justify-end">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.about) updatedConfig.about = {} as any
                              if (!updatedConfig.about.statistics) updatedConfig.about.statistics = {} as any
                              if (!updatedConfig.about.statistics.items) updatedConfig.about.statistics.items = []
                              updatedConfig.about.statistics.items.splice(index, 1)
                              setConfig(updatedConfig)
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.statistics) updatedConfig.about.statistics = {} as any
                        if (!updatedConfig.about.statistics.items) updatedConfig.about.statistics.items = []
                        updatedConfig.about.statistics.items.push({
                          value: '100+',
                          label: 'New Statistic'
                        })
                        setConfig(updatedConfig)
                      }}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      + Add Statistic
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Features Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">⭐ Features</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.about?.features?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                      updatedConfig.about.features.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="Why Choose InstAI CMS"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Feature Items</label>
                  <div className="space-y-3">
                    {(config.about?.features?.items || []).map((item: any, index: number) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Title</label>
                            <Input
                              type="text"
                              value={item.title || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                                if (!updatedConfig.about.features.items) updatedConfig.about.features.items = []
                                updatedConfig.about.features.items[index] = { ...item, title: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              placeholder="Enterprise Security"
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div className="space-y-2">
                            <MediaInput
                              label="Image"
                              value={item.image || ''}
                              onChange={(url) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                                if (!updatedConfig.about.features.items) updatedConfig.about.features.items = []
                                updatedConfig.about.features.items[index] = { ...item, image: url }
                                setConfig(updatedConfig)
                              }}
                              placeholder="Select or enter image URL"
                              description="Choose an image to represent this feature (SVG, PNG, JPG supported)"
                              allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']}
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">Description</label>
                            <textarea
                              value={item.description || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (!updatedConfig.about) updatedConfig.about = {} as any
                                if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                                if (!updatedConfig.about.features.items) updatedConfig.about.features.items = []
                                updatedConfig.about.features.items[index] = { ...item, description: e.target.value }
                                setConfig(updatedConfig)
                              }}
                              rows={3}
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Bank-level security with advanced encryption..."
                            />
                          </div>
                        </div>
                        <div className="mt-3 flex justify-end">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const updatedConfig = { ...config }
                              if (!updatedConfig.about) updatedConfig.about = {} as any
                              if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                              if (!updatedConfig.about.features.items) updatedConfig.about.features.items = []
                              updatedConfig.about.features.items.splice(index, 1)
                              setConfig(updatedConfig)
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.about) updatedConfig.about = {} as any
                        if (!updatedConfig.about.features) updatedConfig.about.features = {} as any
                        if (!updatedConfig.about.features.items) updatedConfig.about.features.items = []
                        updatedConfig.about.features.items.push({
                          title: 'New Feature',
                          image: '/images/feature-default.svg',
                          description: 'Description for the new feature'
                        })
                        setConfig(updatedConfig)
                      }}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      + Add Feature
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to Action Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📢 Call to Action</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">CTA Title</label>
                  <Input
                    type="text"
                    value={config.about?.cta?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                      updatedConfig.about.cta.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="Ready to Experience Enterprise-Grade CMS?"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">CTA Description</label>
                  <textarea
                    value={config.about?.cta?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.about) updatedConfig.about = {} as any
                      if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                      updatedConfig.about.cta.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Join the hundreds of enterprises who trust InstAI CMS..."
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-3 bg-gray-50 rounded border">
                    <h5 className="text-sm font-medium text-gray-800 mb-3">Primary Button</h5>
                    <div className="space-y-2">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Text</label>
                        <Input
                          type="text"
                          value={config.about?.cta?.primaryButton?.text || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.about) updatedConfig.about = {} as any
                            if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                            if (!updatedConfig.about.cta.primaryButton) updatedConfig.about.cta.primaryButton = {} as any
                            updatedConfig.about.cta.primaryButton.text = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="Contact Our Team"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Link</label>
                        <Input
                          type="text"
                          value={config.about?.cta?.primaryButton?.link || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.about) updatedConfig.about = {} as any
                            if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                            if (!updatedConfig.about.cta.primaryButton) updatedConfig.about.cta.primaryButton = {} as any
                            updatedConfig.about.cta.primaryButton.link = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="/contact"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded border">
                    <h5 className="text-sm font-medium text-gray-800 mb-3">Secondary Button</h5>
                    <div className="space-y-2">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Text</label>
                        <Input
                          type="text"
                          value={config.about?.cta?.secondaryButton?.text || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.about) updatedConfig.about = {} as any
                            if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                            if (!updatedConfig.about.cta.secondaryButton) updatedConfig.about.cta.secondaryButton = {} as any
                            updatedConfig.about.cta.secondaryButton.text = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="Explore Solutions"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Button Link</label>
                        <Input
                          type="text"
                          value={config.about?.cta?.secondaryButton?.link || ''}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (!updatedConfig.about) updatedConfig.about = {} as any
                            if (!updatedConfig.about.cta) updatedConfig.about.cta = {} as any
                            if (!updatedConfig.about.cta.secondaryButton) updatedConfig.about.cta.secondaryButton = {} as any
                            updatedConfig.about.cta.secondaryButton.link = e.target.value
                            setConfig(updatedConfig)
                          }}
                          placeholder="/products"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Blog Tab */}
        {activeTab === 'blog' && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">📝 Blog Page Configuration</h3>
            <div className="space-y-6">
              {/* Header Section */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Header Section</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Page Title</label>
                    <Input
                      type="text"
                      value={config.blog?.header?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.blog) updatedConfig.blog = {} as any
                        if (!updatedConfig.blog.header) updatedConfig.blog.header = {} as any
                        updatedConfig.blog.header.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Knowledge Center"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Page Subtitle</label>
                    <Input
                      type="text"
                      value={config.blog?.header?.subtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.blog) updatedConfig.blog = {} as any
                        if (!updatedConfig.blog.header) updatedConfig.blog.header = {} as any
                        updatedConfig.blog.header.subtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Insights & Best Practices"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Page Description</label>
                  <textarea
                    rows={3}
                    value={config.blog?.header?.description || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.blog) updatedConfig.blog = {} as any
                      if (!updatedConfig.blog.header) updatedConfig.blog.header = {} as any
                      updatedConfig.blog.header.description = e.target.value
                      setConfig(updatedConfig)
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Stay informed with the latest insights, industry trends, and best practices in enterprise content management from our team of experts."
                  />
                </div>
              </div>

              {/* Display Settings */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Display Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Posts Per Page</label>
                    <Input
                      type="number"
                      min="5"
                      max="20"
                      value={config.blog?.display?.itemsPerPage || 10}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.blog) updatedConfig.blog = {} as any
                        if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                        updatedConfig.blog.display.itemsPerPage = parseInt(e.target.value)
                        setConfig(updatedConfig)
                      }}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.blog?.display?.showCategories ?? true}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.blog) updatedConfig.blog = {} as any
                          if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                          updatedConfig.blog.display.showCategories = e.target.checked
                          setConfig(updatedConfig)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Categories</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.blog?.display?.showAuthor ?? true}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.blog) updatedConfig.blog = {} as any
                          if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                          updatedConfig.blog.display.showAuthor = e.target.checked
                          setConfig(updatedConfig)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Author</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.blog?.display?.showDate ?? true}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.blog) updatedConfig.blog = {} as any
                          if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                          updatedConfig.blog.display.showDate = e.target.checked
                          setConfig(updatedConfig)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Date</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.blog?.display?.showTags ?? true}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.blog) updatedConfig.blog = {} as any
                          if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                          updatedConfig.blog.display.showTags = e.target.checked
                          setConfig(updatedConfig)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Tags</span>
                    </label>
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.blog?.display?.showComments ?? true}
                        onChange={(e) => {
                          const updatedConfig = { ...config }
                          if (!updatedConfig.blog) updatedConfig.blog = {} as any
                          if (!updatedConfig.blog.display) updatedConfig.blog.display = {} as any
                          updatedConfig.blog.display.showComments = e.target.checked
                          setConfig(updatedConfig)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Show Comments</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Contact Tab */}
        {activeTab === 'contact' && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">📞 Contact Page Configuration</h3>

            {/* Header Section */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
                📝 Page Header
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Page Title</label>
                  <Input
                    type="text"
                    value={config.contact?.header?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.header) updatedConfig.contact.header = {} as any
                      updatedConfig.contact.header.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Contact Our Team"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Subtitle</label>
                  <Input
                    type="text"
                    value={config.contact?.header?.subtitle || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.header) updatedConfig.contact.header = {} as any
                      updatedConfig.contact.header.subtitle = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Professional Support & Consultation"
                  />
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={config.contact?.header?.description || ''}
                  onChange={(e) => {
                    const updatedConfig = { ...config }
                    if (!updatedConfig.contact) updatedConfig.contact = {} as any
                    if (!updatedConfig.contact.header) updatedConfig.contact.header = {} as any
                    updatedConfig.contact.header.description = e.target.value
                    setConfig(updatedConfig)
                  }}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., With over 10 years of experience serving enterprise clients, our team is ready to help you find the perfect content management solution for your organization."
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
                📍 Contact Information
              </h4>

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Company Address</label>
                  <textarea
                    value={config.contact?.info?.address || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.info) updatedConfig.contact.info = {} as any
                      updatedConfig.contact.info.address = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., InstAI Technology Center&#10;1234 Enterprise Boulevard&#10;Suite 500&#10;San Francisco, CA 94105"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Working Hours</label>
                  <textarea
                    value={config.contact?.info?.workingHours || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.info) updatedConfig.contact.info = {} as any
                      updatedConfig.contact.info.workingHours = e.target.value
                      setConfig(updatedConfig)
                    }}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Mon - Fri: 8:00 AM - 7:00 PM PST&#10;Saturday: 9:00 AM - 5:00 PM PST&#10;Sunday: Emergency Support Only"
                  />
                </div>
              </div>

              {/* Contact Groups */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-semibold text-gray-700">Contact Groups</h5>
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.info) updatedConfig.contact.info = {} as any
                      if (!updatedConfig.contact.info.contactGroups) updatedConfig.contact.info.contactGroups = []

                      const newGroup = {
                        id: `group-${Date.now()}`,
                        name: 'New Contact Group',
                        description: 'Description for this contact group',
                        phones: [{ label: 'Phone', number: '', description: '' }],
                        emails: [{ label: 'Email', address: '', description: '' }]
                      }
                      updatedConfig.contact.info.contactGroups.push(newGroup)
                      setConfig(updatedConfig)
                    }}
                  >
                    + Add Group
                  </Button>
                </div>

                {config.contact?.info?.contactGroups?.map((group, groupIndex) => (
                  <div key={group.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <h6 className="font-medium text-gray-800">Contact Group {groupIndex + 1}</h6>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const updatedConfig = { ...config }
                          if (updatedConfig.contact?.info?.contactGroups) {
                            updatedConfig.contact.info.contactGroups.splice(groupIndex, 1)
                            setConfig(updatedConfig)
                          }
                        }}
                      >
                        Remove
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Group Name</label>
                        <Input
                          type="text"
                          value={group.name}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]) {
                              updatedConfig.contact.info.contactGroups[groupIndex].name = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="e.g., Sales Department"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Description</label>
                        <Input
                          type="text"
                          value={group.description}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]) {
                              updatedConfig.contact.info.contactGroups[groupIndex].description = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="e.g., For sales inquiries and product information"
                        />
                      </div>
                    </div>

                    {/* Phones */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">Phone Numbers</label>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]) {
                              updatedConfig.contact.info.contactGroups[groupIndex].phones.push({
                                label: 'Phone',
                                number: '',
                                description: ''
                              })
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          + Add Phone
                        </Button>
                      </div>
                      {group.phones.map((phone, phoneIndex) => (
                        <div key={phoneIndex} className="grid grid-cols-12 gap-2 mb-2">
                          <div className="col-span-3">
                            <Input
                              type="text"
                              value={phone.label}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.phones?.[phoneIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].phones[phoneIndex].label = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Label"
                            />
                          </div>
                          <div className="col-span-3">
                            <Input
                              type="text"
                              value={phone.number}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.phones?.[phoneIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].phones[phoneIndex].number = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Phone number"
                            />
                          </div>
                          <div className="col-span-5">
                            <Input
                              type="text"
                              value={phone.description || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.phones?.[phoneIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].phones[phoneIndex].description = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Description (optional)"
                            />
                          </div>
                          <div className="col-span-1">
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.phones) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].phones.splice(phoneIndex, 1)
                                  setConfig(updatedConfig)
                                }
                              }}
                            >
                              ×
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Emails */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">Email Addresses</label>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]) {
                              updatedConfig.contact.info.contactGroups[groupIndex].emails.push({
                                label: 'Email',
                                address: '',
                                description: ''
                              })
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          + Add Email
                        </Button>
                      </div>
                      {group.emails.map((email, emailIndex) => (
                        <div key={emailIndex} className="grid grid-cols-12 gap-2 mb-2">
                          <div className="col-span-3">
                            <Input
                              type="text"
                              value={email.label}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.emails?.[emailIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].emails[emailIndex].label = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Label"
                            />
                          </div>
                          <div className="col-span-3">
                            <Input
                              type="email"
                              value={email.address}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.emails?.[emailIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].emails[emailIndex].address = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Email address"
                            />
                          </div>
                          <div className="col-span-5">
                            <Input
                              type="text"
                              value={email.description || ''}
                              onChange={(e) => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.emails?.[emailIndex]) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].emails[emailIndex].description = e.target.value
                                  setConfig(updatedConfig)
                                }
                              }}
                              placeholder="Description (optional)"
                            />
                          </div>
                          <div className="col-span-1">
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const updatedConfig = { ...config }
                                if (updatedConfig.contact?.info?.contactGroups?.[groupIndex]?.emails) {
                                  updatedConfig.contact.info.contactGroups[groupIndex].emails.splice(emailIndex, 1)
                                  setConfig(updatedConfig)
                                }
                              }}
                            >
                              ×
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Social Media Settings */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
                🌐 Social Media Settings
              </h4>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.contact?.socialMedia?.showSocialMedia ?? true}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.socialMedia) updatedConfig.contact.socialMedia = {} as any
                      updatedConfig.contact.socialMedia.showSocialMedia = e.target.checked
                      setConfig(updatedConfig)
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Show Social Media Links</span>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Section Title</label>
                  <Input
                    type="text"
                    value={config.contact?.socialMedia?.title || ''}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.socialMedia) updatedConfig.contact.socialMedia = {} as any
                      updatedConfig.contact.socialMedia.title = e.target.value
                      setConfig(updatedConfig)
                    }}
                    placeholder="e.g., Connect With Us"
                  />
                </div>

                {/* Social Media Platforms */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">Social Media Platforms</label>
                  {config.contact?.socialMedia?.platforms?.map((platform, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={platform.enabled}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.socialMedia?.platforms?.[index]) {
                              updatedConfig.contact.socialMedia.platforms[index].enabled = e.target.checked
                              setConfig(updatedConfig)
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="col-span-2">
                        <Input
                          type="text"
                          value={platform.name}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.socialMedia?.platforms?.[index]) {
                              updatedConfig.contact.socialMedia.platforms[index].name = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Platform name"
                        />
                      </div>
                      <div className="col-span-1">
                        <Input
                          type="text"
                          value={platform.icon}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.socialMedia?.platforms?.[index]) {
                              updatedConfig.contact.socialMedia.platforms[index].icon = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="Icon"
                        />
                      </div>
                      <div className="col-span-7">
                        <Input
                          type="url"
                          value={platform.url}
                          onChange={(e) => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.socialMedia?.platforms?.[index]) {
                              updatedConfig.contact.socialMedia.platforms[index].url = e.target.value
                              setConfig(updatedConfig)
                            }
                          }}
                          placeholder="URL"
                        />
                      </div>
                      <div className="col-span-1">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.socialMedia?.platforms) {
                              updatedConfig.contact.socialMedia.platforms.splice(index, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.socialMedia) updatedConfig.contact.socialMedia = {} as any
                      if (!updatedConfig.contact.socialMedia.platforms) updatedConfig.contact.socialMedia.platforms = []

                      updatedConfig.contact.socialMedia.platforms.push({
                        name: 'New Platform',
                        url: '',
                        icon: '🔗',
                        enabled: true
                      })
                      setConfig(updatedConfig)
                    }}
                  >
                    + Add Platform
                  </Button>
                </div>
              </div>
            </div>

            {/* Contact Form Settings */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
                📝 Contact Form Settings
              </h4>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.contact?.form?.showForm ?? true}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.form) updatedConfig.contact.form = {} as any
                      updatedConfig.contact.form.showForm = e.target.checked
                      setConfig(updatedConfig)
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Show Contact Form</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Form Title</label>
                    <Input
                      type="text"
                      value={config.contact?.form?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.contact) updatedConfig.contact = {} as any
                        if (!updatedConfig.contact.form) updatedConfig.contact.form = {} as any
                        updatedConfig.contact.form.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Send us an inquiry"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Form Subtitle</label>
                    <Input
                      type="text"
                      value={config.contact?.form?.subtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.contact) updatedConfig.contact = {} as any
                        if (!updatedConfig.contact.form) updatedConfig.contact.form = {} as any
                        updatedConfig.contact.form.subtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Fill out the form below and we'll get back to you as soon as possible."
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* FAQ Settings */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
                ❓ FAQ Settings
              </h4>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.contact?.faq?.showFaq ?? true}
                    onChange={(e) => {
                      const updatedConfig = { ...config }
                      if (!updatedConfig.contact) updatedConfig.contact = {} as any
                      if (!updatedConfig.contact.faq) updatedConfig.contact.faq = {} as any
                      updatedConfig.contact.faq.showFaq = e.target.checked
                      setConfig(updatedConfig)
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Show FAQ Section</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">FAQ Title</label>
                    <Input
                      type="text"
                      value={config.contact?.faq?.title || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.contact) updatedConfig.contact = {} as any
                        if (!updatedConfig.contact.faq) updatedConfig.contact.faq = {} as any
                        updatedConfig.contact.faq.title = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Frequently Asked Questions"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">FAQ Subtitle</label>
                    <Input
                      type="text"
                      value={config.contact?.faq?.subtitle || ''}
                      onChange={(e) => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.contact) updatedConfig.contact = {} as any
                        if (!updatedConfig.contact.faq) updatedConfig.contact.faq = {} as any
                        updatedConfig.contact.faq.subtitle = e.target.value
                        setConfig(updatedConfig)
                      }}
                      placeholder="e.g., Find answers to common questions about our services"
                    />
                  </div>
                </div>

                {/* FAQ Questions */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">FAQ Questions</label>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => {
                        const updatedConfig = { ...config }
                        if (!updatedConfig.contact) updatedConfig.contact = {} as any
                        if (!updatedConfig.contact.faq) updatedConfig.contact.faq = {} as any
                        if (!updatedConfig.contact.faq.questions) updatedConfig.contact.faq.questions = []

                        updatedConfig.contact.faq.questions.push({
                          question: 'New Question',
                          answer: 'Answer to the question'
                        })
                        setConfig(updatedConfig)
                      }}
                    >
                      + Add Question
                    </Button>
                  </div>
                  {config.contact?.faq?.questions?.map((faq, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h6 className="font-medium text-gray-800">Question {index + 1}</h6>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const updatedConfig = { ...config }
                            if (updatedConfig.contact?.faq?.questions) {
                              updatedConfig.contact.faq.questions.splice(index, 1)
                              setConfig(updatedConfig)
                            }
                          }}
                        >
                          Remove
                        </Button>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Question</label>
                          <Input
                            type="text"
                            value={faq.question}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (updatedConfig.contact?.faq?.questions?.[index]) {
                                updatedConfig.contact.faq.questions[index].question = e.target.value
                                setConfig(updatedConfig)
                              }
                            }}
                            placeholder="Enter the question"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                          <textarea
                            value={faq.answer}
                            onChange={(e) => {
                              const updatedConfig = { ...config }
                              if (updatedConfig.contact?.faq?.questions?.[index]) {
                                updatedConfig.contact.faq.questions[index].answer = e.target.value
                                setConfig(updatedConfig)
                              }
                            }}
                            rows={3}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter the answer"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Save Button */}
      <div className="mt-8 flex justify-end space-x-4">
        <Button
          onClick={handleReset}
          variant="outline"
          disabled={saving}
        >
          Reset to Defaults
        </Button>
        <Button
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
