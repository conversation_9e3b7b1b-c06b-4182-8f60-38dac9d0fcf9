'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { MediaWithRelations } from '@/app/types'

export default function MediaPage() {
  const [media, setMedia] = useState<MediaWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [search, setSearch] = useState('')
  const [filter, setFilter] = useState('all')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])

  useEffect(() => {
    fetchMedia()
  }, [search, filter])

  const fetchMedia = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (search) {
        params.append('search', search)
      }
      if (filter !== 'all') {
        params.append('type', filter)
      }

      const response = await fetch(`/api/media?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        // Ensure data is an array and has proper structure
        const mediaData = Array.isArray(data.data) ? data.data : []
        setMedia(mediaData)
      } else {
        console.error('Error fetching media:', data.error)
        setMedia([]) // Set empty array on error
      }
      setLoading(false)
    } catch (error) {
      console.error('Error fetching media:', error)
      setLoading(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setUploading(true)

    try {
      for (const file of Array.from(files)) {
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        const data = await response.json()
        if (!data.success) {
          console.error('Upload failed:', data.error)
          alert(`Failed to upload ${file.name}: ${data.error}`)
        }
      }

      fetchMedia() // Refresh the list
    } catch (error) {
      console.error('Upload error:', error)
      alert('Upload failed')
    } finally {
      setUploading(false)
      // Reset the input
      event.target.value = ''
    }
  }

  const handleDelete = async (mediaId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return
    }

    try {
      const response = await fetch(`/api/media/${mediaId}`, {
        method: 'DELETE',
      })
      const data = await response.json()

      if (data.success) {
        fetchMedia() // Refresh the list
      } else {
        alert('Error deleting file: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting file:', error)
      alert('Error deleting file')
    }
  }

  const handleBulkDelete = async () => {
    if (selectedFiles.length === 0) return
    
    if (!confirm(`Are you sure you want to delete ${selectedFiles.length} files?`)) {
      return
    }

    try {
      await Promise.all(
        selectedFiles.map(id => 
          fetch(`/api/media/${id}`, { method: 'DELETE' })
        )
      )
      setSelectedFiles([])
      fetchMedia()
    } catch (error) {
      console.error('Error deleting files:', error)
      alert('Error deleting files')
    }
  }

  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    )
  }

  const selectAllFiles = () => {
    setSelectedFiles(media.map(file => file.id))
  }

  const clearSelection = () => {
    setSelectedFiles([])
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getFileIcon = (mimeType: string) => {
    if (!mimeType) return '📁'
    if (mimeType.startsWith('image/')) return '🖼️'
    if (mimeType.startsWith('video/')) return '🎥'
    if (mimeType.startsWith('audio/')) return '🎵'
    if (mimeType.includes('pdf')) return '📄'
    if (mimeType.includes('document') || mimeType.includes('word')) return '📝'
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return '📊'
    return '📁'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Media Library</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your uploaded files and media
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {selectedFiles.length > 0 && (
            <button
              onClick={handleBulkDelete}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
            >
              Delete Selected ({selectedFiles.length})
            </button>
          )}
          <label className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 cursor-pointer">
            {uploading ? 'Uploading...' : 'Upload Files'}
            <input
              type="file"
              multiple
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx"
              onChange={handleFileUpload}
              disabled={uploading}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-4 sm:space-y-0">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search files..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="flex space-x-2">
            {['all', 'image', 'video', 'audio', 'document'].map((type) => (
              <button
                key={type}
                onClick={() => setFilter(type)}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  filter === type
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Selection Controls */}
        {media.length > 0 && (
          <div className="flex items-center space-x-4 text-sm">
            <button
              onClick={selectAllFiles}
              className="text-blue-600 hover:text-blue-800"
            >
              Select All
            </button>
            {selectedFiles.length > 0 && (
              <button
                onClick={clearSelection}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear Selection
              </button>
            )}
            <span className="text-gray-500">
              {selectedFiles.length} of {media.length} selected
            </span>
          </div>
        )}
      </div>

      {/* Media Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {media.map((file) => {
          // Ensure file has required properties
          if (!file || !file.id || !file.filename) {
            return null
          }

          return (
          <div
            key={file.id}
            className={`relative bg-white rounded-lg shadow-sm border p-2 cursor-pointer transition-all ${
              selectedFiles.includes(file.id) 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:shadow-md'
            }`}
            onClick={() => toggleFileSelection(file.id)}
          >
            {/* Selection Checkbox */}
            <div className="absolute top-2 left-2 z-10">
              <input
                type="checkbox"
                checked={selectedFiles.includes(file.id)}
                onChange={() => toggleFileSelection(file.id)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            {/* File Preview */}
            <div className="aspect-square mb-2 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
              {file.mimeType && file.mimeType.startsWith('image/') ? (
                <Image
                  src={file.url}
                  alt={file.filename}
                  width={150}
                  height={150}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-4xl">
                  {getFileIcon(file.mimeType)}
                </div>
              )}
            </div>

            {/* File Info */}
            <div className="text-xs">
              <p className="font-medium text-gray-900 truncate" title={file.filename}>
                {file.filename}
              </p>
              <p className="text-gray-500 mt-1">
                {formatFileSize(file.size)}
              </p>
              <p className="text-gray-400">
                {formatDate(file.createdAt)}
              </p>
            </div>

            {/* Delete Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleDelete(file.id)
              }}
              className="absolute top-2 right-2 text-red-600 hover:text-red-800 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
              title="Delete file"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          )
        }).filter(Boolean)}
      </div>

      {media.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📁</div>
          <p className="text-gray-500 text-lg mb-2">No files found</p>
          <p className="text-gray-400 text-sm">Upload your first file to get started</p>
        </div>
      )}
    </div>
  )
}
