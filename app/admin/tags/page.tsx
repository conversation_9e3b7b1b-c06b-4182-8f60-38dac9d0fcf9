'use client'

import { useEffect, useState } from 'react'
import { TagWithRelations } from '@/app/types'
import { api } from '@/app/lib/api-client'

export default function TagsPage() {
  const [tags, setTags] = useState<TagWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    fetchTags()
  }, [search])

  const fetchTags = async () => {
    try {
      setLoading(true)
      const params: any = {}
      if (search) {
        params.search = search
      }

      const data = await api.getTags(params)

      if (data.success) {
        setTags(data.data)
      } else {
        console.error('Error fetching tags:', data.error)
      }
      setLoading(false)
    } catch (error) {
      console.error('Error fetching tags:', error)
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const handleDelete = async (tagId: string) => {
    if (!confirm('Are you sure you want to delete this tag?')) {
      return
    }

    try {
      const data = await api.deleteTag(tagId)

      if (data.success) {
        fetchTags() // Refresh the list
      } else {
        alert('Error deleting tag: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting tag:', error)
      alert('Error deleting tag')
    }
  }

  const getTagColor = (index: number) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-red-100 text-red-800',
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-gray-100 text-gray-800',
    ]
    return colors[index % colors.length]
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tags</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage tags for better content organization
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            Add New Tag
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search tags..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Tags Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {tags.map((tag, index) => (
          <div key={tag.id} className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between mb-2">
              <span className={`inline-flex px-2 py-1 text-sm font-medium rounded-full ${getTagColor(index)}`}>
                #{tag.name}
              </span>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="text-blue-600 hover:text-blue-900 text-sm"
                  title="Edit tag"
                >
                  ✏️
                </button>
                <button 
                  onClick={() => handleDelete(tag.id)}
                  className="text-red-600 hover:text-red-900 text-sm"
                  title="Delete tag"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              <p className="mb-1">Slug: /{tag.slug}</p>
              <p className="mb-1">Created: {formatDate(tag.createdAt)}</p>
              {tag._count && (
                <p className="font-medium text-gray-900">
                  {tag._count.posts} posts
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {tags.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🏷️</div>
          <p className="text-gray-500 text-lg mb-2">No tags found</p>
          <p className="text-gray-400 text-sm">Create your first tag to get started</p>
        </div>
      )}

      {/* Create Tag Modal */}
      {showCreateForm && (
        <TagModal
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false)
            fetchTags()
          }}
        />
      )}
    </div>
  )
}

// Tag Modal Component
interface TagModalProps {
  tag?: TagWithRelations
  onClose: () => void
  onSuccess: () => void
}

function TagModal({ tag, onClose, onSuccess }: TagModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: tag?.name || '',
    slug: tag?.slug || '',
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Auto-generate slug from name
    if (name === 'name' && !tag) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      setFormData(prev => ({ ...prev, slug }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const data = tag
        ? await api.updateTag(tag.id, formData)
        : await api.createTag(formData)

      if (data.success) {
        onSuccess()
      } else {
        alert('Error saving tag: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving tag:', error)
      alert('Error saving tag')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {tag ? 'Edit Tag' : 'Create New Tag'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Tag Name *
              </label>
              <input
                type="text"
                name="name"
                id="name"
                required
                value={formData.name}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., JavaScript, React, Tutorial"
              />
            </div>

            <div>
              <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                Slug *
              </label>
              <input
                type="text"
                name="slug"
                id="slug"
                required
                value={formData.slug}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="tag-slug"
              />
              <p className="mt-1 text-xs text-gray-500">
                URL-friendly version of the tag name
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (tag ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
