'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { api } from '@/app/lib/api-client'

interface Settings {
  [key: string]: {
    value: any
    type: string
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<Settings>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await api.getSettings()

      if (data.success) {
        setSettings(data.data)
      } else {
        console.error('Error fetching settings:', data.error)
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSettingChange = (key: string, value: any, type: string = 'string') => {
    setSettings(prev => ({
      ...prev,
      [key]: { value, type }
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const data = await api.updateSettings(settings)

      if (data.success) {
        alert('Settings saved successfully!')
      } else {
        alert('Error saving settings: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Error saving settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const tabs = [
    { id: 'general', label: 'General', icon: '⚙️' },
    { id: 'email', label: 'Email', icon: '📧' },
    { id: 'seo', label: 'SEO', icon: '🔍' },
    { id: 'social', label: 'Social Media', icon: '📱' },
  ]

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure your website settings and preferences
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        {activeTab === 'general' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Site Name
                </label>
                <Input
                  type="text"
                  value={settings.site_name?.value || ''}
                  onChange={(e) => handleSettingChange('site_name', e.target.value)}
                  placeholder="Your Site Name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Site URL
                </label>
                <Input
                  type="url"
                  value={settings.site_url?.value || ''}
                  onChange={(e) => handleSettingChange('site_url', e.target.value)}
                  placeholder="https://yoursite.com"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Site Description
                </label>
                <textarea
                  value={settings.site_description?.value || ''}
                  onChange={(e) => handleSettingChange('site_description', e.target.value)}
                  rows={3}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="A brief description of your website"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Email
                </label>
                <Input
                  type="email"
                  value={settings.admin_email?.value || ''}
                  onChange={(e) => handleSettingChange('admin_email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Timezone
                </label>
                <select
                  value={settings.timezone?.value || 'UTC'}
                  onChange={(e) => handleSettingChange('timezone', e.target.value)}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'email' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Email Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Host
                </label>
                <Input
                  type="text"
                  value={settings.smtp_host?.value || ''}
                  onChange={(e) => handleSettingChange('smtp_host', e.target.value)}
                  placeholder="smtp.gmail.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Port
                </label>
                <Input
                  type="number"
                  value={settings.smtp_port?.value || ''}
                  onChange={(e) => handleSettingChange('smtp_port', parseInt(e.target.value), 'number')}
                  placeholder="587"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Username
                </label>
                <Input
                  type="text"
                  value={settings.smtp_username?.value || ''}
                  onChange={(e) => handleSettingChange('smtp_username', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Password
                </label>
                <Input
                  type="password"
                  value={settings.smtp_password?.value || ''}
                  onChange={(e) => handleSettingChange('smtp_password', e.target.value)}
                  placeholder="Your SMTP password"
                />
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.smtp_secure?.value || false}
                    onChange={(e) => handleSettingChange('smtp_secure', e.target.checked, 'boolean')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Use SSL/TLS
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'seo' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">SEO Settings</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Meta Title
                </label>
                <Input
                  type="text"
                  value={settings.default_meta_title?.value || ''}
                  onChange={(e) => handleSettingChange('default_meta_title', e.target.value)}
                  placeholder="Your Site Name - Tagline"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Meta Description
                </label>
                <textarea
                  value={settings.default_meta_description?.value || ''}
                  onChange={(e) => handleSettingChange('default_meta_description', e.target.value)}
                  rows={3}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Default description for your website"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Keywords
                </label>
                <Input
                  type="text"
                  value={settings.default_keywords?.value || ''}
                  onChange={(e) => handleSettingChange('default_keywords', e.target.value)}
                  placeholder="keyword1, keyword2, keyword3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Google Analytics ID
                </label>
                <Input
                  type="text"
                  value={settings.google_analytics_id?.value || ''}
                  onChange={(e) => handleSettingChange('google_analytics_id', e.target.value)}
                  placeholder="GA-XXXXXXXXX-X"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Google Search Console
                </label>
                <Input
                  type="text"
                  value={settings.google_search_console?.value || ''}
                  onChange={(e) => handleSettingChange('google_search_console', e.target.value)}
                  placeholder="Verification code"
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'social' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Social Media</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Facebook URL
                </label>
                <Input
                  type="url"
                  value={settings.facebook_url?.value || ''}
                  onChange={(e) => handleSettingChange('facebook_url', e.target.value)}
                  placeholder="https://facebook.com/yourpage"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Twitter URL
                </label>
                <Input
                  type="url"
                  value={settings.twitter_url?.value || ''}
                  onChange={(e) => handleSettingChange('twitter_url', e.target.value)}
                  placeholder="https://twitter.com/youraccount"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  LinkedIn URL
                </label>
                <Input
                  type="url"
                  value={settings.linkedin_url?.value || ''}
                  onChange={(e) => handleSettingChange('linkedin_url', e.target.value)}
                  placeholder="https://linkedin.com/company/yourcompany"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Instagram URL
                </label>
                <Input
                  type="url"
                  value={settings.instagram_url?.value || ''}
                  onChange={(e) => handleSettingChange('instagram_url', e.target.value)}
                  placeholder="https://instagram.com/youraccount"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  YouTube URL
                </label>
                <Input
                  type="url"
                  value={settings.youtube_url?.value || ''}
                  onChange={(e) => handleSettingChange('youtube_url', e.target.value)}
                  placeholder="https://youtube.com/c/yourchannel"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  GitHub URL
                </label>
                <Input
                  type="url"
                  value={settings.github_url?.value || ''}
                  onChange={(e) => handleSettingChange('github_url', e.target.value)}
                  placeholder="https://github.com/youraccount"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
