'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { CommentWithRelations, CommentStatus } from '@/app/types'
import { api } from '@/app/lib/api-client'

export default function CommentsPage() {
  const [comments, setComments] = useState<CommentWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const [search, setSearch] = useState('')

  useEffect(() => {
    fetchComments()
  }, [filter, search])

  const fetchComments = async () => {
    try {
      setLoading(true)
      const params: any = {}
      if (filter !== 'all') {
        params.status = filter.toUpperCase()
      }
      if (search) {
        params.search = search
      }

      const data = await api.getComments(params)

      if (data.success) {
        setComments(data.data)
      } else {
        console.error('Error fetching comments:', data.error)
      }
      setLoading(false)
    } catch (error) {
      console.error('Error fetching comments:', error)
      setLoading(false)
    }
  }

  const updateCommentStatus = async (commentId: string, newStatus: string) => {
    try {
      const data = await api.updateComment(commentId, { status: newStatus })

      if (data.success) {
        fetchComments() // Refresh the list
      } else {
        alert('Error updating comment: ' + data.error)
      }
    } catch (error) {
      console.error('Error updating comment:', error)
      alert('Error updating comment')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-green-100 text-green-800',
      REJECTED: 'bg-red-100 text-red-800',
    }
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status}
      </span>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleDelete = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) {
      return
    }

    try {
      const data = await api.deleteComment(commentId)

      if (data.success) {
        fetchComments() // Refresh the list
      } else {
        alert('Error deleting comment: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting comment:', error)
      alert('Error deleting comment')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Comments</h1>
        <p className="mt-1 text-sm text-gray-500">
          Moderate and manage user comments
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-4 sm:space-y-0">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search comments..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="flex space-x-2">
            {['all', 'pending', 'approved', 'rejected'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status)}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  filter === status
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Comments List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {comments.map((comment) => (
            <li key={comment.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-sm font-medium text-gray-900">
                        {comment.author?.name || comment.guestName}
                      </h3>
                      {getStatusBadge(comment.status)}
                      {comment.parent && (
                        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                          Reply
                        </span>
                      )}
                    </div>
                    
                    <div className="mb-2 text-sm text-gray-600">
                      <p className="line-clamp-3">{comment.content}</p>
                    </div>

                    <div className="flex items-center text-xs text-gray-500 space-x-4">
                      <span>{comment.author?.email || comment.guestEmail}</span>
                      <span>{formatDate(comment.createdAt)}</span>
                      {comment.post && (
                        <Link 
                          href={`/blog/${comment.post.slug}`}
                          className="text-blue-600 hover:text-blue-800"
                          target="_blank"
                        >
                          {comment.post.title}
                        </Link>
                      )}
                      {comment._count?.replies > 0 && (
                        <span>{comment._count.replies} replies</span>
                      )}
                    </div>

                    {comment.parent && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-600">
                        <strong>Replying to:</strong> {comment.parent.content.substring(0, 100)}...
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col space-y-2 ml-4">
                    {comment.status === CommentStatus.PENDING && (
                      <>
                        <button
                          onClick={() => updateCommentStatus(comment.id, CommentStatus.APPROVED)}
                          className="text-green-600 hover:text-green-900 text-sm font-medium"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => updateCommentStatus(comment.id, CommentStatus.REJECTED)}
                          className="text-red-600 hover:text-red-900 text-sm font-medium"
                        >
                          Reject
                        </button>
                      </>
                    )}
                    
                    {comment.status === CommentStatus.APPROVED && (
                      <button
                        onClick={() => updateCommentStatus(comment.id, CommentStatus.REJECTED)}
                        className="text-red-600 hover:text-red-900 text-sm font-medium"
                      >
                        Reject
                      </button>
                    )}
                    
                    {comment.status === CommentStatus.REJECTED && (
                      <button
                        onClick={() => updateCommentStatus(comment.id, CommentStatus.APPROVED)}
                        className="text-green-600 hover:text-green-900 text-sm font-medium"
                      >
                        Approve
                      </button>
                    )}

                    <button 
                      onClick={() => handleDelete(comment.id)}
                      className="text-red-600 hover:text-red-900 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {comments.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No comments found.</p>
        </div>
      )}
    </div>
  )
}
