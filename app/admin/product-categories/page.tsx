'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { ProductCategoryWithRelations } from '@/app/types'

export default function ProductCategoriesPage() {
  const [categories, setCategories] = useState<ProductCategoryWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ProductCategoryWithRelations | null>(null)

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/product-categories?tree=true')
      const data = await response.json()

      if (data.success) {
        setCategories(data.data)
      } else {
        console.error('Error fetching categories:', data.error)
      }
      setLoading(false)
    } catch (error) {
      console.error('Error fetching categories:', error)
      setLoading(false)
    }
  }

  const handleDelete = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category?')) {
      return
    }

    try {
      const response = await fetch(`/api/product-categories/${categoryId}`, {
        method: 'DELETE',
      })
      const data = await response.json()

      if (data.success) {
        fetchCategories() // Refresh the list
      } else {
        alert('Error deleting category: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Error deleting category')
    }
  }

  const renderCategoryTree = (categories: ProductCategoryWithRelations[], level = 0) => {
    return categories.map((category) => (
      <div key={category.id} className={`${level > 0 ? 'ml-8' : ''}`}>
        <div className="flex items-center justify-between py-3 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-2 h-2 rounded-full ${level === 0 ? 'bg-blue-500' : level === 1 ? 'bg-green-500' : 'bg-yellow-500'}`} />
            <div>
              <h3 className="text-sm font-medium text-gray-900">{category.name}</h3>
              <p className="text-xs text-gray-500">
                {category._count?.products || 0} products
                {category._count?.children ? ` • ${category._count.children} subcategories` : ''}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setEditingCategory(category)}
              className="text-blue-600 hover:text-blue-900 text-sm font-medium"
            >
              Edit
            </button>
            <button
              onClick={() => handleDelete(category.id)}
              className="text-red-600 hover:text-red-900 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        </div>
        {category.children && category.children.length > 0 && (
          <div className="bg-gray-50">
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Product Categories</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your product category hierarchy
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            Add New Category
          </button>
        </div>
      </div>

      {/* Categories Tree */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {categories.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {renderCategoryTree(categories)}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No categories found.</p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Create Your First Category
            </button>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {(showCreateForm || editingCategory) && (
        <CategoryModal
          category={editingCategory}
          categories={categories}
          onClose={() => {
            setShowCreateForm(false)
            setEditingCategory(null)
          }}
          onSuccess={() => {
            setShowCreateForm(false)
            setEditingCategory(null)
            fetchCategories()
          }}
        />
      )}
    </div>
  )
}

// Category Modal Component
interface CategoryModalProps {
  category?: ProductCategoryWithRelations | null
  categories: ProductCategoryWithRelations[]
  onClose: () => void
  onSuccess: () => void
}

function CategoryModal({ category, categories, onClose, onSuccess }: CategoryModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: category?.name || '',
    slug: category?.slug || '',
    description: category?.description || '',
    image: category?.image || '',
    parentId: category?.parentId || '',
    seoTitle: category?.seoTitle || '',
    seoDescription: category?.seoDescription || '',
    seoKeywords: category?.seoKeywords || '',
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = category 
        ? `/api/product-categories/${category.id}`
        : '/api/product-categories'
      
      const method = category ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          parentId: formData.parentId || null,
        }),
      })

      const data = await response.json()

      if (data.success) {
        onSuccess()
      } else {
        alert('Error saving category: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving category:', error)
      alert('Error saving category')
    } finally {
      setLoading(false)
    }
  }

  // Flatten categories for parent selection (exclude current category and its descendants)
  const flattenCategories = (cats: ProductCategoryWithRelations[], exclude?: string): ProductCategoryWithRelations[] => {
    const result: ProductCategoryWithRelations[] = []
    
    for (const cat of cats) {
      if (cat.id !== exclude) {
        result.push(cat)
        if (cat.children) {
          result.push(...flattenCategories(cat.children, exclude))
        }
      }
    }
    
    return result
  }

  const availableParents = flattenCategories(categories, category?.id)

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {category ? 'Edit Category' : 'Create New Category'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Category Name *
              </label>
              <input
                type="text"
                name="name"
                id="name"
                required
                value={formData.name}
                onChange={handleNameChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                URL Slug
              </label>
              <input
                type="text"
                name="slug"
                id="slug"
                value={formData.slug}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="parentId" className="block text-sm font-medium text-gray-700">
                Parent Category
              </label>
              <select
                name="parentId"
                id="parentId"
                value={formData.parentId}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">No Parent (Root Category)</option>
                {availableParents.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                id="description"
                rows={3}
                value={formData.description}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (category ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
