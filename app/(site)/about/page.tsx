import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'

// 获取主题配置
async function getThemeConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store' // 确保获取最新配置
    })
    if (!response.ok) {
      throw new Error('Failed to fetch theme config')
    }
    const result = await response.json()
    return result.data || result
  } catch (error) {
    console.error('Error fetching theme config:', error)
    // 返回默认配置
    return {
      about: {
        hero: {
          title: 'About InstAI CMS',
          subtitle: 'A Decade of Enterprise Excellence',
          description: 'Since 2014, InstAI CMS has been at the forefront of enterprise content management, delivering robust, scalable solutions that power digital transformation for organizations worldwide.'
        },
        company: {
          title: 'Our Story',
          foundingStory: {
            title: 'Founded in 2014',
            description: 'InstAI CMS was founded with a vision to create enterprise-grade content management solutions that could scale with growing businesses while maintaining the highest standards of security and performance.'
          },
          timeline: []
        },
        mission: {
          title: 'Mission & Values',
          items: [
            {
              title: 'Our Mission',
              image: '/images/mission-target.svg',
              description: 'To empower enterprises with robust, scalable content management solutions that drive digital transformation and business growth.'
            },
            {
              title: 'Excellence',
              image: '/images/mission-excellence.svg',
              description: 'We maintain the highest standards in everything we do, from code quality to customer service, ensuring exceptional results.'
            },
            {
              title: 'Partnership',
              image: '/images/mission-partnership.svg',
              description: 'We build long-term partnerships with our clients, understanding their unique needs and growing alongside their businesses.'
            }
          ]
        },
        statistics: { title: 'By the Numbers', items: [] },
        features: {
          title: 'Why Choose InstAI CMS',
          items: [
            {
              title: 'Enterprise Security',
              image: '/images/feature-security.svg',
              description: 'Bank-level security with advanced encryption, multi-factor authentication, and comprehensive audit trails to protect your valuable content.'
            },
            {
              title: 'Scalable Architecture',
              image: '/images/feature-scalable.svg',
              description: 'Built to handle enterprise workloads with cloud-native architecture that scales seamlessly from thousands to millions of users.'
            },
            {
              title: 'Advanced Workflow',
              image: '/images/feature-workflow.svg',
              description: 'Sophisticated content workflows with approval processes, version control, and collaborative editing for complex publishing needs.'
            },
            {
              title: '24/7 Support',
              image: '/images/feature-support.svg',
              description: 'Dedicated enterprise support with guaranteed response times, technical account management, and priority issue resolution.'
            }
          ]
        },
        cta: {
          title: 'Ready to Experience Enterprise-Grade CMS?',
          description: 'Join the hundreds of enterprises who trust InstAI CMS for their content management needs.',
          primaryButton: { text: 'Contact Our Team', link: '/contact' },
          secondaryButton: { text: 'Explore Solutions', link: '/products' }
        }
      }
    }
  }
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettings()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  return {
    title: `About Us | ${siteName}`,
    description: 'Learn about our company - years of enterprise content management excellence. Our story, mission, and commitment to professional solutions.',
    openGraph: {
      title: `About Us | ${siteName}`,
      description: 'Learn about our company - years of enterprise content management excellence. Our story, mission, and commitment to professional solutions.',
      type: 'website',
    },
  }
}

export default async function AboutPage() {
  const themeConfig = await getThemeConfig()
  const config = themeConfig.about
  return (
    <div className="px-8 py-12">
      {/* Professional Header Section */}
      <div className="text-center mb-16">
        <div className="inline-block mb-6">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">{config.hero?.title || 'About InstAI CMS'}</h1>
          <p className="text-lg text-blue-600 font-semibold">{config.hero?.subtitle || 'A Decade of Enterprise Excellence'}</p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-blue-800 mx-auto mt-4"></div>
        </div>

        <div className="max-w-4xl mx-auto">
          <p className="text-xl text-gray-600 leading-relaxed">
            {config.hero?.description || 'Since 2014, InstAI CMS has been at the forefront of enterprise content management, delivering robust, scalable solutions that power digital transformation for organizations worldwide.'}
          </p>
        </div>
      </div>

      {/* Company Story Section */}
      <div className="mb-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">{config.company?.title || 'Our Story'}</h2>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-8 border">
                <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-6">
                  <span className="text-white text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{config.company?.foundingStory?.title || 'Founded in 2014'}</h3>
                <p className="text-gray-600 leading-relaxed">
                  {config.company?.foundingStory?.description || 'InstAI CMS was founded with a vision to create enterprise-grade content management solutions that could scale with growing businesses while maintaining the highest standards of security and performance.'}
                </p>
              </div>
            </div>

            <div className="space-y-6">
              {(config.company?.timeline || []).map((item: any, index: number) => (
                <div key={index} className="border-l-4 border-blue-600 pl-6">
                  <h4 className="font-bold text-gray-900 mb-2">{item.period}</h4>
                  <p className="text-gray-600 text-sm">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mission & Values Section */}
      <div className="mb-16 bg-gray-50 -mx-8 px-8 py-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-12 text-center">{config.mission?.title || 'Mission & Values'}</h2>

          <div className="grid md:grid-cols-3 gap-8">
            {(config.mission?.items || []).map((item: any, index: number) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 overflow-hidden">
                  {item.image ? (
                    <Image
                      src={item.image}
                      alt={item.title}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-2xl">🎯</span>
                  )}
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-3">{item.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Key Statistics */}
      <div className="mb-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-12 text-center">{config.statistics?.title || 'By the Numbers'}</h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {(config.statistics?.items || []).map((item: any, index: number) => (
              <div key={index} className="bg-white border rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">{item.value}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">{item.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Why Choose Us Section */}
      <div className="mb-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-12 text-center">{config.features?.title || 'Why Choose InstAI CMS'}</h2>

          <div className="grid md:grid-cols-2 gap-8">
            {(config.features?.items || []).map((item: any, index: number) => (
              <div key={index} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                    {item.image ? (
                      <Image
                        src={item.image}
                        alt={item.title}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-xl">⭐</span>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                    <p className="text-gray-600 text-sm">
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-lg p-12 text-center">
        <h2 className="text-2xl font-bold mb-4">
          {config.cta?.title || 'Ready to Experience Enterprise-Grade CMS?'}
        </h2>
        <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
          {config.cta?.description || 'Join the hundreds of enterprises who trust InstAI CMS for their content management needs. Let\'s discuss how we can help transform your digital operations.'}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={config.cta?.primaryButton?.link || '/contact'}
            className="bg-white text-blue-600 px-8 py-3 rounded-md font-semibold hover:bg-gray-100 transition-all shadow-lg"
          >
            {config.cta?.primaryButton?.text || 'Contact Our Team'}
          </Link>
          <Link
            href={config.cta?.secondaryButton?.link || '/products'}
            className="border-2 border-white text-white px-8 py-3 rounded-md font-semibold hover:bg-white hover:text-blue-600 transition-all"
          >
            {config.cta?.secondaryButton?.text || 'Explore Solutions'}
          </Link>
        </div>
      </div>
    </div>
  )
}
