'use client'

import { Suspense, useEffect, useState } from 'react'
import InquiryForm from '@/app/components/forms/InquiryForm'

interface ContactConfig {
  header: {
    title: string
    subtitle: string
    description: string
  }
  info: {
    address: string
    workingHours: string
    contactGroups: Array<{
      id: string
      name: string
      description: string
      phones: Array<{
        label: string
        number: string
        description?: string
      }>
      emails: Array<{
        label: string
        address: string
        description?: string
      }>
    }>
  }
  socialMedia: {
    showSocialMedia: boolean
    title: string
    platforms: Array<{
      name: string
      url: string
      icon: string
      enabled: boolean
    }>
  }
  form: {
    showForm: boolean
    title: string
    subtitle: string
  }
  faq: {
    showFaq: boolean
    title: string
    subtitle: string
    questions: Array<{
      question: string
      answer: string
    }>
  }
}

// Component to handle URL search params
function ContactFormWrapper({ formConfig }: { formConfig: ContactConfig['form'] }) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ContactFormWithParams formConfig={formConfig} />
    </Suspense>
  )
}

function ContactFormWithParams({ formConfig }: { formConfig: ContactConfig['form'] }) {
  // In a real app, you would use useSearchParams() here
  // For now, we'll handle this on the client side within the form component
  return (
    <InquiryForm
      className="max-w-2xl"
      customTitle={formConfig.title}
      customSubtitle={formConfig.subtitle}
    />
  )
}

export default function ContactPage() {
  const [contactConfig, setContactConfig] = useState<ContactConfig>({
    header: {
      title: 'Contact Our Team',
      subtitle: 'Professional Support & Consultation',
      description: 'With over 10 years of experience serving enterprise clients, our team is ready to help you find the perfect content management solution for your organization.'
    },
    info: {
      address: 'InstAI Technology Center\n1234 Enterprise Boulevard\nSuite 500\nSan Francisco, CA 94105',
      workingHours: 'Mon - Fri: 8:00 AM - 7:00 PM PST\nSaturday: 9:00 AM - 5:00 PM PST\nSunday: Emergency Support Only',
      contactGroups: [
        {
          id: 'general',
          name: 'General Support',
          description: 'For general inquiries and support',
          phones: [
            {
              label: 'Main Line',
              number: '+****************',
              description: 'Toll-free support line'
            }
          ],
          emails: [
            {
              label: 'General Support',
              address: '<EMAIL>',
              description: '24-hour response guarantee'
            }
          ]
        }
      ]
    },
    socialMedia: {
      showSocialMedia: true,
      title: 'Connect With Us',
      platforms: [
        {
          name: 'LinkedIn',
          url: 'https://linkedin.com/company/instai-cms',
          icon: '💼',
          enabled: true
        },
        {
          name: 'Twitter',
          url: 'https://twitter.com/instai_cms',
          icon: '🐦',
          enabled: true
        },
        {
          name: 'GitHub',
          url: 'https://github.com/instai-cms',
          icon: '🐙',
          enabled: true
        },
        {
          name: 'Email',
          url: 'mailto:<EMAIL>',
          icon: '📧',
          enabled: true
        }
      ]
    },
    form: {
      showForm: true,
      title: 'Send us an inquiry',
      subtitle: 'Fill out the form below and we\'ll get back to you as soon as possible.'
    },
    faq: {
      showFaq: true,
      title: 'Frequently Asked Questions',
      subtitle: 'Find answers to common questions about our services',
      questions: [
        {
          question: 'What is your response time for enterprise inquiries?',
          answer: 'We guarantee a response within 24 hours for all enterprise inquiries. Critical issues receive immediate attention with our dedicated support team available 24/7 for enterprise clients.'
        },
        {
          question: 'Do you offer custom development services?',
          answer: 'Yes, we provide comprehensive custom development services tailored to your specific business requirements. Our experienced team can build custom modules, integrations, and solutions.'
        }
      ]
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchContactConfig()
  }, [])

  const fetchContactConfig = async () => {
    try {
      const response = await fetch('/api/themes/config')
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data?.contact) {
          setContactConfig(result.data.contact)
        }
      }
    } catch (error) {
      console.error('Error fetching contact config:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="px-8 py-12">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-8 py-12">
      {/* Professional Header Section */}
      <div className="text-center mb-12">
        <div className="inline-block mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{contactConfig.header.title}</h1>
          <p className="text-lg text-blue-600 font-semibold">{contactConfig.header.subtitle}</p>
          <div className="w-20 h-1 bg-gradient-to-r from-blue-600 to-blue-800 mx-auto mt-4"></div>
        </div>

        <div className="max-w-3xl mx-auto">
          <p className="text-lg text-gray-600 leading-relaxed">
            {contactConfig.header.description}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        {/* Professional Contact Information */}
        <div className="lg:col-span-1">
          <div className="bg-gray-50 rounded-lg border p-8">
            <h2 className="text-xl font-bold text-gray-900 mb-8 pb-4 border-b border-gray-200">
              Contact Information
            </h2>

            <div className="space-y-8">
              {/* Corporate Headquarters */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-lg">🏢</span>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 mb-2">Corporate Headquarters</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {contactConfig.info.address.split('\n').map((line, index) => (
                      <span key={index}>
                        {line}
                        {index < contactConfig.info.address.split('\n').length - 1 && <br />}
                      </span>
                    ))}
                  </p>
                </div>
              </div>

              {/* Business Hours */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-lg">🕒</span>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 mb-2">Business Hours</h3>
                  <div className="text-gray-600 text-sm space-y-1">
                    {contactConfig.info.workingHours.split('\n').map((line, index) => (
                      <p key={index}>{line}</p>
                    ))}
                  </div>
                </div>
              </div>

              {/* Contact Groups */}
              {contactConfig.info.contactGroups?.map((group, groupIndex) => (
                <div key={group.id} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600 text-lg">
                      {groupIndex === 0 ? '📞' : groupIndex === 1 ? '💼' : '🔧'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 mb-2">{group.name}</h3>
                    <p className="text-gray-600 text-sm mb-3">{group.description}</p>

                    <div className="space-y-3">
                      {/* Phone Numbers */}
                      {group.phones.map((phone, phoneIndex) => (
                        <div key={phoneIndex} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">{phone.label}:</span>
                          <div className="text-right">
                            <a
                              href={`tel:${phone.number.replace(/[^\d+]/g, '')}`}
                              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                            >
                              {phone.number}
                            </a>
                            {phone.description && (
                              <p className="text-xs text-gray-500">{phone.description}</p>
                            )}
                          </div>
                        </div>
                      ))}

                      {/* Email Addresses */}
                      {group.emails.map((email, emailIndex) => (
                        <div key={emailIndex} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">{email.label}:</span>
                          <div className="text-right">
                            <a
                              href={`mailto:${email.address}`}
                              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                            >
                              {email.address}
                            </a>
                            {email.description && (
                              <p className="text-xs text-gray-500">{email.description}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Professional Social Links */}
            {contactConfig.socialMedia.showSocialMedia && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="font-bold text-gray-900 mb-4">{contactConfig.socialMedia.title}</h3>
                <div className="grid grid-cols-2 gap-3">
                  {contactConfig.socialMedia.platforms
                    .filter(platform => platform.enabled)
                    .map((platform, index) => (
                      <a
                        key={index}
                        href={platform.url}
                        className="flex items-center space-x-2 p-3 bg-white rounded-lg border hover:border-blue-300 hover:shadow-sm transition-all"
                        aria-label={platform.name}
                        target={platform.url.startsWith('mailto:') ? '_self' : '_blank'}
                        rel={platform.url.startsWith('mailto:') ? '' : 'noopener noreferrer'}
                      >
                        <span className="text-lg">{platform.icon}</span>
                        <span className="text-sm font-medium text-gray-700">{platform.name}</span>
                      </a>
                    ))}
                </div>
              </div>
            )}
          </div>


        </div>

        {/* Contact Form */}
        <div className="lg:col-span-2">
          {contactConfig.form.showForm && <ContactFormWrapper formConfig={contactConfig.form} />}
        </div>
      </div>

      {/* Professional FAQ Section */}
      {contactConfig.faq.showFaq && (
        <div className="mt-16 pt-12 border-t border-gray-200">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {contactConfig.faq.title}
            </h2>
            <p className="text-gray-600">
              {contactConfig.faq.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {contactConfig.faq.questions.map((faq, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 border">
                <h3 className="font-bold text-gray-900 mb-3 text-lg">
                  {faq.question}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
