'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import ReactMarkdown from 'react-markdown'
import { useRouter } from 'next/navigation'
import BlogPlaceholder from '@/app/components/ui/BlogPlaceholder'

interface Post {
  id: string
  title: string
  slug: string
  excerpt: string
  featuredImage?: string
  featured?: boolean
  publishedAt: string
  author: {
    name: string
    username: string
    avatar?: string
  }
  category?: {
    name: string
    slug: string
  }
  tags: Array<{
    name: string
    slug: string
  }>
  _count: {
    comments: number
  }
}

interface BlogConfig {
  header: {
    title: string
    subtitle: string
    description: string
  }
  display: {
    itemsPerPage: number
    showCategories: boolean
    showTags: boolean
    showAuthor: boolean
    showDate: boolean
    showComments: boolean
  }
}

interface BlogClientComponentProps {
  initialPosts: Post[]
  initialPagination: any
  blogConfig: BlogConfig
  initialPage: number
}

export default function BlogClientComponent({
  initialPosts,
  initialPagination,
  blogConfig,
  initialPage
}: BlogClientComponentProps) {
  const [posts] = useState<Post[]>(initialPosts)
  const [pagination] = useState(initialPagination)
  const [currentPage] = useState(initialPage)
  const router = useRouter()

  // 获取Featured Post - 优先选择标记为featured的文章，否则选择第一篇
  const featuredPost = posts.find(post => post.featured) || posts[0]
  // 获取其他文章（排除Featured Post）
  const otherPosts = featuredPost ? posts.filter(post => post.id !== featuredPost.id) : posts

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handlePageChange = (page: number) => {
    router.push(`/blog?page=${page}`)
  }

  return (
    <div className="px-8 py-12">
      {/* Professional Header Section */}
      <div className="text-center mb-16">
        <div className="inline-block mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{blogConfig.header.title}</h1>
          <p className="text-lg text-blue-600 font-semibold">{blogConfig.header.subtitle}</p>
          <div className="w-20 h-1 bg-gradient-to-r from-blue-600 to-blue-800 mx-auto mt-4"></div>
        </div>

        <div className="max-w-3xl mx-auto">
          <p className="text-lg text-gray-600 leading-relaxed">
            {blogConfig.header.description}
          </p>
        </div>
      </div>

      {/* Featured Post */}
      {featuredPost && (
        <div className="mb-16">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl overflow-hidden shadow-lg">
            <div className="grid md:grid-cols-2 gap-8 p-8">
              <div className="relative">
                {featuredPost.featuredImage ? (
                  <Image
                    src={featuredPost.featuredImage}
                    alt={featuredPost.title}
                    width={600}
                    height={400}
                    className="w-full h-64 object-cover rounded-lg shadow-md"
                  />
                ) : (
                  <BlogPlaceholder
                    variant="featured"
                    className="rounded-lg shadow-md"
                  />
                )}
                <div className="absolute top-4 left-4">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Featured
                  </span>
                </div>
              </div>
              <div className="flex flex-col justify-center">
                {blogConfig.display.showCategories && featuredPost.category && (
                  <div className="mb-3">
                    <Link
                      href={`/blog/category/${featuredPost.category.slug}`}
                      className="text-sm font-semibold text-blue-600 hover:text-blue-800"
                    >
                      {featuredPost.category.name}
                    </Link>
                  </div>
                )}
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  <Link
                    href={`/blog/${featuredPost.slug}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {featuredPost.title}
                  </Link>
                </h2>
                <div className="text-gray-600 mb-6 prose prose-lg max-w-none">
                  <ReactMarkdown
                    components={{
                      p: ({ children }) => <p className="text-gray-600 mb-4">{children}</p>,
                      strong: ({ children }) => <strong className="font-semibold text-gray-700">{children}</strong>,
                      em: ({ children }) => <em className="italic">{children}</em>,
                    }}
                  >
                    {featuredPost.excerpt || ''}
                  </ReactMarkdown>
                </div>
                <div className="flex items-center justify-between">
                  {(blogConfig.display.showAuthor || blogConfig.display.showDate) && (
                    <div className="flex items-center space-x-2">
                      {blogConfig.display.showAuthor && featuredPost.author.avatar && (
                        <Image
                          src={featuredPost.author.avatar}
                          alt={featuredPost.author.name}
                          width={32}
                          height={32}
                          className="rounded-full"
                        />
                      )}
                      <div>
                        {blogConfig.display.showAuthor && (
                          <p className="text-sm font-medium text-gray-900">
                            {featuredPost.author.name}
                          </p>
                        )}
                        {blogConfig.display.showDate && (
                          <p className="text-xs text-gray-500">
                            {formatDate(featuredPost.publishedAt)}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                  {blogConfig.display.showComments && (
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{featuredPost._count.comments} comments</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Posts Grid */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {otherPosts.map((post) => (
          <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full">
            <div className="h-48 relative">
              {post.featuredImage ? (
                <Image
                  src={post.featuredImage}
                  alt={post.title}
                  fill
                  className="object-cover"
                />
              ) : (
                <BlogPlaceholder variant="default" />
              )}
            </div>
            <div className="p-6 flex flex-col flex-grow">
              {/* Category Section - Fixed Height */}
              <div className="mb-3 h-6 flex items-start">
                {blogConfig.display.showCategories && post.category && (
                  <Link
                    href={`/blog/category/${post.category.slug}`}
                    className="text-xs font-semibold text-blue-600 hover:text-blue-800"
                  >
                    {post.category.name}
                  </Link>
                )}
              </div>

              {/* Title Section */}
              <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                <Link
                  href={`/blog/${post.slug}`}
                  className="hover:text-blue-600 transition-colors"
                >
                  {post.title}
                </Link>
              </h3>

              {/* Excerpt Section - Flexible Growth */}
              <div className="text-gray-600 mb-4 line-clamp-3 prose prose-sm max-w-none flex-grow">
                <ReactMarkdown
                  components={{
                    p: ({ children }) => <p className="text-gray-600">{children}</p>,
                    strong: ({ children }) => <strong className="font-semibold text-gray-700">{children}</strong>,
                    em: ({ children }) => <em className="italic">{children}</em>,
                  }}
                >
                  {post.excerpt || ''}
                </ReactMarkdown>
              </div>

              {/* Author and Comments Section - Fixed at Bottom */}
              <div className="flex items-center justify-between mb-4 mt-auto">
                {(blogConfig.display.showAuthor || blogConfig.display.showDate) && (
                  <div className="flex items-center space-x-2">
                    {blogConfig.display.showAuthor && post.author.avatar && (
                      <Image
                        src={post.author.avatar}
                        alt={post.author.name}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    )}
                    <div>
                      {blogConfig.display.showAuthor && (
                        <p className="text-sm font-medium text-gray-900">
                          {post.author.name}
                        </p>
                      )}
                      {blogConfig.display.showDate && (
                        <p className="text-xs text-gray-500">
                          {formatDate(post.publishedAt)}
                        </p>
                      )}
                    </div>
                  </div>
                )}
                {blogConfig.display.showComments && (
                  <span className="text-sm text-gray-500">
                    {post._count.comments} comments
                  </span>
                )}
              </div>

              {/* Tags Section - Fixed Height at Bottom */}
              <div className="min-h-[2.5rem] flex items-start">
                {blogConfig.display.showTags && post.tags.length > 0 && (
                  <div className="pt-4 border-t border-gray-100 w-full">
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag) => (
                        <Link
                          key={tag.slug}
                          href={`/blog/tag/${tag.slug}`}
                          className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors"
                        >
                          #{tag.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </article>
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <div className="flex items-center space-x-2">
            {currentPage > 1 && (
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                className="px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Previous
              </button>
            )}
            
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  currentPage === page
                    ? 'text-white bg-blue-600 border border-blue-600'
                    : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            
            {currentPage < pagination.totalPages && (
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                className="px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Next
              </button>
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {posts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl text-gray-300 mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No blog posts yet</h3>
          <p className="text-gray-500">
            Check back later for the latest updates and insights.
          </p>
        </div>
      )}
    </div>
  )
}
