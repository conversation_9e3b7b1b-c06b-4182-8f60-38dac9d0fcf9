import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import ReactMarkdown from 'react-markdown'
import { notFound } from 'next/navigation'
import BlogPlaceholder from '@/app/components/ui/BlogPlaceholder'

interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string
  featuredImage?: string
  publishedAt: string
  author: {
    name: string
    username: string
    avatar?: string
  }
  category?: {
    name: string
    slug: string
  }
  tags: Array<{
    name: string
    slug: string
  }>
  _count: {
    comments: number
  }
}

interface Category {
  id: string
  name: string
  slug: string
  description?: string
  _count: {
    posts: number
  }
}

interface CategoryPageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ page?: string }>
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

// 获取分类信息
async function getCategory(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/categories/slug/${slug}`, {
      cache: 'no-store'
    })

    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch category')
    }

    const result = await response.json()
    return result.success ? result.data : null
  } catch (error) {
    console.error('Error fetching category:', error)
    return null
  }
}

// 获取分类下的文章
async function getCategoryPosts(categorySlug: string, page: number = 1) {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '12',
      status: 'PUBLISHED',
      category: categorySlug
    })

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/posts?${params}`, {
      cache: 'no-store'
    })

    if (!response.ok) throw new Error('Failed to fetch posts')
    const result = await response.json()
    return result.success ? { posts: result.data || [], pagination: result.pagination } : { posts: [], pagination: null }
  } catch (error) {
    console.error('Error fetching posts:', error)
    return { posts: [], pagination: null }
  }
}

// 生成metadata
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params
  const [category, globalSettings] = await Promise.all([
    getCategory(slug),
    getGlobalSettings()
  ])

  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  if (!category) {
    return {
      title: `Category Not Found | ${siteName}`,
    }
  }

  return {
    title: `${category.name} | Blog | ${siteName}`,
    description: category.description || `Browse all posts in ${category.name} category`,
    openGraph: {
      title: `${category.name} | Blog | ${siteName}`,
      description: category.description || `Browse all posts in ${category.name} category`,
      type: 'website',
    },
  }
}

// SSR页面组件
export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params
  const resolvedSearchParams = await searchParams
  const page = parseInt(resolvedSearchParams.page || '1')

  const [category, { posts, pagination }] = await Promise.all([
    getCategory(slug),
    getCategoryPosts(slug, page)
  ])

  if (!category) {
    notFound()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Link href="/" className="hover:text-gray-700">Home</Link>
          <span>/</span>
          <Link href="/blog" className="hover:text-gray-700">Blog</Link>
          <span>/</span>
          <span className="text-gray-900">{category.name}</span>
        </div>
      </nav>

      {/* Category Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{category.name}</h1>
        {category.description && (
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-4">
            {category.description}
          </p>
        )}
        <p className="text-sm text-gray-500">
          {category._count.posts} {category._count.posts === 1 ? 'post' : 'posts'} in this category
        </p>
      </div>

      {/* Posts Grid */}
      {posts.length > 0 ? (
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.map((post) => (
            <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 relative">
                {post.featuredImage ? (
                  <Image
                    src={post.featuredImage}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <BlogPlaceholder
                    variant="category"
                    category={category.name}
                  />
                )}
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  <Link
                    href={`/blog/${post.slug}`}
                    className="hover:text-blue-600"
                  >
                    {post.title}
                  </Link>
                </h3>
                <div className="text-gray-600 mb-4 line-clamp-3 prose prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      p: ({ children }) => <p className="text-gray-600">{children}</p>,
                      strong: ({ children }) => <strong className="font-semibold text-gray-700">{children}</strong>,
                      em: ({ children }) => <em className="italic">{children}</em>,
                    }}
                  >
                    {post.excerpt || ''}
                  </ReactMarkdown>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {post.author.avatar && (
                      <Image
                        src={post.author.avatar}
                        alt={post.author.name}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {post.author.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatDate(post.publishedAt)}
                      </p>
                    </div>
                  </div>
                  <span className="text-sm text-gray-500">
                    {post._count.comments} comments
                  </span>
                </div>
              </div>
            </article>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl text-gray-300 mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No posts in this category yet</h3>
          <p className="text-gray-500">
            Check back later for new content in {category.name}.
          </p>
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <nav className="flex space-x-2">
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNum) => (
              <Link
                key={pageNum}
                href={`/blog/category/${slug}?page=${pageNum}`}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  page === pageNum
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {pageNum}
              </Link>
            ))}
          </nav>
        </div>
      )}

      {/* Back to Blog */}
      <div className="mt-8 pt-8 border-t">
        <Link
          href="/blog"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          ← Back to Blog
        </Link>
      </div>
    </div>
  )
}