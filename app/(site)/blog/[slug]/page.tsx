import { <PERSON>adata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import { notFound } from 'next/navigation'
import 'highlight.js/styles/github.css'

interface Post {
  id: string
  title: string
  slug: string
  content: string
  excerpt: string
  featuredImage?: string
  publishedAt: string
  metaTitle?: string
  metaDescription?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  author: {
    id: string
    name: string
    username: string
    avatar?: string
    bio?: string
  }
  category?: {
    name: string
    slug: string
  }
  tags: Array<{
    name: string
    slug: string
  }>
  _count: {
    comments: number
  }
}

interface BlogPostPageProps {
  params: Promise<{ slug: string }>
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

// 获取博客文章
async function getPost(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/posts/slug/${slug}`, {
      cache: 'no-store'
    })
    
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch post')
    }
    
    const result = await response.json()
    return result.success ? result.data : null
  } catch (error) {
    console.error('Error fetching post:', error)
    return null
  }
}

// 获取博客配置
async function getBlogConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch blog config')
    const result = await response.json()
    return result.success && result.data?.blog ? result.data.blog : {
      display: {
        showCategories: true,
        showTags: true,
        showAuthor: true,
        showDate: true,
        showComments: true
      }
    }
  } catch (error) {
    return {
      display: {
        showCategories: true,
        showTags: true,
        showAuthor: true,
        showDate: true,
        showComments: true
      }
    }
  }
}

// 生成metadata
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const [post, globalSettings] = await Promise.all([
    getPost(slug),
    getGlobalSettings()
  ])
  
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  
  if (!post) {
    return {
      title: `Post Not Found | ${siteName}`,
    }
  }

  return {
    title: post.metaTitle || `${post.title} | ${siteName}`,
    description: post.metaDescription || post.excerpt,
    openGraph: {
      title: post.ogTitle || post.title,
      description: post.ogDescription || post.excerpt,
      type: 'article',
      images: post.ogImage ? [{ url: post.ogImage }] : (post.featuredImage ? [{ url: post.featuredImage }] : []),
      publishedTime: post.publishedAt,
      authors: [post.author.name],
    },
    other: {
      'article:author': post.author.name,
      'article:published_time': post.publishedAt,
      'article:section': post.category?.name || '',
      'article:tag': post.tags.map(tag => tag.name).join(', '),
    },
  }
}

// SSR页面组件
export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const [post, blogConfig] = await Promise.all([
    getPost(slug),
    getBlogConfig()
  ])

  if (!post) {
    notFound()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Link href="/" className="hover:text-gray-700">Home</Link>
          <span>/</span>
          <Link href="/blog" className="hover:text-gray-700">Blog</Link>
          <span>/</span>
          <span className="text-gray-900">{post.title}</span>
        </div>
      </nav>

      {/* Article Header */}
      <header className="mb-8">
        {blogConfig.display.showCategories && post.category && (
          <div className="mb-4">
            <Link
              href={`/blog/category/${post.category.slug}`}
              className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded"
            >
              {post.category.name}
            </Link>
          </div>
        )}
        
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{post.title}</h1>
        
        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-6">
          {blogConfig.display.showAuthor && (
            <div className="flex items-center space-x-2">
              {post.author.avatar && (
                <Image
                  src={post.author.avatar}
                  alt={post.author.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              )}
              <span>By {post.author.name}</span>
            </div>
          )}
          {blogConfig.display.showDate && (
            <span>{formatDate(post.publishedAt)}</span>
          )}
          {blogConfig.display.showComments && (
            <span>{post._count.comments} comments</span>
          )}
        </div>

        {post.featuredImage && (
          <div className="mb-8">
            <Image
              src={post.featuredImage}
              alt={post.title}
              width={800}
              height={400}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
            />
          </div>
        )}
      </header>

      {/* Article Content */}
      <article className="prose prose-lg max-w-none mb-8">
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
        >
          {post.content}
        </ReactMarkdown>
      </article>

      {/* Tags */}
      {blogConfig.display.showTags && post.tags.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag.slug}
                href={`/blog/tag/${tag.slug}`}
                className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm hover:bg-gray-200"
              >
                #{tag.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Author Bio */}
      {blogConfig.display.showAuthor && post.author.bio && (
        <div className="mb-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-start space-x-4">
            {post.author.avatar && (
              <Image
                src={post.author.avatar}
                alt={post.author.name}
                width={64}
                height={64}
                className="rounded-full"
              />
            )}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                About {post.author.name}
              </h3>
              <p className="text-gray-600">{post.author.bio}</p>
            </div>
          </div>
        </div>
      )}

      {/* Comments Section Placeholder */}
      {blogConfig.display.showComments && (
        <div className="border-t pt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Comments</h3>
          <div className="bg-gray-50 p-6 rounded-lg text-center text-gray-500">
            Comments functionality will be implemented here
          </div>
        </div>
      )}

      {/* Back to Blog */}
      <div className="mt-8 pt-8 border-t">
        <Link
          href="/blog"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          ← Back to Blog
        </Link>
      </div>
    </div>
  )
}
