import { Metadata } from 'next'
import BlogClientComponent from './BlogClientComponent'

interface Post {
  id: string
  title: string
  slug: string
  excerpt: string
  featuredImage?: string
  publishedAt: string
  author: {
    name: string
    username: string
    avatar?: string
  }
  category?: {
    name: string
    slug: string
  }
  tags: Array<{
    name: string
    slug: string
  }>
  _count: {
    comments: number
  }
}

interface BlogConfig {
  header: {
    title: string
    subtitle: string
    description: string
  }
  display: {
    itemsPerPage: number
    showCategories: boolean
    showTags: boolean
    showAuthor: boolean
    showDate: boolean
    showComments: boolean
  }
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

// 获取博客配置
async function getBlogConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch blog config')
    const result = await response.json()
    return result.success && result.data?.blog ? result.data.blog : {
      header: {
        title: 'Our Blog',
        subtitle: 'Latest News & Insights',
        description: 'Stay updated with the latest trends, tips, and insights from our team of experts.'
      },
      display: {
        itemsPerPage: 10,
        showCategories: true,
        showTags: true,
        showAuthor: true,
        showDate: true,
        showComments: true
      }
    }
  } catch (error) {
    return {
      header: {
        title: 'Our Blog',
        subtitle: 'Latest News & Insights',
        description: 'Stay updated with the latest trends, tips, and insights from our team of experts.'
      },
      display: {
        itemsPerPage: 10,
        showCategories: true,
        showTags: true,
        showAuthor: true,
        showDate: true,
        showComments: true
      }
    }
  }
}

// 获取博客文章
async function getPosts(page: number = 1, limit: number = 10) {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      status: 'PUBLISHED'
    })

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/posts?${params}`, {
      cache: 'no-store'
    })

    if (!response.ok) throw new Error('Failed to fetch posts')
    const result = await response.json()
    return result.success ? { posts: result.data || [], pagination: result.pagination } : { posts: [], pagination: null }
  } catch (error) {
    console.error('Error fetching posts:', error)
    return { posts: [], pagination: null }
  }
}

// 生成metadata
export async function generateMetadata(): Promise<Metadata> {
  const [globalSettings, blogConfig] = await Promise.all([
    getGlobalSettings(),
    getBlogConfig()
  ])
  
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'
  const title = `${blogConfig.header.title} | ${siteName}`
  const description = blogConfig.header.description

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

// SSR页面组件
export default async function BlogPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>
}) {
  const resolvedSearchParams = await searchParams
  const page = parseInt(resolvedSearchParams.page || '1')
  
  const [blogConfig, { posts, pagination }] = await Promise.all([
    getBlogConfig(),
    getPosts(page, 10)
  ])

  return (
    <BlogClientComponent 
      initialPosts={posts}
      initialPagination={pagination}
      blogConfig={blogConfig}
      initialPage={page}
    />
  )
}
