import { Metadata } from 'next'
import HeroCarousel from './components/HeroCarousel'
import ProductShowcase from './components/ProductShowcase'
import CompanyIntro from './components/CompanyIntro'
import HotSaleProducts from './components/HotSaleProducts'

// 获取主题配置
async function getThemeConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store' // 确保获取最新配置
    })
    if (!response.ok) {
      throw new Error('Failed to fetch theme config')
    }
    const result = await response.json()
    // API返回格式是 {success: true, data: {...}}，需要提取data字段
    return result.data || result
  } catch (error) {
    console.error('Error fetching theme config:', error)
    // 返回默认配置
    return {
      homepage: {
        hero: { carousel: { slides: [] } },
        productShowcase: {},
        companyIntro: {},
        hotSaleProducts: {}
      }
    }
  }
}

// 获取全局设置用于metadata
async function getGlobalSettingsForMeta() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return {
      site_name: { value: 'InstAI CMS', type: 'string' },
      default_meta_title: { value: 'InstAI CMS - Professional Content Management System', type: 'string' },
      default_meta_description: { value: 'InstAI CMS is a powerful, flexible content management system that helps you create, manage, and scale your digital presence with ease.', type: 'string' }
    }
  }
}

export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const title = globalSettings.default_meta_title?.value || 'InstAI CMS - Professional Content Management System'
  const description = globalSettings.default_meta_description?.value || 'InstAI CMS is a powerful, flexible content management system that helps you create, manage, and scale your digital presence with ease.'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function HomePage() {
  const themeConfig = await getThemeConfig()

  return (
    <div className="min-h-screen">
      {/* Hero Carousel */}
      <HeroCarousel config={themeConfig.homepage?.hero?.carousel} />

      {/* Product Showcase */}
      <ProductShowcase config={themeConfig.homepage?.productShowcase} />

      {/* Company Introduction */}
      <CompanyIntro config={themeConfig.homepage?.companyIntro} />

      {/* Hot Sale Products */}
      <HotSaleProducts config={themeConfig.homepage?.hotSaleProducts} />

      {/* 其他模块将在这里添加 */}
      <div className="py-8">
        {/* 预留空间给其他模块 */}
      </div>
    </div>
  )
}
