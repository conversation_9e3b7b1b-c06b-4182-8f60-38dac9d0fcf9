'use client'

import Image from 'next/image'
import Link from 'next/link'

interface CompanyIntroConfig {
  title?: string
  description1?: string
  description2?: string
  missionTitle?: string
  missionDescription?: string
  visionTitle?: string
  visionDescription?: string
  moreInfoButtonText?: string
  moreInfoButtonLink?: string
  contactButtonText?: string
  contactButtonLink?: string
}

interface CompanyIntroProps {
  config?: CompanyIntroConfig
}

export default function CompanyIntro({ config }: CompanyIntroProps) {
  // 使用配置或默认值
  const title = config?.title || 'ABOUT OUR COMPANY'
  const description1 = config?.description1 || 'If you are not familiar with InstAI CMS name and our reputation for excellence, please take a moment to browse our website and understand why buyers from all over the world consistently choose InstAI CMS as their primary source for content management solutions.'
  const description2 = config?.description2 || 'InstAI CMS has been providing content management systems and services to individuals, resellers, service organizations and major corporations from all over the world. You can count on InstAI CMS for the supply of quality content management solutions.'
  const missionTitle = config?.missionTitle || 'Mission'
  const missionDescription = config?.missionDescription || 'To provide innovative, reliable, and user-friendly content management solutions that empower businesses to succeed in the digital world.'
  const visionTitle = config?.visionTitle || 'Vision'
  const visionDescription = config?.visionDescription || 'To be the leading global provider of content management systems, setting industry standards for innovation and customer satisfaction.'
  const moreInfoButtonText = config?.moreInfoButtonText || 'More Info'
  const moreInfoButtonLink = config?.moreInfoButtonLink || '/about'
  const contactButtonText = config?.contactButtonText || 'Contact Us'
  const contactButtonLink = config?.contactButtonLink || '/contact'
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white/95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-200/50 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Left Side - Image */}
            <div className="relative h-64 lg:h-full min-h-[400px]">
              <Image
                src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                alt="InstAI CMS Company - Modern Office Environment"
                fill
                className="object-cover"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
              {/* Overlay for better text readability if needed */}
              <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent lg:hidden"></div>
            </div>

            {/* Right Side - Company Introduction */}
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              {/* Section Header */}
              <div className="mb-8">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  {title}
                </h2>
                <div className="w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
              </div>

              {/* Company Description */}
              <div className="space-y-6 mb-8">
                <p className="text-gray-700 text-lg leading-relaxed">
                  {description1}
                </p>

                <p className="text-gray-700 text-lg leading-relaxed">
                  {description2}
                </p>
              </div>

              {/* Mission & Vision Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
                  <h3 className="text-xl font-bold text-blue-900 mb-3 flex items-center">
                    <span className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                      {missionTitle.charAt(0).toUpperCase()}
                    </span>
                    {missionTitle}
                  </h3>
                  <p className="text-blue-800 text-sm">
                    {missionDescription}
                  </p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-lg border border-purple-200">
                  <h3 className="text-xl font-bold text-purple-900 mb-3 flex items-center">
                    <span className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                      {visionTitle.charAt(0).toUpperCase()}
                    </span>
                    {visionTitle}
                  </h3>
                  <p className="text-purple-800 text-sm">
                    {visionDescription}
                  </p>
                </div>
              </div>

              {/* Call to Action Button */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={moreInfoButtonLink}
                  className="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                >
                  <span>{moreInfoButtonText}</span>
                  <svg
                    className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>

                <Link
                  href={contactButtonLink}
                  className="inline-flex items-center justify-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg border-2 border-blue-600 hover:bg-blue-50 transform hover:scale-105 transition-all duration-300"
                >
                  {contactButtonText}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
