'use client'

import Image from 'next/image'
import Link from 'next/link'

interface HotProduct {
  id: string
  name: string
  image: string
  price?: string
  originalPrice?: string
  isHot: boolean
  category: string
  description: string
}

interface HotSaleProductsConfig {
  title?: string
  subtitle?: string
  description?: string
  ctaTitle?: string
  ctaDescription?: string
  viewAllButtonText?: string
  viewAllButtonLink?: string
  contactSalesButtonText?: string
  contactSalesButtonLink?: string
}

interface HotSaleProductsProps {
  config?: HotSaleProductsConfig
}

const hotProducts: HotProduct[] = [
  {
    id: '1',
    name: 'InstAI CMS Enterprise Suite',
    image: '/images/lb01.jpg',
    price: '$299/month',
    originalPrice: '$399/month',
    isHot: true,
    category: 'Content Management',
    description: 'Complete enterprise content management solution with advanced features and 24/7 support.'
  },
  {
    id: '2',
    name: 'E-Commerce Platform Pro',
    image: '/images/page-bk.jpg',
    price: '$199/month',
    originalPrice: '$249/month',
    isHot: true,
    category: 'E-Commerce',
    description: 'Powerful e-commerce platform with integrated payment processing and inventory management.'
  },
  {
    id: '3',
    name: 'Analytics Dashboard Suite',
    image: '/images/lb01.jpg',
    price: '$149/month',
    isHot: true,
    category: 'Analytics',
    description: 'Comprehensive analytics and reporting tools to track your business performance in real-time.'
  },
  {
    id: '4',
    name: 'Custom Development Service',
    image: '/images/page-bk.jpg',
    isHot: false,
    category: 'Services',
    description: 'Tailored development solutions to meet your specific business requirements and technical needs.'
  },
  {
    id: '5',
    name: 'Mobile App Integration',
    image: '/images/lb01.jpg',
    price: '$99/month',
    isHot: true,
    category: 'Mobile',
    description: 'Seamless mobile app integration with your CMS for enhanced user experience across all devices.'
  },
  {
    id: '6',
    name: 'Security & Backup Solutions',
    image: '/images/page-bk.jpg',
    price: '$79/month',
    originalPrice: '$99/month',
    isHot: false,
    category: 'Security',
    description: 'Advanced security features and automated backup solutions to protect your valuable data.'
  }
]

export default function HotSaleProducts({ config }: HotSaleProductsProps) {
  // 使用配置或默认值
  const title = config?.title || 'HOT SALE PRODUCTS'
  const subtitle = config?.subtitle || ''
  const description = config?.description || 'To be competitive, InstAI CMS will have new products every year with its own design.'
  const ctaTitle = config?.ctaTitle || '🔥 Limited Time Offers!'
  const ctaDescription = config?.ctaDescription || "Don't miss out on these exclusive deals. Our hot sale products are available for a limited time only. Upgrade your business with premium CMS solutions at unbeatable prices."
  const viewAllButtonText = config?.viewAllButtonText || 'View All Products'
  const viewAllButtonLink = config?.viewAllButtonLink || '/products'
  const contactSalesButtonText = config?.contactSalesButtonText || 'Contact Sales'
  const contactSalesButtonLink = config?.contactSalesButtonLink || '/contact'
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {title}
              </span>
            }
          </h2>
          {subtitle && (
            <h3 className="text-xl font-medium text-gray-700 mb-2">
              {subtitle}
            </h3>
          )}
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {hotProducts.map((product) => (
            <div
              key={product.id}
              className="group bg-white/95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-200/50 overflow-hidden hover:scale-105 transition-all duration-300 hover:shadow-2xl relative flex flex-col h-full"
            >
              {/* Hot Badge */}
              {product.isHot && (
                <div className="absolute top-4 right-4 z-10">
                  <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg animate-pulse">
                    HOT
                  </span>
                </div>
              )}

              {/* Product Image */}
              <div className="relative h-64 overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Product Info */}
              <div className="p-6 flex flex-col flex-grow">
                {/* Category */}
                <div className="mb-3">
                  <span className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                    {product.category}
                  </span>
                </div>

                {/* Product Name */}
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                  {product.name}
                </h3>

                {/* Product Description */}
                <p className="text-gray-600 text-sm mb-4 flex-grow line-clamp-3">
                  {product.description}
                </p>

                {/* Price Section */}
                {product.price && (
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-blue-600">
                        {product.price}
                      </span>
                      {product.originalPrice && (
                        <span className="text-lg text-gray-500 line-through">
                          {product.originalPrice}
                        </span>
                      )}
                    </div>
                    {product.originalPrice && (
                      <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-semibold">
                        SAVE 25%
                      </div>
                    )}
                  </div>
                )}

                {/* Contact for Quote Section */}
                {!product.price && (
                  <div className="mb-6">
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 text-center">
                      <p className="text-blue-800 font-semibold text-lg">Contact for Quote</p>
                      <p className="text-blue-600 text-sm mt-1">Custom pricing based on requirements</p>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col gap-3">
                  <Link
                    href={`/products/${product.id}`}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold text-center hover:shadow-lg transform hover:scale-105 transition-all duration-300 group"
                  >
                    <span>More Info</span>
                    <svg
                      className="inline-block ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>

                  <Link
                    href="/contact"
                    className="w-full bg-white text-blue-600 py-3 px-6 rounded-lg font-semibold text-center border-2 border-blue-600 hover:bg-blue-50 transform hover:scale-105 transition-all duration-300"
                  >
                    Get Quote
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8 border border-blue-200">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {ctaTitle}
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              {ctaDescription}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={viewAllButtonLink}
                className="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                {viewAllButtonText}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
              <Link
                href={contactSalesButtonLink}
                className="inline-flex items-center justify-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg border-2 border-blue-600 hover:bg-blue-50 transform hover:scale-105 transition-all duration-300"
              >
                {contactSalesButtonText}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
