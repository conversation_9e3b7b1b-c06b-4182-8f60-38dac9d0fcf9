import Link from 'next/link'

interface Product {
  id: number
  name: string
  description: string
  image: string
  imageAlt: string
  category: string
  featured?: boolean
}

interface ProductShowcaseConfig {
  title?: string
  subtitle?: string
  description?: string
  viewAllButtonText?: string
  viewAllButtonLink?: string
}

interface ProductShowcaseProps {
  config?: ProductShowcaseConfig
}

const featuredProducts: Product[] = [
  {
    id: 1,
    name: "InstAI CMS Core System",
    description: "Complete content management platform with advanced editing capabilities",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center",
    imageAlt: "CMS Core System Dashboard",
    category: "Content Management",
    featured: true
  },
  {
    id: 2,
    name: "E-Commerce Platform Pro",
    description: "Full-featured online store with payment integration and inventory management",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center",
    imageAlt: "E-Commerce Platform Interface",
    category: "E-Commerce",
    featured: true
  },
  {
    id: 3,
    name: "Analytics Dashboard Suite",
    description: "Real-time analytics and reporting with customizable dashboards",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center",
    imageAlt: "Analytics Dashboard",
    category: "Analytics",
    featured: true
  },
  {
    id: 4,
    name: "Mobile App Builder",
    description: "Native mobile applications for iOS and Android with offline capabilities",
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop&crop=center",
    imageAlt: "Mobile App Interface",
    category: "Mobile Development",
    featured: true
  }
]

export default function ProductShowcase({ config }: ProductShowcaseProps) {
  // 使用配置或默认值
  const title = config?.title || 'WELCOME TO InstAI CMS!'
  const subtitle = config?.subtitle || ''
  const description = config?.description || 'Discover our flagship products designed to power your digital transformation'
  const viewAllButtonText = config?.viewAllButtonText || '全部产品'
  const viewAllButtonLink = config?.viewAllButtonLink || '/products'

  return (
    <div className="py-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          {subtitle && (
            <h3 className="text-xl font-medium text-gray-700 mb-2">
              {subtitle}
            </h3>
          )}
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {description}
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredProducts.map((product) => (
            <div
              key={product.id}
              className="group bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden flex flex-col h-full"
            >
              {/* Product Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.imageAlt}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                {product.featured && (
                  <div className="absolute top-3 right-3 bg-blue-600 text-white px-2 py-1 rounded text-xs font-semibold">
                    FEATURED
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Product Info */}
              <div className="p-6 flex flex-col flex-grow">
                <div className="mb-2">
                  <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                    {product.category}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {product.name}
                </h3>

                <p className="text-gray-600 text-sm mb-4 flex-grow">
                  {product.description}
                </p>

                {/* Action Buttons */}
                <div className="flex flex-col gap-2 mt-auto">
                  <Link
                    href={`/products/${product.id}`}
                    className="w-full bg-blue-600 text-white flex items-center justify-center py-2.5 px-4 rounded text-sm font-medium hover:bg-blue-700 transition-colors text-center"
                  >
                    VIEW DETAILS
                  </Link>
                  <Link
                    href="/admin"
                    className="w-full border border-blue-600 text-blue-600 flex items-center justify-center py-2.5 px-4 rounded text-sm font-medium hover:bg-blue-600 hover:text-white transition-colors text-center"
                  >
                    TRY DEMO
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Products CTA */}
        <div className="text-center mt-12">
          <Link
            href={viewAllButtonLink}
            className="inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all transform hover:scale-105 shadow-lg"
          >
            {viewAllButtonText}
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  )
}
