'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

interface HeroSlide {
  id: number
  title: string
  subtitle: string
  description: string
  primaryButton: {
    text: string
    link: string
  }
  secondaryButton: {
    text: string
    link: string
  }
  image: string
  imageAlt: string
}

interface HeroCarouselConfig {
  slides?: HeroSlide[]
}

interface HeroCarouselProps {
  config?: HeroCarouselConfig
}

const heroSlides: HeroSlide[] = [
  {
    id: 1,
    title: "TOP QUALITY CMS PRODUCTS",
    subtitle: "Professional Content Management Solutions",
    description: "Professional Content Management Solutions for Enterprise Applications",
    primaryButton: {
      text: "VIEW ALL PRODUCTS",
      link: "/products"
    },
    secondaryButton: {
      text: "LIVE DEMO",
      link: "/admin"
    },
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=center",
    imageAlt: "CMS Dashboard Interface"
  },
  {
    id: 2,
    title: "ENTERPRISE SOLUTIONS",
    subtitle: "Scalable & Secure Platform",
    description: "Built for large-scale enterprises with advanced security and performance optimization",
    primaryButton: {
      text: "ENTERPRISE PLANS",
      link: "/products"
    },
    secondaryButton: {
      text: "CONTACT SALES",
      link: "/contact"
    },
    image: "/images/lb01.jpg",
    imageAlt: "Enterprise Security Dashboard"
  },
  {
    id: 3,
    title: "DEVELOPER FRIENDLY",
    subtitle: "Modern Tech Stack",
    description: "Built with Next.js, TypeScript, and modern development practices for maximum productivity",
    primaryButton: {
      text: "DOCUMENTATION",
      link: "/blog"
    },
    secondaryButton: {
      text: "API REFERENCE",
      link: "/admin"
    },
    image: "/images/lb01.jpg",
    imageAlt: "Developer Tools Interface"
  }
]

export default function HeroCarousel({ config }: HeroCarouselProps) {
  // 使用配置或默认值
  const slides = config?.slides || heroSlides
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, slides.length])

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false) // Stop auto-play when user manually navigates
    setTimeout(() => setIsAutoPlaying(true), 10000) // Resume auto-play after 10 seconds
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const slide = slides[currentSlide]

  return (
    <div className="relative bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 text-white py-20 mb-12 rounded-lg overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-400 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-20 right-20 w-24 h-24 bg-purple-400 rounded-full blur-lg animate-bounce"></div>
        <div className="absolute bottom-10 left-1/3 w-40 h-40 bg-cyan-400 rounded-full blur-2xl animate-pulse delay-1000"></div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-full p-3 transition-all duration-300"
      >
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-full p-3 transition-all duration-300"
      >
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* Slide Content */}
      <div className="relative z-10 text-center transition-all duration-500 ease-in-out">
        <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
          {slide.title}
        </h1>
        <h2 className="text-2xl font-semibold text-blue-200 mb-4">
          {slide.subtitle}
        </h2>
        <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
          {slide.description}
        </p>

        {/* Featured Image */}
        <div className="flex justify-center mb-8">
          <div className="relative w-full max-w-2xl h-64 rounded-lg overflow-hidden shadow-2xl">
            <img
              src={slide.image}
              alt={slide.imageAlt}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={slide.primaryButton.link}
            className="bg-white text-blue-600 px-8 py-3 rounded-md font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg"
          >
            {slide.primaryButton.text}
          </Link>
          <Link
            href={slide.secondaryButton.link}
            className="border-2 border-white text-white px-8 py-3 rounded-md font-semibold hover:bg-white hover:text-blue-600 transition-all transform hover:scale-105"
          >
            {slide.secondaryButton.text}
          </Link>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-white scale-125'
                : 'bg-white/50 hover:bg-white/75'
            }`}
          />
        ))}
      </div>

      {/* Auto-play indicator */}
      <div className="absolute top-4 right-4 z-20">
        <button
          onClick={() => setIsAutoPlaying(!isAutoPlaying)}
          className={`p-2 rounded-full transition-all duration-300 ${
            isAutoPlaying
              ? 'bg-green-500/20 text-green-300'
              : 'bg-red-500/20 text-red-300'
          }`}
        >
          {isAutoPlaying ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 4h10a1 1 0 001-1V7a1 1 0 00-1-1H6a1 1 0 00-1 1v10a1 1 0 001 1z" />
            </svg>
          )}
        </button>
      </div>
    </div>
  )
}
