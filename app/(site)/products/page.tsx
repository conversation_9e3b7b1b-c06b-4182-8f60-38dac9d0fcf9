import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { ProductWithRelations, ProductCategoryWithRelations } from '@/app/types'

// 获取主题配置
async function getThemeConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store' // 确保获取最新配置
    })
    if (!response.ok) {
      throw new Error('Failed to fetch theme config')
    }
    const result = await response.json()
    // API返回格式是 {success: true, data: {...}}，需要提取data字段
    return result.data || result
  } catch (error) {
    console.error('Error fetching theme config:', error)
    // 返回默认配置
    return {
      products: {
        header: {
          title: 'Product Catalog',
          subtitle: 'Enterprise Solutions & Services',
          description: 'Comprehensive content management solutions designed for enterprise-level operations.'
        },
        sidebar: {
          categoriesTitle: 'Product Categories',
          allProductsText: 'All Products',
          hotProductsText: 'Hot Products',
          byCategoryText: 'By Category'
        },
        card: {
          featuredBadgeText: 'FEATURED',
          learnMoreButtonText: 'Learn More →',
          contactForPriceText: 'Contact for Price',
          productCodeLabel: 'Product Code:'
        },
        emptyState: {
          title: 'Product Catalog Coming Soon',
          description: 'We\'re currently preparing our comprehensive product catalog.',
          contactButtonText: 'Contact Sales Team'
        }
      }
    }
  }
}

// 获取全局设置用于metadata
async function getGlobalSettingsForMeta() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  return {
    title: `Products | ${siteName}`,
    description: 'Browse our complete product catalog with detailed specifications and features.',
    openGraph: {
      title: `Products | ${siteName}`,
      description: 'Browse our complete product catalog with detailed specifications and features.',
      type: 'website',
    },
  }
}

async function getProducts() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products/public`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : []
  } catch (error) {
    console.error('Error fetching products:', error)
    return []
  }
}

async function getCategories() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/product-categories?tree=true`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : []
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

export default async function ProductsPage() {
  const [products, categories, themeConfig] = await Promise.all([
    getProducts(),
    getCategories(),
    getThemeConfig(),
  ])

  // 使用配置或默认值
  const config = themeConfig.products

  const formatPrice = (price: any) => {
    if (!price) return config.card.contactForPriceText
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(price))
  }

  const renderCategories = (categories: any[]) => {
    return categories.map((category: any) => (
      <div key={category.id}>
        <Link
          href={`/products/category/${category.slug}`}
          className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md border-l-4 border-transparent hover:border-blue-300 transition-all"
        >
          <div className="flex items-center justify-between">
            <span className="font-medium">{category.name}</span>
            {(category._count?.totalProducts || category._count?.products) && (
              <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {category._count?.totalProducts || category._count?.products}
              </span>
            )}
          </div>
        </Link>
        {category.children && category.children.length > 0 && (
          <div className="ml-6 mt-2 space-y-1">
            {renderCategories(category.children)}
          </div>
        )}
      </div>
    ))
  }

  return (
    <div className="px-8 py-12">
      {/* Professional Header Section */}
      <div className="text-center mb-12">
        <div className="inline-block mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{config.header.title}</h1>
          <p className="text-lg text-blue-600 font-semibold">{config.header.subtitle}</p>
          <div className="w-20 h-1 bg-gradient-to-r from-blue-600 to-blue-800 mx-auto mt-4"></div>
        </div>

        <div className="max-w-3xl mx-auto">
          <p className="text-lg text-gray-600 leading-relaxed">
            {config.header.description}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Professional Sidebar - Categories */}
        <div className="lg:col-span-1">
          <div className="bg-gray-50 rounded-lg border p-6 sticky top-8">
            <h2 className="text-lg font-bold text-gray-900 mb-6 pb-3 border-b border-gray-200">
              {config.sidebar.categoriesTitle}
            </h2>
            <div className="space-y-2">
              <Link
                href="/products"
                className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md font-medium border-l-4 border-blue-600 bg-blue-50"
              >
                {config.sidebar.allProductsText} ({products.length})
              </Link>
              <Link
                href="/products/featured"
                className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md border-l-4 border-transparent hover:border-blue-300"
              >
                {config.sidebar.hotProductsText}
              </Link>
              <div className="pt-3 border-t border-gray-200 mt-4">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
                  {config.sidebar.byCategoryText}
                </div>
                {renderCategories(categories)}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Professional Products Grid */}
        <div className="lg:col-span-3">
          {products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {products.map((product: ProductWithRelations) => (
                <div key={product.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-blue-300 flex flex-col h-full">
                  {/* Professional Product Image - Fixed Height */}
                  <div className="aspect-w-16 aspect-h-10 bg-gradient-to-br from-gray-50 to-gray-100">
                    {product.images && product.images.length > 0 ? (
                      <Image
                        src={product.images[0].url}
                        alt={product.images[0].alt || product.name}
                        width={500}
                        height={300}
                        className="w-full h-56 object-cover"
                      />
                    ) : (
                      <div className="w-full h-56 bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-blue-200 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <span className="text-blue-600 text-2xl">📦</span>
                          </div>
                          <div className="text-blue-600 font-medium text-sm">Product Image</div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Professional Product Info - Flexible Layout */}
                  <div className="p-8 flex flex-col flex-grow">
                    {/* Header Section - Fixed Height */}
                    <div className="flex items-start justify-between mb-4 min-h-[4rem]">
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-2 leading-tight line-clamp-2">
                          {product.name}
                        </h3>
                        {product.category && (
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {product.category.name}
                          </div>
                        )}
                      </div>
                      {product.featured && (
                        <span className="inline-flex px-3 py-1 text-xs font-bold rounded-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 shadow-sm ml-2">
                          {config.card.featuredBadgeText}
                        </span>
                      )}
                    </div>

                    {/* Description - Flexible Growth */}
                    <p className="text-gray-600 text-sm mb-6 leading-relaxed line-clamp-4 flex-grow">
                      {product.excerpt || product.description}
                    </p>

                    {/* Price and Button Section - Fixed at Bottom */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
                      <div className="text-xl font-bold text-blue-600">
                        {formatPrice(product.price)}
                      </div>
                      <Link
                        href={`/products/${product.category?.slug || 'uncategorized'}/${product.slug}`}
                        className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all shadow-md hover:shadow-lg"
                      >
                        {config.card.learnMoreButtonText}
                      </Link>
                    </div>

                    {/* SKU Section - Fixed Height */}
                    <div className="mt-4 pt-4 border-t border-gray-100 min-h-[2rem] flex items-start">
                      {product.sku && (
                        <p className="text-xs text-gray-500 font-mono">{config.card.productCodeLabel} {product.sku}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-gray-400 text-3xl">📦</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{config.emptyState.title}</h3>
              <p className="text-gray-600 max-w-md mx-auto mb-6">
                {config.emptyState.description}
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md font-semibold hover:bg-blue-700 transition-colors"
              >
                {config.emptyState.contactButtonText}
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
