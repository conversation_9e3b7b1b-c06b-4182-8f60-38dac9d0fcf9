import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { notFound } from 'next/navigation'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { ProductWithRelations } from '@/app/types'
import InquiryForm from '@/app/components/forms/InquiryForm'

interface ProductPageProps {
  params: Promise<{ category: string; slug: string }>
}

async function getProduct(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products/slug/${slug}`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : null
  } catch (error) {
    console.error('Error fetching product:', error)
    return null
  }
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) {
      throw new Error('Failed to fetch global settings')
    }
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    console.error('Error fetching global settings:', error)
    // 返回默认设置
    return {
      site_name: { value: 'InstAI CMS', type: 'string' },
      default_meta_title: { value: 'InstAI CMS - Professional Content Management System', type: 'string' }
    }
  }
}

// 获取主题配置
async function getThemeConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store' // 确保获取最新配置
    })
    if (!response.ok) {
      throw new Error('Failed to fetch theme config')
    }
    const result = await response.json()
    return result.data || result
  } catch (error) {
    console.error('Error fetching theme config:', error)
    // 返回默认配置
    return {
      products: {
        detail: {
          inquirySection: {
            title: 'Interested in this product?',
            description: 'Get detailed information, pricing, and availability for {productName}. Our team will respond to your inquiry within 24 hours.',
            features: [
              'Free consultation and quote',
              'Expert technical support',
              'Custom configuration available',
              '24-hour response guarantee'
            ]
          }
        }
      }
    }
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const { slug } = await params
  const [product, globalSettings] = await Promise.all([
    getProduct(slug),
    getGlobalSettings()
  ])

  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  if (!product) {
    return {
      title: `Product Not Found | ${siteName}`,
    }
  }

  return {
    title: product.seoTitle || `${product.name} | ${siteName}`,
    description: product.seoDescription || product.excerpt || product.description,
    keywords: product.seoKeywords,
    openGraph: {
      title: product.ogTitle || product.name,
      description: product.ogDescription || product.excerpt || product.description,
      type: 'website',
      images: product.ogImage ? [{ url: product.ogImage }] : (product.images?.length > 0 ? [{ url: product.images[0].url }] : []),
    },
    other: {
      'product:price:amount': product.price?.toString() || '',
      'product:price:currency': 'USD',
      'product:availability': product.status === 'ACTIVE' ? 'in stock' : 'out of stock',
    },
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { slug } = await params
  const [product, themeConfig] = await Promise.all([
    getProduct(slug),
    getThemeConfig(),
  ])

  if (!product) {
    notFound()
  }

  // 使用配置或默认值
  const config = themeConfig.products?.detail || {
    inquirySection: {
      title: 'Interested in this product?',
      description: 'Get detailed information, pricing, and availability for {productName}. Our team will respond to your inquiry within 24 hours.',
      features: [
        'Free consultation and quote',
        'Expert technical support',
        'Custom configuration available',
        '24-hour response guarantee'
      ]
    }
  }

  const formatPrice = (price: number | null) => {
    if (!price) return 'Contact for Price'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(price))
  }

  // Generate JSON-LD structured data
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    sku: product.sku,
    brand: {
      '@type': 'Brand',
      name: 'Enterprise CMS',
    },
    offers: {
      '@type': 'Offer',
      price: product.price || 0,
      priceCurrency: 'USD',
      availability: product.status === 'ACTIVE' ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Enterprise CMS',
      },
    },
    image: product.images?.map(img => img.url) || [],
    category: product.category?.name,
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div>
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-blue-600">
                Home
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/products" className="text-gray-700 hover:text-blue-600">
                  Products
                </Link>
              </div>
            </li>
            {product.category && (
              <li>
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <Link 
                    href={`/products/category/${product.category.slug}`}
                    className="text-gray-700 hover:text-blue-600"
                  >
                    {product.category.name}
                  </Link>
                </div>
              </li>
            )}
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-gray-500">{product.name}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <div>
            {product.images && product.images.length > 0 ? (
              <div className="space-y-4">
                <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                  <Image
                    src={product.images[0].url}
                    alt={product.images[0].alt || product.name}
                    width={600}
                    height={600}
                    className="w-full h-full object-cover"
                    priority
                  />
                </div>
                {product.images.length > 1 && (
                  <div className="grid grid-cols-4 gap-2">
                    {product.images.slice(1, 5).map((image, index) => (
                      <div key={index} className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                        <Image
                          src={image.url}
                          alt={image.alt || `${product.name} ${index + 2}`}
                          width={150}
                          height={150}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 text-8xl">📦</span>
              </div>
            )}
          </div>

          {/* Product Info */}
          <div>
            <div className="flex items-start justify-between mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
              {product.featured && (
                <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                  Featured
                </span>
              )}
            </div>

            {product.category && (
              <p className="text-lg text-blue-600 mb-4">
                <Link href={`/products/category/${product.category.slug}`} className="hover:underline">
                  {product.category.name}
                </Link>
              </p>
            )}

            <div className="text-3xl font-bold text-gray-900 mb-6">
              {formatPrice(product.price)}
            </div>

            <div className="prose prose-gray max-w-none mb-8">
              <p className="text-lg text-gray-600 leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Product Attributes */}
            {product.attributes && product.attributes.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Specifications</h3>
                <dl className="grid grid-cols-1 gap-4">
                  {product.attributes.map((attr, index) => (
                    <div key={index} className="flex justify-between py-2 border-b border-gray-200">
                      <dt className="font-medium text-gray-900">{attr.name}</dt>
                      <dd className="text-gray-600">{attr.value}</dd>
                    </div>
                  ))}
                </dl>
              </div>
            )}

            {/* Product Meta */}
            <div className="border-t border-gray-200 pt-6 space-y-2">
              {product.sku && (
                <p className="text-sm text-gray-500">
                  <span className="font-medium">SKU:</span> {product.sku}
                </p>
              )}
              <p className="text-sm text-gray-500">
                <span className="font-medium">Status:</span> 
                <span className={`ml-1 ${product.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                  {product.status === 'ACTIVE' ? 'In Stock' : 'Out of Stock'}
                </span>
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="mt-8 space-y-4">
              <Link
                href={`/contact?product=${product.slug}`}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors text-center block"
              >
                Request Quote
              </Link>
              <Link
                href="/contact"
                className="w-full bg-gray-100 text-gray-900 px-6 py-3 rounded-md text-lg font-medium hover:bg-gray-200 transition-colors text-center block"
              >
                Contact Sales
              </Link>
            </div>
          </div>
        </div>

        {/* Product Description */}
        <div className="border-t border-gray-200 pt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Product Details</h2>
          <div className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900 prose-code:text-pink-600 prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-2xl font-bold text-gray-900 mt-6 mb-4 first:mt-0">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-xl font-bold text-gray-900 mt-5 mb-3">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg font-semibold text-gray-900 mt-4 mb-2">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-4 space-y-1 text-gray-700">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside mb-4 space-y-1 text-gray-700">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-700">
                    {children}
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 my-4">
                    {children}
                  </blockquote>
                ),
                code: ({ inline, children }) => {
                  if (inline) {
                    return (
                      <code className="bg-gray-100 text-pink-600 px-1 py-0.5 rounded text-sm">
                        {children}
                      </code>
                    )
                  }
                  return (
                    <code className="block bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                      {children}
                    </code>
                  )
                },
                pre: ({ children }) => (
                  <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mb-4">
                    {children}
                  </pre>
                ),
                a: ({ href, children }) => (
                  <a
                    href={href}
                    className="text-blue-600 hover:text-blue-800 underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {children}
                  </a>
                ),
              }}
            >
              {product.content}
            </ReactMarkdown>
          </div>
        </div>

        {/* Inquiry Form */}
        <div className="border-t border-gray-200 pt-12 mt-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">{config.inquirySection?.title || 'Interested in this product?'}</h2>
              <p className="text-gray-600 mb-6">
                {(config.inquirySection?.description || 'Get detailed information, pricing, and availability for {productName}. Our team will respond to your inquiry within 24 hours.').replace('{productName}', product.name)}
              </p>
              <div className="space-y-4 text-sm text-gray-600">
                {(config.inquirySection?.features || ['Free consultation and quote', 'Expert technical support', 'Custom configuration available', '24-hour response guarantee']).map((feature: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-green-600">✓</span>
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <InquiryForm
                productId={product.id}
                productName={product.name}
                className="shadow-lg"
              />
            </div>
          </div>
        </div>

        {/* Related Products */}
        {product.category && (
          <div className="border-t border-gray-200 pt-12 mt-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Products</h2>
            <div className="text-center py-8">
              <Link
                href={`/products/category/${product.category.slug}`}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                View More in {product.category.name}
              </Link>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
