import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { ProductWithRelations, ProductCategoryWithRelations } from '@/app/types'

// 获取主题配置
async function getThemeConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/themes/config`, {
      cache: 'no-store'
    })
    if (!response.ok) {
      throw new Error('Failed to fetch theme config')
    }
    const result = await response.json()
    return result.data || result
  } catch (error) {
    console.error('Error fetching theme config:', error)
    return {
      products: {
        featured: {
          pageTitle: 'Hot Products',
          pageSubtitle: 'Featured & Popular Items',
          pageDescription: 'Discover our most popular and featured products.',
          badgeTitle: '🔥 Hot Products',
          badgeDescription: 'These products are currently trending.',
          emptyTitle: 'No Hot Products Available',
          emptyDescription: 'We\'re currently updating our featured products.',
          browseAllButtonText: 'Browse All Products'
        },
        sidebar: {
          categoriesTitle: 'Product Categories',
          allProductsText: 'All Products',
          hotProductsText: 'Hot Products',
          byCategoryText: 'By Category'
        },
        card: {
          featuredBadgeText: 'FEATURED',
          learnMoreButtonText: 'Learn More →',
          contactForPriceText: 'Contact for Price',
          productCodeLabel: 'Product Code:'
        }
      }
    }
  }
}

// 获取全局设置用于metadata
async function getGlobalSettingsForMeta() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

export async function generateMetadata(): Promise<Metadata> {
  const globalSettings = await getGlobalSettingsForMeta()
  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  return {
    title: `Hot Products | ${siteName}`,
    description: 'Discover our most popular and featured products with exclusive deals and premium features.',
    openGraph: {
      title: `Hot Products | ${siteName}`,
      description: 'Discover our most popular and featured products with exclusive deals and premium features.',
      type: 'website',
    },
  }
}

async function getHotProducts() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products/public?featured=true`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : []
  } catch (error) {
    console.error('Error fetching hot products:', error)
    return []
  }
}

async function getCategories() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/product-categories?tree=true`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : []
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

export default async function HotProductsPage() {
  const [products, categories, themeConfig] = await Promise.all([
    getHotProducts(),
    getCategories(),
    getThemeConfig(),
  ])

  // 使用配置或默认值
  const config = themeConfig.products

  const formatPrice = (price: any) => {
    if (!price) return config.card.contactForPriceText
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(price))
  }

  const renderCategories = (categories: any[]) => {
    return categories.map((category: any) => (
      <div key={category.id}>
        <Link
          href={`/products/category/${category.slug}`}
          className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md border-l-4 border-transparent hover:border-blue-300 transition-all"
        >
          <div className="flex items-center justify-between">
            <span className="font-medium">{category.name}</span>
            {category._count?.products && (
              <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {category._count.products}
              </span>
            )}
          </div>
        </Link>
        {category.children && category.children.length > 0 && (
          <div className="ml-6 mt-2 space-y-1">
            {renderCategories(category.children)}
          </div>
        )}
      </div>
    ))
  }

  return (
    <div className="px-8 py-12">
      {/* Professional Header Section */}
      <div className="text-center mb-16">
        <div className="inline-block mb-6">
          {/* <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg">
            <span className="text-white font-bold text-2xl">🔥</span>
          </div> */}
          <h1 className="text-4xl font-bold text-gray-900 mb-2">{config.featured.pageTitle}</h1>
          <p className="text-lg text-red-600 font-semibold">{config.featured.pageSubtitle}</p>
          <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-red-600 mx-auto mt-4"></div>
        </div>

        <div className="max-w-4xl mx-auto">
          <p className="text-xl text-gray-600 leading-relaxed">
            {config.featured.pageDescription}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Professional Sidebar - Categories */}
        <div className="lg:col-span-1">
          <div className="bg-gray-50 rounded-lg border p-6 sticky top-8">
            <h2 className="text-lg font-bold text-gray-900 mb-6 pb-3 border-b border-gray-200">
              Product Categories
            </h2>
            <div className="space-y-2">
              <Link
                href="/products"
                className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md border-l-4 border-transparent hover:border-blue-300"
              >
                {config.sidebar.allProductsText}
              </Link>
              <Link
                href="/products/featured"
                className="block px-4 py-3 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md font-medium border-l-4 border-red-600 bg-red-50"
              >
                {config.sidebar.hotProductsText} ({products.length})
              </Link>
              {categories.length > 0 && (
                <div className="pt-4 border-t border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">By Category</h3>
                  {renderCategories(categories)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {products.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-4xl">🔥</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{config.featured.emptyTitle}</h3>
              <p className="text-gray-600 mb-6">
                {config.featured.emptyDescription}
              </p>
              <Link
                href="/products"
                className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                {config.featured.browseAllButtonText}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          ) : (
            <>
              {/* Hot Products Badge */}
              <div className="mb-8">
                <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg p-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-4">
                      <span className="text-white text-xl">🔥</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">Featured Products</h2>
                      <p className="text-red-600 font-medium">
                        {products.length} hot products • Limited time offers available
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Products Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                {products.map((product: ProductWithRelations) => (
                  <div
                    key={product.id}
                    className="group bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden flex flex-col h-full relative"
                  >
                    {/* Hot Badge */}
                    <div className="absolute top-4 right-4 z-10">
                      <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg animate-pulse">
                        HOT
                      </span>
                    </div>

                    {/* Product Image */}
                    <div className="relative h-48 overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0].url}
                          alt={product.images[0].alt || product.name}
                          fill
                          className="object-cover transition-transform duration-300 group-hover:scale-110"
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-red-100 to-orange-100 flex items-center justify-center">
                          <span className="text-4xl">🔥</span>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="p-6 flex flex-col flex-grow">
                      {/* Category */}
                      {product.category && (
                        <div className="mb-3">
                          <span className="inline-block bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                            {product.category.name}
                          </span>
                        </div>
                      )}

                      {/* Product Name */}
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-red-600 transition-colors duration-300 line-clamp-2">
                        {product.name}
                      </h3>

                      {/* Product Description */}
                      <p className="text-gray-600 text-sm mb-4 flex-grow line-clamp-3">
                        {product.description}
                      </p>

                      {/* Price */}
                      <div className="mb-6">
                        <div className="text-2xl font-bold text-red-600">
                          {formatPrice(product.price)}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col gap-3">
                        <Link
                          href={`/products/${product.slug}`}
                          className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-6 rounded-lg font-semibold text-center hover:shadow-lg transform hover:scale-105 transition-all duration-300 group"
                        >
                          <span>{config.card.learnMoreButtonText}</span>
                          <svg 
                            className="inline-block ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                        
                        <Link
                          href="/contact"
                          className="w-full bg-white text-red-600 py-3 px-6 rounded-lg font-semibold text-center border-2 border-red-600 hover:bg-red-50 transform hover:scale-105 transition-all duration-300"
                        >
                          Get Quote
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
