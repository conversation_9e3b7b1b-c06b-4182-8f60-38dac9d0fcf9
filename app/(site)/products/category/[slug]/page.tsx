import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { notFound } from 'next/navigation'
import { ProductWithRelations, ProductCategoryWithRelations } from '@/app/types'

interface CategoryPageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ page?: string }>
}

async function getCategory(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/product-categories/slug/${slug}?products=true&includeSubcategories=true`, {
      cache: 'no-store',
    })
    const data = await response.json()
    return data.success ? data.data : null
  } catch (error) {
    console.error('Error fetching category:', error)
    return null
  }
}

// 获取全局设置
async function getGlobalSettings() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })
    if (!response.ok) throw new Error('Failed to fetch settings')
    const result = await response.json()
    return result.success ? result.data : {}
  } catch (error) {
    return { site_name: { value: 'InstAI CMS', type: 'string' } }
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params
  const [category, globalSettings] = await Promise.all([
    getCategory(slug),
    getGlobalSettings()
  ])

  const siteName = globalSettings.site_name?.value || 'InstAI CMS'

  if (!category) {
    return {
      title: `Category Not Found | ${siteName}`,
    }
  }

  return {
    title: `${category.name} | Products | ${siteName}`,
    description: category.seoDescription || category.description || `Browse ${category.name} products in our catalog.`,
    keywords: category.seoKeywords,
    openGraph: {
      title: category.ogTitle || `${category.name} | Products`,
      description: category.ogDescription || category.description,
      type: 'website',
      images: category.image ? [{ url: category.image }] : [],
    },
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params
  const { page: pageParam } = await searchParams
  const category = await getCategory(slug)

  if (!category) {
    notFound()
  }

  const page = parseInt(pageParam || '1')
  const productsPerPage = 12

  const formatPrice = (price: any) => {
    if (!price) return 'Contact for Price'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(price))
  }

  return (
    <div>
      {/* Breadcrumb */}
      <nav className="flex mb-8" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <Link href="/" className="text-gray-700 hover:text-blue-600">
              Home
            </Link>
          </li>
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <Link href="/products" className="text-gray-700 hover:text-blue-600">
                Products
              </Link>
            </div>
          </li>
          {category.breadcrumb && category.breadcrumb.map((crumb: any) => (
            <li key={crumb.id}>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link 
                  href={`/products/category/${crumb.slug}`}
                  className="text-gray-700 hover:text-blue-600"
                >
                  {crumb.name}
                </Link>
              </div>
            </li>
          ))}
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-gray-500">{category.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Category Header */}
      <div className="mb-8">
        <div className="flex items-start space-x-6">
          {category.image && (
            <div className="flex-shrink-0">
              <Image
                src={category.image}
                alt={category.name}
                width={120}
                height={120}
                className="w-30 h-30 object-cover rounded-lg"
              />
            </div>
          )}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{category.name}</h1>
            {category.description && (
              <p className="text-lg text-gray-600 mb-4">{category.description}</p>
            )}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>{category._count?.totalProducts || category._count?.products || 0} products</span>
              {category._count?.children > 0 && (
                <span>{category._count.children} subcategories</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Subcategories */}
      {category.children && category.children.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Subcategories</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {category.children.map((subcategory: ProductCategoryWithRelations) => (
              <Link
                key={subcategory.id}
                href={`/products/category/${subcategory.slug}`}
                className="group block bg-white rounded-lg border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 overflow-hidden"
              >
                {/* 统一的卡片结构 */}
                <div className="p-4 text-center h-full flex flex-col">
                 
                  {/* 标题区域 - 固定最小高度确保对齐 */}
                  <div className="mb-2 flex-1 flex items-center justify-center">
                    <h3 className="font-medium text-gray-900 text-sm line-clamp-2 text-center min-h-[2.5rem] leading-5 group-hover:text-blue-600 transition-colors">
                      {subcategory.name}
                    </h3>
                  </div>

                  {/* 产品数量 - 固定在底部 */}
                  <div className="flex-shrink-0">
                    <p className="text-xs text-gray-500">
                      {subcategory._count?.totalProducts || subcategory._count?.products || 0} products
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Products Grid */}
      {category.products && category.products.length > 0 ? (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Products</h2>
            <div className="text-sm text-gray-500">
              Showing {category.products.length} of {category._count?.products || 0} products
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {category.products.map((product: ProductWithRelations) => (
              <div key={product.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg hover:border-blue-300 transition-all duration-300 flex flex-col h-full">
                {/* Product Image */}
                <div className="relative bg-gray-100 flex-shrink-0">
                  {product.images && product.images.length > 0 ? (
                    <Image
                      src={product.images[0].url}
                      alt={product.images[0].alt || product.name}
                      width={400}
                      height={280}
                      className="w-full h-56 object-cover"
                    />
                  ) : (
                    <div className="w-full h-56 bg-gray-100 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2">
                          <span className="text-gray-400 text-xl">📦</span>
                        </div>
                        <span className="text-gray-500 text-sm">No Image</span>
                      </div>
                    </div>
                  )}

                  {/* Featured Badge */}
                  {product.featured && (
                    <div className="absolute top-3 right-3">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 shadow-sm">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                {/* Product Info - 使用flex-1确保内容区域填满剩余空间 */}
                <div className="p-5 flex flex-col flex-1">
                  {/* 标题区域 - 固定高度 */}
                  <div className="mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 min-h-[3.5rem] leading-7">
                      {product.name}
                    </h3>
                  </div>

                  {/* 描述区域 - 固定高度 */}
                  <div className="mb-4 flex-1">
                    <p className="text-gray-600 text-sm line-clamp-3 min-h-[4.5rem] leading-6">
                      {product.excerpt || product.description}
                    </p>
                  </div>

                  {/* 底部区域 - 固定在底部 */}
                  <div className="mt-auto">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-lg font-bold text-blue-600">
                        {formatPrice(product.price)}
                      </div>
                      <Link
                        href={`/products/${category.slug}/${product.slug}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                      >
                        View Details
                      </Link>
                    </div>

                    {/* SKU信息 - 可选显示 */}
                    {product.sku && (
                      <div className="pt-3 border-t border-gray-100">
                        <p className="text-xs text-gray-500">SKU: {product.sku}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {category.pagination && category.pagination.totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <nav className="flex items-center space-x-2">
                {category.pagination.hasPrevPage && (
                  <Link
                    href={`/products/category/${category.slug}?page=${page - 1}`}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Previous
                  </Link>
                )}
                
                <span className="px-3 py-2 text-sm font-medium text-gray-700">
                  Page {page} of {category.pagination.totalPages}
                </span>

                {category.pagination.hasNextPage && (
                  <Link
                    href={`/products/category/${category.slug}?page=${page + 1}`}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Next
                  </Link>
                )}
              </nav>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl text-gray-300 mb-4">📦</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products in this category</h3>
          <p className="text-gray-500 mb-4">
            This category doesn't have any products yet.
          </p>
          <Link
            href="/products"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Browse All Products
          </Link>
        </div>
      )}
    </div>
  )
}
