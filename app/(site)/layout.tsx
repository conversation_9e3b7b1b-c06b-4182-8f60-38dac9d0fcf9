'use client'

import Link from 'next/link'
import { useThemeConfig } from '@/app/hooks/useThemeConfig'

export default function SiteLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { config, loading } = useThemeConfig()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!config || !config.header || !config.footer) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-white">Failed to load configuration</div>
      </div>
    )
  }
  return (
    <div
      className="min-h-screen"
      style={{
        backgroundImage: 'url(/images/page-bk.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center center',
        backgroundAttachment: 'fixed',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Professional Header */}
      <header className="bg-slate-800/95 backdrop-blur-sm border-b border-slate-700 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Top Bar */}
          <div className="flex justify-between items-center py-2 text-xs border-b border-slate-700/50">
            <div className="text-slate-300">
              {config.header.topBar.leftText}
            </div>
            <div className="flex items-center space-x-4 text-slate-300">
              <span>{config.header.topBar.rightEmail}</span>
              <span>{config.header.topBar.rightPhone}</span>
              {/* <Link href="/auth/login" className="hover:text-white transition-colors">
                Sign In
              </Link> */}
            </div>
          </div>
          
          {/* Main Navigation */}
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-3">
                {/* Logo Container - Different styling based on type */}
                {config.header.brand.logo?.type === 'image' ? (
                  // Image logo - no background, full image display
                  <div className="w-10 h-10 flex items-center justify-center">
                    <img
                      src={config.header.brand.logo.value}
                      alt={config.header.brand.logo.alt || config.header.brand.name}
                      className="max-w-10 max-h-10 object-contain"
                    />
                  </div>
                ) : (
                  // Text and Icon logos - with blue background
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                    {config.header.brand.logo?.type === 'text' && (
                      <span className="text-white font-bold text-lg">{config.header.brand.logo.value}</span>
                    )}
                    {config.header.brand.logo?.type === 'icon' && (
                      <span className="text-white text-lg">{config.header.brand.logo.value}</span>
                    )}
                    {/* Fallback for old format or missing logo */}
                    {(!config.header.brand.logo || typeof config.header.brand.logo === 'string') && (
                      <span className="text-white font-bold text-lg">
                        {typeof config.header.brand.logo === 'string' ? config.header.brand.logo : 'I'}
                      </span>
                    )}
                  </div>
                )}
                <div>
                  <div className="text-xl font-bold text-white tracking-wide">{config.header.brand.name}</div>
                  <div className="text-xs text-slate-300 -mt-1">{config.header.brand.tagline}</div>
                </div>
              </Link>
            </div>
            
            <nav className="hidden lg:flex items-center space-x-8">
              {config.header.navigation
                .filter(item => item.enabled)
                .map((item, index) => (
                  <Link
                    key={index}
                    href={item.href}
                    className="text-slate-200 hover:text-white transition-colors font-medium"
                  >
                    {item.name}
                  </Link>
                ))}
            </nav>
            
            <div className="flex items-center space-x-3">
              <Link
                href={config.header.ctaButton.href}
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:from-blue-700 hover:to-blue-800 transition-all shadow-md"
              >
                {config.header.ctaButton.text}
              </Link>
              {/* <Link
                href="/admin"
                className="border border-slate-600 text-slate-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-slate-700 hover:text-white transition-all"
              >
                Admin
              </Link> */}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content with Transparent Container */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-transparent">
          {children}
        </div>
      </main>

      {/* Professional Footer */}
      <footer className="bg-slate-900/95 backdrop-blur-sm border-t border-slate-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                {/* Footer Logo Container - Different styling based on type */}
                {config.header.brand.logo?.type === 'image' ? (
                  // Image logo - no background, full image display
                  <div className="w-8 h-8 flex items-center justify-center">
                    <img
                      src={config.header.brand.logo.value}
                      alt={config.header.brand.logo.alt || config.footer.brand.name}
                      className="max-w-8 max-h-8 object-contain"
                    />
                  </div>
                ) : (
                  // Text and Icon logos - with blue background
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                    {config.header.brand.logo?.type === 'text' && (
                      <span className="text-white font-bold">{config.header.brand.logo.value}</span>
                    )}
                    {config.header.brand.logo?.type === 'icon' && (
                      <span className="text-white text-sm">{config.header.brand.logo.value}</span>
                    )}
                    {/* Fallback for old format or missing logo */}
                    {(!config.header.brand.logo || typeof config.header.brand.logo === 'string') && (
                      <span className="text-white font-bold">
                        {typeof config.header.brand.logo === 'string' ? config.header.brand.logo : config.footer.brand.name.charAt(0)}
                      </span>
                    )}
                  </div>
                )}
                <div>
                  <h3 className="text-lg font-bold text-white">{config.footer.brand.name}</h3>
                  <div className="text-xs text-slate-300 -mt-1">{config.footer.brand.tagline}</div>
                </div>
              </div>
              <p className="text-slate-300 mb-4 text-sm leading-relaxed">
                {config.footer.brand.description}
              </p>
            </div>
            
            <div>
              <h4 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
                {config.footer.productLinks.title}
              </h4>
              <ul className="space-y-2 text-sm">
                {config.footer.productLinks.links
                  .filter(link => link.enabled)
                  .map((link, index) => (
                    <li key={index}>
                      <Link href={link.href} className="text-slate-300 hover:text-white transition-colors">
                        {link.name}
                      </Link>
                    </li>
                  ))}
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
                {config.footer.supportLinks.title}
              </h4>
              <ul className="space-y-2 text-sm">
                {config.footer.supportLinks.links
                  .filter(link => link.enabled)
                  .map((link, index) => (
                    <li key={index}>
                      <Link href={link.href} className="text-slate-300 hover:text-white transition-colors">
                        {link.name}
                      </Link>
                    </li>
                  ))}
              </ul>
            </div>

            {/* Custom Link Groups */}
            {config.footer.customGroups?.filter(group => group.enabled).map((group, groupIndex) => (
              <div key={groupIndex}>
                <h4 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
                  {group.title}
                </h4>
                <ul className="space-y-2 text-sm">
                  {group.links
                    .filter(link => link.enabled)
                    .map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <Link href={link.href} className="text-slate-300 hover:text-white transition-colors">
                          {link.name}
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            ))}
          </div>
          
          {/* Bottom Footer */}
          <div className="mt-12 pt-8 border-t border-slate-700">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-slate-400 text-sm mb-4 md:mb-0">
                {config.footer.bottomBar.copyright} ｜ <a href='https://www.instai.cc/'> POWERED BY INST Ai </a>
              </div>
              <div className="flex items-center space-x-6 text-sm text-slate-400">
                {config.footer.bottomBar.legalLinks
                  .filter(link => link.enabled)
                  .map((link, index) => (
                    <Link
                      key={index}
                      href={link.href}
                      className="hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
