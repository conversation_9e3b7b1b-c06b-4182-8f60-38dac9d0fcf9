import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  const isProduction = process.env.NODE_ENV === 'production'
  
  // Different robots.txt for production vs development
  const robotsContent = isProduction
    ? `# Production robots.txt for Enterprise CMS
User-agent: *
Allow: /

# Allow all crawlers to access public content
Allow: /products
Allow: /products/*
Allow: /blog
Allow: /blog/*
Allow: /contact
Allow: /about

# Disallow admin and API routes
Disallow: /admin
Disallow: /admin/*
Disallow: /api
Disallow: /api/*
Disallow: /auth
Disallow: /auth/*

# Disallow private files
Disallow: /*.json$
Disallow: /*.xml$
Disallow: /*.txt$
Disallow: /private/
Disallow: /temp/
Disallow: /_next/
Disallow: /node_modules/

# Allow specific files
Allow: /sitemap.xml
Allow: /robots.txt
Allow: /favicon.ico
Allow: /manifest.json

# Crawl delay (optional - be respectful)
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Additional directives for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# Block aggressive crawlers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

# Social media crawlers (allow for rich previews)
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /`
    : `# Development robots.txt for Enterprise CMS
User-agent: *
Disallow: /

# Block all crawlers in development
# This prevents indexing of development/staging sites

# Allow only essential files for testing
Allow: /sitemap.xml
Allow: /robots.txt
Allow: /favicon.ico

# Sitemap location (for testing)
Sitemap: ${baseUrl}/sitemap.xml`

  return new NextResponse(robotsContent, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
    },
  })
}
