'use client'

import { useState, useEffect } from 'react'

interface GlobalSettings {
  site_name?: { value: string; type: string }
  site_url?: { value: string; type: string }
  site_description?: { value: string; type: string }
  admin_email?: { value: string; type: string }
  default_meta_title?: { value: string; type: string }
  default_meta_description?: { value: string; type: string }
  default_meta_keywords?: { value: string; type: string }
  og_image?: { value: string; type: string }
  twitter_handle?: { value: string; type: string }
  facebook_url?: { value: string; type: string }
  twitter_url?: { value: string; type: string }
  linkedin_url?: { value: string; type: string }
  instagram_url?: { value: string; type: string }
}

// 默认设置
const DEFAULT_SETTINGS: GlobalSettings = {
  site_name: { value: 'InstAI CMS', type: 'string' },
  site_url: { value: 'https://instai-cms.com', type: 'string' },
  site_description: { value: 'Professional Content Management System', type: 'string' },
  default_meta_title: { value: 'InstAI CMS - Professional Content Management System', type: 'string' },
  default_meta_description: { value: 'A powerful, flexible content management system that helps businesses create, manage, and scale their digital presence with ease.', type: 'string' },
}

export function useGlobalSettings() {
  const [settings, setSettings] = useState<GlobalSettings>(DEFAULT_SETTINGS)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      // 这里我们从公开API获取设置，而不是需要认证的admin API
      const response = await fetch('/api/settings/public', {
        cache: 'no-store'
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setSettings(prev => ({
            ...prev,
            ...result.data
          }))
        }
      }
    } catch (error) {
      console.error('Failed to fetch global settings:', error)
      // 使用默认设置
    } finally {
      setLoading(false)
    }
  }

  // 辅助函数来获取设置值
  const getSetting = (key: keyof GlobalSettings, defaultValue: string = '') => {
    return settings[key]?.value || defaultValue
  }

  // 获取站点名称
  const getSiteName = () => getSetting('site_name', 'InstAI CMS')
  
  // 获取默认Meta标题
  const getDefaultMetaTitle = () => getSetting('default_meta_title', 'InstAI CMS - Professional Content Management System')
  
  // 获取默认Meta描述
  const getDefaultMetaDescription = () => getSetting('default_meta_description', 'A powerful, flexible content management system')

  // 生成页面标题（用于SEO）
  const generatePageTitle = (pageTitle?: string) => {
    const siteName = getSiteName()
    if (!pageTitle) return getDefaultMetaTitle()
    return `${pageTitle} | ${siteName}`
  }

  return {
    settings,
    loading,
    getSetting,
    getSiteName,
    getDefaultMetaTitle,
    getDefaultMetaDescription,
    generatePageTitle,
  }
}

export type { GlobalSettings }
