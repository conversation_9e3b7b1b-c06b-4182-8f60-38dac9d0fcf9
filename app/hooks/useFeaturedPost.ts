import { useState, useEffect } from 'react'

interface FeaturedPost {
  id: string
  title: string
  slug: string
  status: string
  publishedAt: string | null
  author: {
    name: string
    username: string
  }
}

interface UseFeaturedPostReturn {
  featuredPost: FeaturedPost | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  setFeaturedPost: (postId: string) => Promise<boolean>
  removeFeaturedPost: () => Promise<boolean>
}

export function useFeaturedPost(): UseFeaturedPostReturn {
  const [featuredPost, setFeaturedPostState] = useState<FeaturedPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFeaturedPost = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/posts/featured')
      const result = await response.json()
      
      if (result.success) {
        setFeaturedPostState(result.data)
      } else {
        setError(result.error || 'Failed to fetch featured post')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Fetch featured post error:', err)
    } finally {
      setLoading(false)
    }
  }

  const setFeaturedPost = async (postId: string): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch('/api/posts/featured', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postId }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        setFeaturedPostState(result.data)
        return true
      } else {
        setError(result.error || 'Failed to set featured post')
        return false
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Set featured post error:', err)
      return false
    }
  }

  const removeFeaturedPost = async (): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch('/api/posts/featured', {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        setFeaturedPostState(null)
        return true
      } else {
        setError(result.error || 'Failed to remove featured post')
        return false
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Remove featured post error:', err)
      return false
    }
  }

  useEffect(() => {
    fetchFeaturedPost()
  }, [])

  return {
    featuredPost,
    loading,
    error,
    refetch: fetchFeaturedPost,
    setFeaturedPost,
    removeFeaturedPost,
  }
}
