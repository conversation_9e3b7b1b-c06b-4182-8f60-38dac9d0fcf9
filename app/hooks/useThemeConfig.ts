'use client'

import { useState, useEffect } from 'react'

interface HeaderConfig {
  topBar: {
    leftText: string
    rightEmail: string
    rightPhone: string
  }
  brand: {
    name: string
    tagline: string
    logo: {
      type: 'text' | 'image' | 'icon'
      value: string
      alt?: string
    }
  }
  navigation: Array<{
    name: string
    href: string
    enabled: boolean
  }>
  ctaButton: {
    text: string
    href: string
  }
}

interface FooterConfig {
  brand: {
    name: string
    tagline: string
    description: string
  }
  socialMedia: Array<{
    name: string
    href: string
    enabled: boolean
  }>
  productLinks: {
    title: string
    links: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }
  supportLinks: {
    title: string
    links: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }
  customGroups?: Array<{
    title: string
    enabled: boolean
    links: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }>
  bottomBar: {
    copyright: string
    legalLinks: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }
}

interface ThemeConfig {
  header: HeaderConfig
  footer: FooterConfig
}

const defaultConfig: ThemeConfig = {
  header: {
    topBar: {
      leftText: 'Professional Content Management System · Trusted by 50K+ Websites',
      rightEmail: '<EMAIL>',
      rightPhone: '+****************'
    },
    brand: {
      name: 'InstAI CMS',
      tagline: 'Content Management System',
      logo: {
        type: 'text',
        value: 'I',
        alt: 'InstAI CMS Logo'
      }
    },
    navigation: [
      { name: 'Home', href: '/', enabled: true },
      { name: 'Products', href: '/products', enabled: true },
      { name: 'About', href: '/about', enabled: true },
      { name: 'Blog', href: '/blog', enabled: true },
      { name: 'Contact', href: '/contact', enabled: true }
    ],
    ctaButton: {
      text: 'Get Started',
      href: '/contact'
    }
  },
  footer: {
    brand: {
      name: 'InstAI CMS',
      tagline: 'Content Management System',
      description: 'InstAI CMS is a powerful, flexible content management system that helps businesses create, manage, and scale their digital presence with ease. Trusted by over 50,000 websites worldwide.'
    },
    socialMedia: [
      { name: 'LinkedIn', href: '#', enabled: true },
      { name: 'Twitter', href: '#', enabled: true },
      { name: 'GitHub', href: '#', enabled: true }
    ],
    productLinks: {
      title: 'Product',
      links: [
        { name: 'Features', href: '/products', enabled: true },
        { name: 'Templates', href: '/products', enabled: true },
        { name: 'Integrations', href: '/products', enabled: true },
        { name: 'Pricing', href: '/contact', enabled: true }
      ]
    },
    supportLinks: {
      title: 'Support',
      links: [
        { name: 'Help Center', href: '/contact', enabled: true },
        { name: 'Documentation', href: '/blog', enabled: true },
        { name: 'Contact Support', href: '/contact', enabled: true },
        { name: 'System Status', href: '/about', enabled: true }
      ]
    },
    customGroups: [],
    bottomBar: {
      copyright: '© 2024 InstAI CMS. All rights reserved. | Professional Content Management Solutions',
      legalLinks: [
        { name: 'Privacy Policy', href: '/privacy', enabled: true },
        { name: 'Terms of Service', href: '/terms', enabled: true },
        { name: 'Sitemap', href: '/sitemap', enabled: true }
      ]
    }
  }
}

export function useThemeConfig() {
  const [config, setConfig] = useState<ThemeConfig>(defaultConfig)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/themes/config')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            const data = result.data
            if (data.header && data.footer) {
              setConfig({
                header: data.header,
                footer: data.footer
              })
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch theme config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchConfig()
  }, [])

  return { config, loading }
}

export type { HeaderConfig, FooterConfig, ThemeConfig }
