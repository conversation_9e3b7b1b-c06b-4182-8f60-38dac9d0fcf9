import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'
import { prisma } from './prisma'
import { Role } from '@/app/types'

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'

export interface JWTPayload {
  userId: string
  email: string
  role: string
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// Generate JWT token
export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload
  } catch (error) {
    return null
  }
}

// Get user from token
export async function getUserFromToken(token: string) {
  const payload = verifyToken(token)
  if (!payload) return null

  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    select: {
      id: true,
      email: true,
      username: true,
      name: true,
      role: true,
      avatar: true,
      bio: true,
      createdAt: true,
      updatedAt: true,
    },
  })

  return user
}

// Get user from request
export async function getUserFromRequest(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '')
  
  if (!token) return null
  
  return getUserFromToken(token)
}

// Check if user has required role
export function hasRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    [Role.USER]: 0,
    [Role.EDITOR]: 1,
    [Role.ADMIN]: 2,
  }

  return roleHierarchy[userRole as keyof typeof roleHierarchy] >= 
         roleHierarchy[requiredRole as keyof typeof roleHierarchy]
}

// Middleware helper for protected routes
export async function requireAuth(request: NextRequest, requiredRole?: string) {
  const user = await getUserFromRequest(request)

  if (!user) {
    return { error: 'Authentication required', status: 401 }
  }

  if (requiredRole && !hasRole(user.role, requiredRole)) {
    return { error: 'Insufficient permissions', status: 403 }
  }

  return { user }
}

// Create session
export async function createSession(userId: string) {
  const sessionToken = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '30d' })
  
  const session = await prisma.session.create({
    data: {
      sessionToken,
      userId,
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  })

  return session
}

// Delete session
export async function deleteSession(sessionToken: string) {
  await prisma.session.delete({
    where: { sessionToken },
  })
}
