// Client-side API utilities with authentication

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl
  }

  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    // Get token from localStorage or cookies
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth-token') || this.getCookie('auth-token')
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
    }

    return headers
  }

  private getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null
    
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null
    }
    return null
  }

  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      // Construct URL properly
      let url = `${this.baseUrl}${endpoint}`

      if (params) {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value))
          }
        })
        if (searchParams.toString()) {
          url += `?${searchParams.toString()}`
        }
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include', // Include cookies
      })

      const result = await response.json()

      // If the response has an error status but the JSON parsing succeeded,
      // return the parsed error response
      if (!response.ok && result.error) {
        return {
          success: false,
          error: result.error,
        }
      }

      return result
    } catch (error) {
      console.error('API GET error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`

      const response = await fetch(url, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        credentials: 'include',
        body: data ? JSON.stringify(data) : undefined,
      })

      const result = await response.json()

      // If the response has an error status but the JSON parsing succeeded,
      // return the parsed error response
      if (!response.ok && result.error) {
        return {
          success: false,
          error: result.error,
        }
      }

      return result
    } catch (error) {
      console.error('API POST error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`

      const response = await fetch(url, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        credentials: 'include',
        body: data ? JSON.stringify(data) : undefined,
      })

      return await response.json()
    } catch (error) {
      console.error('API PUT error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`

      const response = await fetch(url, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      })

      return await response.json()
    } catch (error) {
      console.error('API DELETE error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  // Upload file with authentication
  async upload<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, String(value))
        })
      }

      const headers: HeadersInit = {}
      
      // Get token for authorization
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth-token') || this.getCookie('auth-token')
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
      }

      const url = `${this.baseUrl}${endpoint}`

      const response = await fetch(url, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: formData,
      })

      return await response.json()
    } catch (error) {
      console.error('API Upload error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload error',
      }
    }
  }
}

// Create a default API client instance
export const apiClient = new ApiClient('/api')

// Convenience functions for common operations
export const api = {
  // Auth
  login: (credentials: { email: string; password: string }) =>
    apiClient.post('/auth/login', credentials),
  
  logout: () =>
    apiClient.post('/auth/logout'),
  
  me: () =>
    apiClient.get('/auth/me'),

  // Users
  getUsers: (params?: { page?: number; limit?: number; role?: string; search?: string }) =>
    apiClient.get('/users', params),
  
  getUser: (id: string) =>
    apiClient.get(`/users/${id}`),
  
  createUser: (data: any) =>
    apiClient.post('/users', data),
  
  updateUser: (id: string, data: any) =>
    apiClient.put(`/users/${id}`, data),
  
  deleteUser: (id: string) =>
    apiClient.delete(`/users/${id}`),

  // Comments
  getComments: (params?: { page?: number; limit?: number; status?: string; search?: string }) =>
    apiClient.get('/comments', params),
  
  getComment: (id: string) =>
    apiClient.get(`/comments/${id}`),
  
  updateComment: (id: string, data: any) =>
    apiClient.put(`/comments/${id}`, data),
  
  deleteComment: (id: string) =>
    apiClient.delete(`/comments/${id}`),

  // Settings
  getSettings: () =>
    apiClient.get('/settings'),
  
  updateSettings: (settings: any) =>
    apiClient.put('/settings', { settings }),

  // Categories
  getCategories: (params?: { search?: string }) =>
    apiClient.get('/categories', params),
  
  createCategory: (data: any) =>
    apiClient.post('/categories', data),
  
  updateCategory: (id: string, data: any) =>
    apiClient.put(`/categories/${id}`, data),
  
  deleteCategory: (id: string) =>
    apiClient.delete(`/categories/${id}`),

  // Tags
  getTags: (params?: { search?: string }) =>
    apiClient.get('/tags', params),
  
  createTag: (data: any) =>
    apiClient.post('/tags', data),
  
  updateTag: (id: string, data: any) =>
    apiClient.put(`/tags/${id}`, data),
  
  deleteTag: (id: string) =>
    apiClient.delete(`/tags/${id}`),

  // Media
  getMedia: (params?: { search?: string; type?: string }) =>
    apiClient.get('/media', params),
  
  uploadFile: (file: File) =>
    apiClient.upload('/upload', file),
  
  deleteMedia: (id: string) =>
    apiClient.delete(`/media/${id}`),

  // Themes
  getThemeConfig: () =>
    apiClient.get('/themes/config'),
  
  updateThemeConfig: (config: any) =>
    apiClient.put('/themes/config', config),
  
  resetThemeConfig: () =>
    apiClient.post('/themes/config/reset'),
}

export default apiClient
