import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role } from '@/app/types'
import fs from 'fs'
import path from 'path'

// GET /api/media/[id] - Get single media file
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params

    const media = await prisma.media.findUnique({
      where: { id },
    })

    if (!media) {
      return NextResponse.json(
        { success: false, error: 'Media file not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: media,
    })
  } catch (error) {
    console.error('Get media error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/media/[id] - Update media file metadata
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params
    const body = await request.json()

    // Check if media exists
    const existingMedia = await prisma.media.findUnique({
      where: { id },
    })

    if (!existingMedia) {
      return NextResponse.json(
        { success: false, error: 'Media file not found' },
        { status: 404 }
      )
    }

    const { filename, alt, caption } = body

    // Update media metadata
    const updateData: any = {}
    if (filename) updateData.filename = filename
    if (alt !== undefined) updateData.alt = alt
    if (caption !== undefined) updateData.caption = caption

    const media = await prisma.media.update({
      where: { id },
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      data: media,
      message: 'Media file updated successfully',
    })
  } catch (error) {
    console.error('Update media error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/media/[id] - Delete media file
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params

    // Check if media exists
    const existingMedia = await prisma.media.findUnique({
      where: { id },
    })

    if (!existingMedia) {
      return NextResponse.json(
        { success: false, error: 'Media file not found' },
        { status: 404 }
      )
    }

    // Check if media is being used
    const usageCount = await prisma.post.count({
      where: {
        OR: [
          { featuredImage: existingMedia.url },
          { content: { contains: existingMedia.url } },
        ],
      },
    })

    if (usageCount > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot delete media file. It is being used in ${usageCount} post(s).` 
        },
        { status: 400 }
      )
    }

    try {
      // Delete physical file
      // Extract the file path from URL (remove leading slash if present)
      const relativePath = existingMedia.url.startsWith('/') ? existingMedia.url.slice(1) : existingMedia.url
      const filePath = path.join(process.cwd(), 'public', relativePath)
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
      }
    } catch (fileError) {
      console.error('Error deleting physical file:', fileError)
      // Continue with database deletion even if file deletion fails
    }

    // Delete from database
    await prisma.media.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: 'Media file deleted successfully',
    })
  } catch (error) {
    console.error('Delete media error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
