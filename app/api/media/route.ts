import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { getPaginationData } from '@/app/lib/utils'
import { Role } from '@/app/types'

// GET /api/media - Get all media files
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') as string
    const type = searchParams.get('type') as string

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { originalName: { contains: search } },
        { alt: { contains: search } },
        { caption: { contains: search } },
      ]
    }

    if (type && type !== 'all') {
      if (type === 'image') {
        where.mimeType = { startsWith: 'image/' }
      } else if (type === 'video') {
        where.mimeType = { startsWith: 'video/' }
      } else if (type === 'audio') {
        where.mimeType = { startsWith: 'audio/' }
      } else if (type === 'document') {
        where.OR = [
          { mimeType: { contains: 'pdf' } },
          { mimeType: { contains: 'document' } },
          { mimeType: { contains: 'word' } },
          { mimeType: { contains: 'spreadsheet' } },
          { mimeType: { contains: 'excel' } },
          { mimeType: { contains: 'text' } }
        ]
      }
    }

    // Get media files
    const [media, total] = await Promise.all([
      prisma.media.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.media.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: media,
      pagination,
    })
  } catch (error) {
    console.error('Get media error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
