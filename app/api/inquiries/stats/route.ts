import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role, InquiryStatus } from '@/app/types'

// GET /api/inquiries/stats - Get inquiry statistics
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days

    const periodDays = parseInt(period)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - periodDays)

    // Get total counts by status
    const statusCounts = await prisma.inquiry.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
    })

    // Get recent inquiries count
    const recentCount = await prisma.inquiry.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    })

    // Get inquiries by product (top 10)
    const productInquiries = await prisma.inquiry.groupBy({
      by: ['productId'],
      _count: {
        id: true,
      },
      where: {
        productId: {
          not: null,
        },
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 10,
    })

    // Get product details for the grouped results
    const productIds = productInquiries.map(item => item.productId).filter(Boolean)
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: productIds as string[],
        },
      },
      select: {
        id: true,
        name: true,
        slug: true,
      },
    })

    // Combine product data with inquiry counts
    const productStats = productInquiries.map(item => {
      const product = products.find(p => p.id === item.productId)
      return {
        product,
        count: item._count.id,
      }
    })

    // Get daily inquiry counts for the period
    const dailyStats = await prisma.$queryRaw`
      SELECT 
        DATE(createdAt) as date,
        COUNT(*) as count
      FROM inquiries 
      WHERE createdAt >= ${startDate}
      GROUP BY DATE(createdAt)
      ORDER BY date ASC
    `

    // Format status counts
    const statusStats = Object.values(InquiryStatus).map(status => {
      const statusData = statusCounts.find(item => item.status === status)
      return {
        status,
        count: statusData?._count.id || 0,
      }
    })

    // Calculate conversion rate (QUALIFIED + CLOSED / TOTAL)
    const totalInquiries = statusCounts.reduce((sum, item) => sum + item._count.id, 0)
    const convertedInquiries = statusCounts
      .filter(item => item.status === InquiryStatus.QUALIFIED || item.status === InquiryStatus.CLOSED)
      .reduce((sum, item) => sum + item._count.id, 0)
    
    const conversionRate = totalInquiries > 0 ? (convertedInquiries / totalInquiries) * 100 : 0

    const stats = {
      total: totalInquiries,
      recent: recentCount,
      conversionRate: Math.round(conversionRate * 100) / 100,
      statusBreakdown: statusStats,
      productStats,
      dailyStats,
      period: periodDays,
    }

    return NextResponse.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Get inquiry stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
