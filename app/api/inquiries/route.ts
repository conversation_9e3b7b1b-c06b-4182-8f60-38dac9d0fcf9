import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { getPaginationData, isValidEmail } from '@/app/lib/utils'
import { Role, InquiryStatus } from '@/app/types'

// GET /api/inquiries - Get all inquiries (Admin only)
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') as string
    const productId = searchParams.get('productId') as string
    const search = searchParams.get('search') as string
    const dateFrom = searchParams.get('dateFrom') as string
    const dateTo = searchParams.get('dateTo') as string

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (status && Object.values(InquiryStatus).includes(status as any)) {
      where.status = status
    }

    if (productId) {
      where.productId = productId
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { company: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom)
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo)
      }
    }

    // Get inquiries with relations
    const [inquiries, total] = await Promise.all([
      prisma.inquiry.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              images: {
                take: 1,
                orderBy: { sortOrder: 'asc' },
              },
            },
          },
        },
      }),
      prisma.inquiry.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: inquiries,
      pagination,
    })
  } catch (error) {
    console.error('Get inquiries error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/inquiries - Submit new inquiry (Public)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      email,
      phone,
      company,
      message,
      productId,
      source,
    } = body

    // Validation
    if (!name || !email || !message) {
      return NextResponse.json(
        { success: false, error: 'Name, email and message are required' },
        { status: 400 }
      )
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      )
    }

    if (message.length < 10) {
      return NextResponse.json(
        { success: false, error: 'Message must be at least 10 characters long' },
        { status: 400 }
      )
    }

    // Validate product exists if productId provided
    if (productId) {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: { id: true, name: true, status: true },
      })

      if (!product) {
        return NextResponse.json(
          { success: false, error: 'Product not found' },
          { status: 400 }
        )
      }

      if (product.status !== 'ACTIVE') {
        return NextResponse.json(
          { success: false, error: 'Product is not available' },
          { status: 400 }
        )
      }
    }

    // Rate limiting: Check for duplicate submissions
    const recentInquiry = await prisma.inquiry.findFirst({
      where: {
        email,
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        },
      },
    })

    if (recentInquiry) {
      return NextResponse.json(
        { success: false, error: 'Please wait before submitting another inquiry' },
        { status: 429 }
      )
    }

    // Create inquiry
    const inquiry = await prisma.inquiry.create({
      data: {
        name: name.trim(),
        email: email.toLowerCase().trim(),
        phone: phone?.trim() || null,
        company: company?.trim() || null,
        message: message.trim(),
        productId: productId || null,
        source: source || null,
        status: InquiryStatus.NEW,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    })

    // TODO: Send email notification to admin
    // await sendInquiryNotification(inquiry)

    return NextResponse.json({
      success: true,
      data: inquiry,
      message: 'Inquiry submitted successfully. We will contact you soon!',
    })
  } catch (error) {
    console.error('Submit inquiry error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
