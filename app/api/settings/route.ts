import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role } from '@/app/types'

// GET /api/settings - Get all settings
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.ADMIN)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const settings = await prisma.setting.findMany({
      orderBy: { key: 'asc' },
    })

    // Convert to key-value object for easier use
    const settingsObject = settings.reduce((acc, setting) => {
      let value = setting.value
      
      // Parse JSON values
      if (setting.type === 'json') {
        try {
          value = JSON.parse(setting.value)
        } catch {
          // Keep as string if parsing fails
        }
      } else if (setting.type === 'boolean') {
        value = setting.value === 'true'
      } else if (setting.type === 'number') {
        value = parseFloat(setting.value)
      }
      
      acc[setting.key] = {
        value,
        type: setting.type,
      }
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      data: settingsObject,
    })
  } catch (error) {
    console.error('Get settings error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/settings - Update multiple settings
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.ADMIN)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const body = await request.json()
    const { settings } = body

    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Settings object is required' },
        { status: 400 }
      )
    }

    // Update settings
    const updatePromises = Object.entries(settings).map(async ([key, config]: [string, any]) => {
      const { value, type = 'string' } = config
      
      let stringValue = value
      if (type === 'json') {
        stringValue = JSON.stringify(value)
      } else if (type === 'boolean') {
        stringValue = value ? 'true' : 'false'
      } else if (type === 'number') {
        stringValue = value.toString()
      } else {
        stringValue = value.toString()
      }

      return prisma.setting.upsert({
        where: { key },
        update: {
          value: stringValue,
          type,
        },
        create: {
          key,
          value: stringValue,
          type,
        },
      })
    })

    await Promise.all(updatePromises)

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
    })
  } catch (error) {
    console.error('Update settings error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
