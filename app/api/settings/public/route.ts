import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'

// 公开的设置键（不需要认证即可访问）
const PUBLIC_SETTINGS_KEYS = [
  'site_name',
  'site_url', 
  'site_description',
  'default_meta_title',
  'default_meta_description',
  'default_meta_keywords',
  'og_image',
  'twitter_handle',
  'facebook_url',
  'twitter_url',
  'linkedin_url',
  'instagram_url'
]

// GET /api/settings/public - Get public settings (no auth required)
export async function GET(request: NextRequest) {
  try {
    const settings = await prisma.setting.findMany({
      where: {
        key: {
          in: PUBLIC_SETTINGS_KEYS
        }
      },
      orderBy: { key: 'asc' },
    })

    // Convert to key-value object for easier use
    const settingsObject = settings.reduce((acc, setting) => {
      let value = setting.value
      
      // Parse JSON values
      if (setting.type === 'json') {
        try {
          value = JSON.parse(setting.value)
        } catch {
          // Keep as string if parsing fails
        }
      } else if (setting.type === 'boolean') {
        value = setting.value === 'true'
      } else if (setting.type === 'number') {
        value = parseFloat(setting.value)
      }
      
      acc[setting.key] = {
        value,
        type: setting.type,
      }
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      data: settingsObject,
    })
  } catch (error) {
    console.error('Get public settings error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
