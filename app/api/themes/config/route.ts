import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role } from '@/app/types'

// Default theme configuration
const DEFAULT_THEME_CONFIG = {
  header: {
    topBar: {
      leftText: 'Professional Content Management System · Trusted by 50K+ Websites',
      rightEmail: '<EMAIL>',
      rightPhone: '+****************'
    },
    brand: {
      name: 'InstAI CMS',
      tagline: 'Content Management System',
      logo: {
        type: 'text',
        value: 'I',
        alt: 'InstAI CMS Logo'
      }
    },
    navigation: [
      { name: 'Home', href: '/', enabled: true },
      { name: 'Products', href: '/products', enabled: true },
      { name: 'About', href: '/about', enabled: true },
      { name: 'Blog', href: '/blog', enabled: true },
      { name: 'Contact', href: '/contact', enabled: true }
    ],
    ctaButton: {
      text: 'Get Started',
      href: '/contact'
    }
  },
  footer: {
    brand: {
      name: 'InstAI CMS',
      tagline: 'Content Management System',
      description: 'InstAI CMS is a powerful, flexible content management system that helps businesses create, manage, and scale their digital presence with ease. Trusted by over 50,000 websites worldwide.'
    },
    socialMedia: [
      { name: 'LinkedIn', href: '#', enabled: true },
      { name: 'Twitter', href: '#', enabled: true },
      { name: 'GitHub', href: '#', enabled: true }
    ],
    productLinks: {
      title: 'Product',
      links: [
        { name: 'Features', href: '/products', enabled: true },
        { name: 'Templates', href: '/products', enabled: true },
        { name: 'Integrations', href: '/products', enabled: true },
        { name: 'Pricing', href: '/contact', enabled: true }
      ]
    },
    supportLinks: {
      title: 'Support',
      links: [
        { name: 'Help Center', href: '/contact', enabled: true },
        { name: 'Documentation', href: '/blog', enabled: true },
        { name: 'Contact Support', href: '/contact', enabled: true },
        { name: 'System Status', href: '/about', enabled: true }
      ]
    },
    customGroups: [],
    bottomBar: {
      copyright: '© 2024 InstAI CMS. All rights reserved. | Professional Content Management Solutions',
      legalLinks: [
        { name: 'Privacy Policy', href: '/privacy', enabled: true },
        { name: 'Terms of Service', href: '/terms', enabled: true },
        { name: 'Sitemap', href: '/sitemap', enabled: true }
      ]
    }
  },
  homepage: {
    hero: {
      carousel: {
        autoPlay: true,
        autoPlayInterval: 5,
        showDots: true,
        showArrows: true,
        slides: [
          {
            id: 'slide-1',
            title: 'Welcome to InstAI CMS',
            subtitle: 'Enterprise Content Management',
            description: 'Build powerful, scalable websites with our advanced CMS platform. Perfect for businesses of all sizes.',
            image: '/images/lb01.jpg',
            primaryButton: {
              text: 'Get Started',
              link: '/products'
            },
            secondaryButton: {
              text: 'Learn More',
              link: '/about'
            },
            isActive: true
          },
          {
            id: 'slide-2',
            title: 'Advanced Features',
            subtitle: 'Everything You Need',
            description: 'From content management to e-commerce, our platform provides all the tools you need to succeed online.',
            image: '/images/page-bk.jpg',
            primaryButton: {
              text: 'View Products',
              link: '/products'
            },
            secondaryButton: {
              text: 'Contact Sales',
              link: '/contact'
            },
            isActive: true
          },
          {
            id: 'slide-3',
            title: 'Expert Support',
            subtitle: '24/7 Customer Service',
            description: 'Get help when you need it with our dedicated support team and comprehensive documentation.',
            image: '/images/lb01.jpg',
            primaryButton: {
              text: 'Contact Us',
              link: '/contact'
            },
            secondaryButton: {
              text: 'View Blog',
              link: '/blog'
            },
            isActive: true
          }
        ]
      }
    },
    productShowcase: {
      title: 'WELCOME TO InstAI CMS!',
      subtitle: '',
      description: 'Discover our flagship products designed to power your digital transformation',
      viewAllButtonText: '全部产品',
      viewAllButtonLink: '/products'
    },
    companyIntro: {
      title: 'ABOUT OUR COMPANY',
      description1: 'If you are not familiar with InstAI CMS name and our reputation for excellence, please take a moment to browse our website and understand why buyers from all over the world consistently choose InstAI CMS as their primary source for content management solutions.',
      description2: 'InstAI CMS has been providing content management systems and services to individuals, resellers, service organizations and major corporations from all over the world. You can count on InstAI CMS for the supply of quality content management solutions.',
      missionTitle: 'Mission',
      missionDescription: 'To provide innovative, reliable, and user-friendly content management solutions that empower businesses to succeed in the digital world.',
      visionTitle: 'Vision',
      visionDescription: 'To be the leading global provider of content management systems, setting industry standards for innovation and customer satisfaction.',
      moreInfoButtonText: 'More Info',
      moreInfoButtonLink: '/about',
      contactButtonText: 'Contact Us',
      contactButtonLink: '/contact'
    },
    hotSaleProducts: {
      title: 'HOT SALE PRODUCTS',
      subtitle: '',
      description: 'To be competitive, InstAI CMS will have new products every year with its own design.',
      ctaTitle: '🔥 Limited Time Offers!',
      ctaDescription: 'Don\'t miss out on these exclusive deals. Our hot sale products are available for a limited time only. Upgrade your business with premium CMS solutions at unbeatable prices.',
      viewAllButtonText: 'View All Products',
      viewAllButtonLink: '/products',
      contactSalesButtonText: 'Contact Sales',
      contactSalesButtonLink: '/contact'
    }
  },
  products: {
    header: {
      title: 'Product Catalog',
      subtitle: 'Enterprise Solutions & Services',
      description: 'Comprehensive content management solutions designed for enterprise-level operations. Each product is built with 10+ years of industry expertise and proven reliability.'
    },
    sidebar: {
      categoriesTitle: 'Product Categories',
      allProductsText: 'All Products',
      hotProductsText: 'Hot Products',
      byCategoryText: 'By Category'
    },
    card: {
      featuredBadgeText: 'FEATURED',
      learnMoreButtonText: 'Learn More →',
      contactForPriceText: 'Contact for Price',
      productCodeLabel: 'Product Code:'
    },

    emptyState: {
      title: 'Product Catalog Coming Soon',
      description: 'We\'re currently preparing our comprehensive product catalog. Our enterprise solutions will be available shortly.',
      contactButtonText: 'Contact Sales Team'
    },
    featured: {
      pageTitle: 'Hot Products',
      pageSubtitle: 'Featured & Popular Items',
      pageDescription: 'Discover our most popular and featured products, carefully selected for their quality and customer satisfaction.',
      badgeTitle: '🔥 Hot Products',
      badgeDescription: 'These products are currently trending and highly recommended by our customers.',
      emptyTitle: 'No Hot Products Available',
      emptyDescription: 'We\'re currently updating our featured products. Please check back soon or browse all products.',
      browseAllButtonText: 'Browse All Products'
    },
    category: {
      backToProductsText: '← Back to Products',
      productsInCategoryText: 'Products in',
      emptyTitle: 'No Products in This Category',
      emptyDescription: 'This category is currently empty. Please check back later or browse other categories.',
      browseAllButtonText: 'Browse All Products',
      paginationPrevText: 'Previous',
      paginationNextText: 'Next'
    },
    detail: {
      backToCategoryText: '← Back to Category',
      backToProductsText: '← Back to Products',
      priceLabel: 'Price',
      skuLabel: 'SKU',
      categoryLabel: 'Category',
      statusLabel: 'Status',
      inquiryFormTitle: 'Product Inquiry',
      relatedProductsTitle: 'Related Products',
      specificationsTitle: 'Specifications',
      descriptionTitle: 'Description',
      imagesTitle: 'Product Images',
      inquirySection: {
        title: 'Interested in this product?',
        description: 'Get detailed information, pricing, and availability for {productName}. Our team will respond to your inquiry within 24 hours.',
        features: [
          'Free consultation and quote',
          'Expert technical support',
          'Custom configuration available',
          '24-hour response guarantee'
        ]
      }
    },
    filters: {
      showCategories: true,
      showSearch: true,
      showSort: true
    },
    display: {
      itemsPerPage: 12,
      showPagination: true,
      gridColumns: 2
    }
  },
  about: {
    hero: {
      title: 'About InstAI CMS',
      subtitle: 'A Decade of Enterprise Excellence',
      description: 'Since 2014, InstAI CMS has been at the forefront of enterprise content management, delivering robust, scalable solutions that power digital transformation for organizations worldwide.'
    },
    company: {
      title: 'Our Story',
      foundingStory: {
        title: 'Founded in 2014',
        description: 'InstAI CMS was founded with a vision to create enterprise-grade content management solutions that could scale with growing businesses while maintaining the highest standards of security and performance.'
      },
      timeline: [
        {
          period: '2014 - 2016: Foundation Years',
          description: 'Established core CMS platform with focus on enterprise security and scalability.'
        },
        {
          period: '2017 - 2019: Rapid Growth',
          description: 'Expanded to serve 100+ enterprise clients across various industries.'
        },
        {
          period: '2020 - 2022: Innovation Era',
          description: 'Introduced AI-powered features and advanced analytics capabilities.'
        },
        {
          period: '2023 - Present: Market Leader',
          description: 'Serving 500+ enterprise clients with 99.9% uptime and industry-leading support.'
        }
      ]
    },
    mission: {
      title: 'Mission & Values',
      items: [
        {
          title: 'Our Mission',
          image: '/images/mission-target.svg',
          description: 'To empower enterprises with robust, scalable content management solutions that drive digital transformation and business growth.'
        },
        {
          title: 'Excellence',
          image: '/images/mission-excellence.svg',
          description: 'We maintain the highest standards in everything we do, from code quality to customer service, ensuring exceptional results.'
        },
        {
          title: 'Partnership',
          image: '/images/mission-partnership.svg',
          description: 'We build long-term partnerships with our clients, understanding their unique needs and growing alongside their businesses.'
        }
      ]
    },
    statistics: {
      title: 'By the Numbers',
      items: [
        {
          value: '10+',
          label: 'Years of Excellence'
        },
        {
          value: '500+',
          label: 'Enterprise Clients'
        },
        {
          value: '99.9%',
          label: 'Uptime Record'
        },
        {
          value: '24/7',
          label: 'Support Coverage'
        }
      ]
    },
    features: {
      title: 'Why Choose InstAI CMS',
      items: [
        {
          title: 'Enterprise Security',
          image: '/images/feature-security.svg',
          description: 'Bank-level security with advanced encryption, role-based access control, and compliance with industry standards.'
        },
        {
          title: 'Proven Performance',
          image: '/images/feature-performance.svg',
          description: 'Optimized for high-traffic environments with advanced caching, CDN integration, and performance monitoring.'
        },
        {
          title: 'Customizable Platform',
          image: '/images/feature-customizable.svg',
          description: 'Flexible architecture that adapts to your unique business requirements with extensive customization options.'
        },
        {
          title: 'Expert Support',
          image: '/images/feature-support.svg',
          description: 'Dedicated support team with deep CMS expertise, available 24/7 for enterprise clients with guaranteed response times.'
        }
      ]
    },
    cta: {
      title: 'Ready to Experience Enterprise-Grade CMS?',
      description: 'Join the hundreds of enterprises who trust InstAI CMS for their content management needs. Let\'s discuss how we can help transform your digital operations.',
      primaryButton: {
        text: 'Contact Our Team',
        link: '/contact'
      },
      secondaryButton: {
        text: 'Explore Solutions',
        link: '/products'
      }
    }
  },
  blog: {
    header: {
      title: 'Our Blog',
      subtitle: 'Latest News & Insights',
      description: 'Stay updated with the latest trends, tips, and insights from our team of experts.'
    },
    display: {
      itemsPerPage: 10,
      showCategories: true,
      showTags: true,
      showAuthor: false,
      showDate: true,
      showComments: true
    },
    sidebar: {
      showRecentPosts: true,
      showCategories: true,
      showTags: true
    }
  },
  contact: {
    header: {
      title: 'Contact Our Team',
      subtitle: 'Professional Support & Consultation',
      description: 'With over 10 years of experience serving enterprise clients, our team is ready to help you find the perfect content management solution for your organization.'
    },
    info: {
      address: 'InstAI Technology Center\n1234 Enterprise Boulevard\nSuite 500\nSan Francisco, CA 94105',
      workingHours: 'Mon - Fri: 8:00 AM - 7:00 PM PST\nSaturday: 9:00 AM - 5:00 PM PST\nSunday: Emergency Support Only',
      contactGroups: [
        {
          id: 'general',
          name: 'General Support',
          description: 'For general inquiries and support',
          phones: [
            {
              label: 'Main Line',
              number: '+****************',
              description: 'Toll-free support line'
            }
          ],
          emails: [
            {
              label: 'General Support',
              address: '<EMAIL>',
              description: '24-hour response guarantee'
            }
          ]
        },
        {
          id: 'sales',
          name: 'Sales Department',
          description: 'For sales inquiries and product information',
          phones: [
            {
              label: 'Sales Hotline',
              number: '+****************',
              description: 'Direct sales line'
            }
          ],
          emails: [
            {
              label: 'Sales Team',
              address: '<EMAIL>',
              description: 'Product inquiries and quotes'
            }
          ]
        },
        {
          id: 'technical',
          name: 'Technical Support',
          description: 'For technical issues and implementation support',
          phones: [
            {
              label: 'Tech Support',
              number: '+****************',
              description: 'Technical assistance'
            }
          ],
          emails: [
            {
              label: 'Technical Team',
              address: '<EMAIL>',
              description: 'Technical support and troubleshooting'
            }
          ]
        }
      ]
    },
    socialMedia: {
      showSocialMedia: true,
      title: 'Connect With Us',
      platforms: [
        {
          name: 'LinkedIn',
          url: 'https://linkedin.com/company/instai-cms',
          icon: '💼',
          enabled: true
        },
        {
          name: 'Twitter',
          url: 'https://twitter.com/instai_cms',
          icon: '🐦',
          enabled: true
        },
        {
          name: 'GitHub',
          url: 'https://github.com/instai-cms',
          icon: '🐙',
          enabled: true
        },
        {
          name: 'Email',
          url: 'mailto:<EMAIL>',
          icon: '📧',
          enabled: true
        }
      ]
    },
    form: {
      showForm: true,
      title: 'Send us an inquiry',
      subtitle: 'Fill out the form below and we\'ll get back to you as soon as possible.'
    },
    faq: {
      showFaq: true,
      title: 'Frequently Asked Questions',
      subtitle: 'Find answers to common questions about our services',
      questions: [
        {
          question: 'What is your response time for enterprise inquiries?',
          answer: 'We guarantee a response within 24 hours for all enterprise inquiries. Critical issues receive immediate attention with our dedicated support team available 24/7 for enterprise clients.'
        },
        {
          question: 'Do you offer custom development services?',
          answer: 'Yes, we provide comprehensive custom development services tailored to your specific business requirements. Our experienced team can build custom modules, integrations, and solutions.'
        },
        {
          question: 'What should I include in my project inquiry?',
          answer: 'Please provide details about your organization size, current systems, specific requirements, timeline, and budget range. This helps us prepare a comprehensive proposal tailored to your needs.'
        },
        {
          question: 'Do you provide ongoing support and maintenance?',
          answer: 'Absolutely! We offer various support and maintenance packages to ensure your system runs smoothly. This includes regular updates, security patches, performance monitoring, and technical support.'
        }
      ]
    },
    map: {
      showMap: true,
      latitude: '37.7749',
      longitude: '-122.4194'
    }
  }
}

// GET /api/themes/config - Get current theme configuration
export async function GET(request: NextRequest) {
  try {
    // Get theme configuration from settings
    const themeConfig = await prisma.setting.findUnique({
      where: { key: 'theme_config' },
    })

    if (!themeConfig) {
      // Return default configuration if none exists
      return NextResponse.json({
        success: true,
        data: DEFAULT_THEME_CONFIG,
      })
    }

    let config
    try {
      config = JSON.parse(themeConfig.value)
    } catch (error) {
      console.error('Error parsing theme config:', error)
      config = DEFAULT_THEME_CONFIG
    }

    // Merge with defaults to ensure all properties exist
    const mergedConfig = {
      header: {
        ...DEFAULT_THEME_CONFIG.header,
        ...config.header,
        topBar: {
          ...DEFAULT_THEME_CONFIG.header.topBar,
          ...config.header?.topBar
        },
        brand: {
          ...DEFAULT_THEME_CONFIG.header.brand,
          ...config.header?.brand
        },
        navigation: config.header?.navigation || DEFAULT_THEME_CONFIG.header.navigation,
        ctaButton: {
          ...DEFAULT_THEME_CONFIG.header.ctaButton,
          ...config.header?.ctaButton
        }
      },
      footer: {
        ...DEFAULT_THEME_CONFIG.footer,
        ...config.footer,
        brand: {
          ...DEFAULT_THEME_CONFIG.footer.brand,
          ...config.footer?.brand
        },
        socialMedia: config.footer?.socialMedia || DEFAULT_THEME_CONFIG.footer.socialMedia,
        productLinks: {
          title: config.footer?.productLinks?.title || DEFAULT_THEME_CONFIG.footer.productLinks.title,
          links: config.footer?.productLinks?.links || DEFAULT_THEME_CONFIG.footer.productLinks.links
        },
        supportLinks: {
          title: config.footer?.supportLinks?.title || DEFAULT_THEME_CONFIG.footer.supportLinks.title,
          links: config.footer?.supportLinks?.links || DEFAULT_THEME_CONFIG.footer.supportLinks.links
        },
        customGroups: config.footer?.customGroups || DEFAULT_THEME_CONFIG.footer.customGroups,
        bottomBar: {
          ...DEFAULT_THEME_CONFIG.footer.bottomBar,
          ...config.footer?.bottomBar,
          legalLinks: config.footer?.bottomBar?.legalLinks || DEFAULT_THEME_CONFIG.footer.bottomBar.legalLinks
        }
      },
      homepage: {
        ...DEFAULT_THEME_CONFIG.homepage,
        ...config.homepage,
        hero: {
          ...DEFAULT_THEME_CONFIG.homepage.hero,
          ...config.homepage?.hero,
          carousel: {
            ...DEFAULT_THEME_CONFIG.homepage.hero.carousel,
            ...config.homepage?.hero?.carousel,
            slides: config.homepage?.hero?.carousel?.slides || DEFAULT_THEME_CONFIG.homepage.hero.carousel.slides
          }
        },
        productShowcase: {
          ...DEFAULT_THEME_CONFIG.homepage.productShowcase,
          ...config.homepage?.productShowcase
        },
        companyIntro: {
          ...DEFAULT_THEME_CONFIG.homepage.companyIntro,
          ...config.homepage?.companyIntro
        },
        hotSaleProducts: {
          ...DEFAULT_THEME_CONFIG.homepage.hotSaleProducts,
          ...config.homepage?.hotSaleProducts
        }
      },
      products: {
        ...DEFAULT_THEME_CONFIG.products,
        ...config.products,
        detail: {
          ...DEFAULT_THEME_CONFIG.products.detail,
          ...config.products?.detail,
          inquirySection: {
            ...DEFAULT_THEME_CONFIG.products.detail.inquirySection,
            ...config.products?.detail?.inquirySection
          }
        }
      },
      about: {
        ...DEFAULT_THEME_CONFIG.about,
        ...config.about,
        hero: {
          ...DEFAULT_THEME_CONFIG.about.hero,
          ...config.about?.hero
        },
        company: {
          ...DEFAULT_THEME_CONFIG.about.company,
          ...config.about?.company,
          foundingStory: {
            ...DEFAULT_THEME_CONFIG.about.company.foundingStory,
            ...config.about?.company?.foundingStory
          },
          timeline: config.about?.company?.timeline || DEFAULT_THEME_CONFIG.about.company.timeline
        },
        mission: {
          ...DEFAULT_THEME_CONFIG.about.mission,
          ...config.about?.mission,
          items: config.about?.mission?.items || DEFAULT_THEME_CONFIG.about.mission.items
        },
        statistics: {
          ...DEFAULT_THEME_CONFIG.about.statistics,
          ...config.about?.statistics,
          items: config.about?.statistics?.items || DEFAULT_THEME_CONFIG.about.statistics.items
        },
        features: {
          ...DEFAULT_THEME_CONFIG.about.features,
          ...config.about?.features,
          items: config.about?.features?.items || DEFAULT_THEME_CONFIG.about.features.items
        },
        cta: {
          ...DEFAULT_THEME_CONFIG.about.cta,
          ...config.about?.cta,
          primaryButton: {
            ...DEFAULT_THEME_CONFIG.about.cta.primaryButton,
            ...config.about?.cta?.primaryButton
          },
          secondaryButton: {
            ...DEFAULT_THEME_CONFIG.about.cta.secondaryButton,
            ...config.about?.cta?.secondaryButton
          }
        }
      },
      blog: {
        ...DEFAULT_THEME_CONFIG.blog,
        ...config.blog
      },
      contact: {
        ...DEFAULT_THEME_CONFIG.contact,
        ...config.contact
      }
    }

    return NextResponse.json({
      success: true,
      data: mergedConfig,
    })
  } catch (error) {
    console.error('Get theme config error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/themes/config - Update theme configuration
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.ADMIN)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const body = await request.json()
    const { header, footer, homepage, products, about, blog, contact } = body

    // Validate configuration structure
    if (!header && !footer && !homepage && !products && !about && !blog && !contact) {
      return NextResponse.json(
        { success: false, error: 'At least one configuration section is required' },
        { status: 400 }
      )
    }

    // Get current configuration
    const currentConfig = await prisma.setting.findUnique({
      where: { key: 'theme_config' },
    })

    let existingConfig = DEFAULT_THEME_CONFIG
    if (currentConfig) {
      try {
        existingConfig = JSON.parse(currentConfig.value)
      } catch (error) {
        console.error('Error parsing existing config:', error)
      }
    }

    // Merge configurations
    const updatedConfig = {
      header: header ? {
        ...existingConfig.header,
        ...header
      } : existingConfig.header,
      footer: footer ? {
        ...existingConfig.footer,
        ...footer
      } : existingConfig.footer,
      homepage: homepage ? {
        ...existingConfig.homepage,
        ...homepage,
        hero: homepage.hero ? {
          ...existingConfig.homepage?.hero,
          ...homepage.hero,
          carousel: homepage.hero.carousel ? {
            ...existingConfig.homepage?.hero?.carousel,
            ...homepage.hero.carousel,
            slides: homepage.hero.carousel.slides || existingConfig.homepage?.hero?.carousel?.slides || DEFAULT_THEME_CONFIG.homepage.hero.carousel.slides
          } : existingConfig.homepage?.hero?.carousel || DEFAULT_THEME_CONFIG.homepage.hero.carousel
        } : existingConfig.homepage?.hero || DEFAULT_THEME_CONFIG.homepage.hero,
        productShowcase: homepage.productShowcase ? {
          ...existingConfig.homepage?.productShowcase,
          ...homepage.productShowcase
        } : existingConfig.homepage?.productShowcase || DEFAULT_THEME_CONFIG.homepage.productShowcase,
        companyIntro: homepage.companyIntro ? {
          ...existingConfig.homepage?.companyIntro,
          ...homepage.companyIntro
        } : existingConfig.homepage?.companyIntro || DEFAULT_THEME_CONFIG.homepage.companyIntro,
        hotSaleProducts: homepage.hotSaleProducts ? {
          ...existingConfig.homepage?.hotSaleProducts,
          ...homepage.hotSaleProducts
        } : existingConfig.homepage?.hotSaleProducts || DEFAULT_THEME_CONFIG.homepage.hotSaleProducts
      } : existingConfig.homepage || DEFAULT_THEME_CONFIG.homepage,
      products: products ? {
        ...existingConfig.products,
        ...products
      } : existingConfig.products || DEFAULT_THEME_CONFIG.products,
      about: about ? {
        ...existingConfig.about,
        ...about
      } : existingConfig.about || DEFAULT_THEME_CONFIG.about,
      blog: blog ? {
        ...existingConfig.blog,
        ...blog
      } : existingConfig.blog || DEFAULT_THEME_CONFIG.blog,
      contact: contact ? {
        ...existingConfig.contact,
        ...contact
      } : existingConfig.contact || DEFAULT_THEME_CONFIG.contact
    }

    // Save configuration
    await prisma.setting.upsert({
      where: { key: 'theme_config' },
      update: {
        value: JSON.stringify(updatedConfig),
        type: 'json',
      },
      create: {
        key: 'theme_config',
        value: JSON.stringify(updatedConfig),
        type: 'json',
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedConfig,
      message: 'Theme configuration updated successfully',
    })
  } catch (error) {
    console.error('Update theme config error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/themes/config/reset - Reset to default configuration
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.ADMIN)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    // Reset to default configuration
    await prisma.setting.upsert({
      where: { key: 'theme_config' },
      update: {
        value: JSON.stringify(DEFAULT_THEME_CONFIG),
        type: 'json',
      },
      create: {
        key: 'theme_config',
        value: JSON.stringify(DEFAULT_THEME_CONFIG),
        type: 'json',
      },
    })

    return NextResponse.json({
      success: true,
      data: DEFAULT_THEME_CONFIG,
      message: 'Theme configuration reset to defaults',
    })
  } catch (error) {
    console.error('Reset theme config error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
