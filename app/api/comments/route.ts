import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { getPaginationData } from '@/app/lib/utils'
import { Role, CommentStatus } from '@/app/types'

// GET /api/comments - Get all comments (Editor+)
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') as string
    const postId = searchParams.get('postId') as string
    const search = searchParams.get('search') as string

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (status && Object.values(CommentStatus).includes(status as any)) {
      where.status = status
    }

    if (postId) {
      where.postId = postId
    }

    if (search) {
      where.OR = [
        { content: { contains: search, mode: 'insensitive' } },
        { guestName: { contains: search, mode: 'insensitive' } },
        { guestEmail: { contains: search, mode: 'insensitive' } },
        { author: { name: { contains: search, mode: 'insensitive' } } },
      ]
    }

    // Get comments with relations
    const [comments, total] = await Promise.all([
      prisma.comment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          post: {
            select: {
              id: true,
              title: true,
              slug: true,
            },
          },
          parent: {
            select: {
              id: true,
              content: true,
              author: {
                select: {
                  name: true,
                },
              },
              guestName: true,
            },
          },
          _count: {
            select: {
              replies: true,
            },
          },
        },
      }),
      prisma.comment.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: comments,
      pagination,
    })
  } catch (error) {
    console.error('Get comments error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/comments - Create new comment (Public)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { content, postId, parentId, guestName, guestEmail } = body

    // Validation
    if (!content || !postId) {
      return NextResponse.json(
        { success: false, error: 'Content and post ID are required' },
        { status: 400 }
      )
    }

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: { id: true, status: true },
    })

    if (!post || post.status !== 'PUBLISHED') {
      return NextResponse.json(
        { success: false, error: 'Post not found or not published' },
        { status: 404 }
      )
    }

    // Check if parent comment exists
    if (parentId) {
      const parentComment = await prisma.comment.findUnique({
        where: { id: parentId },
      })

      if (!parentComment || parentComment.postId !== postId) {
        return NextResponse.json(
          { success: false, error: 'Parent comment not found' },
          { status: 404 }
        )
      }
    }

    // Try to get authenticated user
    let authorId = null
    try {
      const authResult = await requireAuth(request)
      if ('user' in authResult) {
        authorId = authResult.user.id
      }
    } catch {
      // Not authenticated, use guest info
    }

    // Validate guest info if not authenticated
    if (!authorId && (!guestName || !guestEmail)) {
      return NextResponse.json(
        { success: false, error: 'Guest name and email are required' },
        { status: 400 }
      )
    }

    // Create comment
    const comment = await prisma.comment.create({
      data: {
        content,
        postId,
        parentId: parentId || null,
        authorId: authorId || null,
        guestName: !authorId ? guestName : null,
        guestEmail: !authorId ? guestEmail : null,
        status: CommentStatus.PENDING, // Default to pending for moderation
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: comment,
      message: 'Comment submitted successfully and is pending moderation',
    })
  } catch (error) {
    console.error('Create comment error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
