import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { getPaginationData } from '@/app/lib/utils'
import { ProductStatus } from '@/app/types'

// GET /api/products/public - Get public products (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const categoryId = searchParams.get('categoryId') as string
    const search = searchParams.get('search') as string
    const featured = searchParams.get('featured') === 'true'

    const skip = (page - 1) * limit

    // Build where clause - only show active products for public access
    const where: any = {
      status: ProductStatus.ACTIVE
    }

    if (categoryId) {
      where.categoryId = categoryId
    }

    if (featured) {
      where.featured = true
    }

    if (search) {
      const searchLower = search.toLowerCase()
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { content: { contains: search } },
        { sku: { contains: search } },
      ]
    }

    // Get products with relations
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          category: true,
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          attributes: true,
          _count: {
            select: {
              images: true,
              attributes: true,
              inquiries: true,
            },
          },
        },
      }),
      prisma.product.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: products,
      pagination,
    })
  } catch (error) {
    console.error('Get public products error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
