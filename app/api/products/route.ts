import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug, extractExcerpt, getPaginationData } from '@/app/lib/utils'
import { ProductStatus, Role } from '@/app/types'

// GET /api/products - Get all products with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Require authentication for admin access
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') as string
    const categoryId = searchParams.get('categoryId') as string
    const search = searchParams.get('search') as string
    const featured = searchParams.get('featured') === 'true'
    const active = searchParams.get('active') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (active) {
      where.status = ProductStatus.ACTIVE
    } else if (status) {
      where.status = status
    }

    if (categoryId) {
      where.categoryId = categoryId
    }

    if (featured) {
      where.featured = true
    }

    if (search) {
      // SQLite doesn't support mode: 'insensitive', so we'll use contains without mode
      // For case-insensitive search in SQLite, we could use raw SQL or convert to lowercase
      const searchLower = search.toLowerCase()
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { content: { contains: search } },
        { sku: { contains: search } },
      ]
    }

    // Get products with relations
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          category: true,
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          attributes: true,
          _count: {
            select: {
              images: true,
              attributes: true,
              inquiries: true,
            },
          },
        },
      }),
      prisma.product.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: products,
      pagination,
    })
  } catch (error) {
    console.error('Get products error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/products - Create new product
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const body = await request.json()
    const {
      name,
      description,
      content,
      excerpt,
      price,
      sku,
      status = ProductStatus.ACTIVE,
      featured = false,
      categoryId,
      images = [],
      seoTitle,
      seoDescription,
      seoKeywords,
      ogTitle,
      ogDescription,
      ogImage,
      attributes = [],
    } = body

    // Validation
    if (!name || !description || !content) {
      return NextResponse.json(
        { success: false, error: 'Name, description and content are required' },
        { status: 400 }
      )
    }

    // Generate slug
    let slug = generateSlug(name)
    
    // Ensure slug is unique
    let slugExists = await prisma.product.findUnique({ where: { slug } })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(name)}-${counter}`
      slugExists = await prisma.product.findUnique({ where: { slug } })
      counter++
    }

    // Generate excerpt if not provided
    const productExcerpt = excerpt || extractExcerpt(content)

    // Create product
    const product = await prisma.product.create({
      data: {
        name,
        slug,
        description,
        content,
        excerpt: productExcerpt,
        price: price ? parseFloat(price) : null,
        sku,
        status,
        featured,
        categoryId: categoryId || null,
        seoTitle,
        seoDescription,
        seoKeywords,
        ogTitle,
        ogDescription,
        ogImage,
        images: {
          create: images.map((img: any, index: number) => ({
            url: img.url,
            alt: img.alt || '',
            caption: img.caption || '',
            sortOrder: img.sortOrder !== undefined ? img.sortOrder : index,
          })),
        },
        attributes: {
          create: attributes.map((attr: any) => ({
            name: attr.name,
            value: attr.value,
            type: attr.type || 'text',
          })),
        },
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' },
        },
        attributes: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully',
    })
  } catch (error) {
    console.error('Create product error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
