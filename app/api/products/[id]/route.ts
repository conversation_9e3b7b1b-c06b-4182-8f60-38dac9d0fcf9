import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug, extractExcerpt } from '@/app/lib/utils'
import { ProductStatus, Role } from '@/app/types'

// GET /api/products/[id] - Get single product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const includeInquiries = searchParams.get('inquiries') === 'true'

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' },
        },
        attributes: true,
        media: true,
        inquiries: includeInquiries ? {
          where: { status: 'NEW' },
          orderBy: { createdAt: 'desc' },
          take: 10,
        } : false,
        _count: {
          select: {
            images: true,
            attributes: true,
            inquiries: true,
          },
        },
      },
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: product,
    })
  } catch (error) {
    console.error('Get product error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params
    const body = await request.json()

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    const {
      name,
      description,
      content,
      excerpt,
      price,
      sku,
      status,
      featured,
      categoryId,
      images = [],
      seoTitle,
      seoDescription,
      seoKeywords,
      ogTitle,
      ogDescription,
      ogImage,
      attributes = [],
    } = body

    // Validation
    if (!name || !description || !content) {
      return NextResponse.json(
        { success: false, error: 'Name, description and content are required' },
        { status: 400 }
      )
    }

    // Generate new slug if name changed
    let slug = generateSlug(name)
    let slugExists = await prisma.product.findFirst({
      where: { slug, NOT: { id } },
    })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(name)}-${counter}`
      slugExists = await prisma.product.findFirst({
        where: { slug, NOT: { id } },
      })
      counter++
    }

    // Generate excerpt if not provided
    const productExcerpt = excerpt || extractExcerpt(content)

    // Update product with images and attributes
    const product = await prisma.product.update({
      where: { id },
      data: {
        name,
        slug,
        description,
        content,
        excerpt: productExcerpt,
        price: price ? parseFloat(price) : null,
        sku,
        status,
        featured,
        categoryId: categoryId || null,
        seoTitle,
        seoDescription,
        seoKeywords,
        ogTitle,
        ogDescription,
        ogImage,
        images: {
          deleteMany: {}, // Clear existing images
          create: images.map((img: any, index: number) => ({
            url: img.url,
            alt: img.alt || '',
            caption: img.caption || '',
            sortOrder: img.sortOrder !== undefined ? img.sortOrder : index,
          })),
        },
        attributes: {
          deleteMany: {}, // Clear existing attributes
          create: attributes.map((attr: any) => ({
            name: attr.name,
            value: attr.value,
            type: attr.type || 'text',
          })),
        },
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' },
        },
        attributes: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product updated successfully',
    })
  } catch (error) {
    console.error('Update product error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/products/[id] - Delete product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Delete product (cascade will handle related records)
    await prisma.product.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
    })
  } catch (error) {
    console.error('Delete product error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
