import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug } from '@/app/lib/utils'
import { Role } from '@/app/types'

// Helper function to recursively count products in category and all subcategories
async function getTotalProductCount(categoryId: string): Promise<number> {
  const category = await prisma.productCategory.findUnique({
    where: { id: categoryId },
    include: {
      _count: {
        select: {
          products: {
            where: { status: 'ACTIVE' },
          },
        },
      },
      children: {
        select: { id: true },
      },
    },
  })

  if (!category) return 0

  let totalCount = category._count.products

  // Recursively count products in all subcategories
  for (const child of category.children) {
    totalCount += await getTotalProductCount(child.id)
  }

  return totalCount
}

// GET /api/product-categories - Get all product categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('products') === 'true'
    const tree = searchParams.get('tree') === 'true'
    const parentId = searchParams.get('parentId') as string

    let categories

    if (tree) {
      // Return hierarchical tree structure
      categories = await prisma.productCategory.findMany({
        where: { parentId: null }, // Only root categories
        orderBy: { name: 'asc' },
        include: {
          children: {
            orderBy: { name: 'asc' },
            include: {
              children: {
                orderBy: { name: 'asc' },
                include: {
                  _count: {
                    select: {
                      products: {
                        where: { status: 'ACTIVE' },
                      },
                    },
                  },
                },
              },
              _count: {
                select: {
                  products: {
                    where: { status: 'ACTIVE' },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              products: {
                where: { status: 'ACTIVE' },
                },
            },
          },
        },
      })

      // Add total product counts recursively
      const addTotalCounts = async (cats: any[]): Promise<any[]> => {
        return Promise.all(
          cats.map(async (cat) => ({
            ...cat,
            _count: {
              ...cat._count,
              totalProducts: await getTotalProductCount(cat.id),
            },
            children: cat.children ? await addTotalCounts(cat.children) : [],
          }))
        )
      }

      categories = await addTotalCounts(categories)
    } else {
      // Return flat list with optional parent filter
      const where = parentId ? { parentId } : {}
      
      categories = await prisma.productCategory.findMany({
        where,
        orderBy: { name: 'asc' },
        include: {
          parent: true,
          children: {
            orderBy: { name: 'asc' },
          },
          _count: {
            select: {
              products: {
                where: { status: 'ACTIVE' },
              },
              children: true,
            },
          },
          ...(includeProducts && {
            products: {
              where: { status: 'ACTIVE' },
              take: 5,
              orderBy: { createdAt: 'desc' },
              include: {
                images: {
                  take: 1,
                  orderBy: { sortOrder: 'asc' },
                },
              },
            },
          }),
        },
      })
    }

    return NextResponse.json({
      success: true,
      data: categories,
    })
  } catch (error) {
    console.error('Get product categories error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/product-categories - Create new product category
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const body = await request.json()
    const { 
      name, 
      description, 
      image, 
      parentId,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body

    // Validation
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      )
    }

    // Validate parent category exists if provided
    if (parentId) {
      const parentCategory = await prisma.productCategory.findUnique({
        where: { id: parentId },
      })
      if (!parentCategory) {
        return NextResponse.json(
          { success: false, error: 'Parent category not found' },
          { status: 400 }
        )
      }
    }

    // Generate slug
    let slug = generateSlug(name)
    
    // Ensure slug is unique
    let slugExists = await prisma.productCategory.findUnique({ where: { slug } })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(name)}-${counter}`
      slugExists = await prisma.productCategory.findUnique({ where: { slug } })
      counter++
    }

    // Check if name already exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { name },
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Product category with this name already exists' },
        { status: 400 }
      )
    }

    // Create product category
    const category = await prisma.productCategory.create({
      data: {
        name,
        slug,
        description,
        image,
        parentId: parentId || null,
        seoTitle,
        seoDescription,
        seoKeywords,
      },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: category,
      message: 'Product category created successfully',
    })
  } catch (error) {
    console.error('Create product category error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
