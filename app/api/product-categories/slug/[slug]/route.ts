import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'

// Helper function to recursively count products in category and all subcategories
async function getTotalProductCount(categoryId: string): Promise<number> {
  const category = await prisma.productCategory.findUnique({
    where: { id: categoryId },
    include: {
      _count: {
        select: {
          products: {
            where: { status: 'ACTIVE' },
          },
        },
      },
      children: {
        select: { id: true },
      },
    },
  })

  if (!category) return 0

  let totalCount = category._count.products

  // Recursively count products in all subcategories
  for (const child of category.children) {
    totalCount += await getTotalProductCount(child.id)
  }

  return totalCount
}

// Helper function to get all category IDs including subcategories
async function getAllCategoryIds(categoryId: string): Promise<string[]> {
  const category = await prisma.productCategory.findUnique({
    where: { id: categoryId },
    include: {
      children: {
        select: { id: true },
      },
    },
  })

  if (!category) return []

  let allIds = [categoryId]

  // Recursively get all subcategory IDs
  for (const child of category.children) {
    const childIds = await getAllCategoryIds(child.id)
    allIds.push(...childIds)
  }

  return allIds
}

// GET /api/product-categories/slug/[slug] - Get product category by slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params
    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('products') === 'true'
    const includeSubcategories = searchParams.get('includeSubcategories') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')

    const category = await prisma.productCategory.findUnique({
      where: { slug },
      include: {
        parent: {
          include: {
            parent: true, // Include grandparent for breadcrumb
          },
        },
        children: {
          orderBy: { name: 'asc' },
          include: {
            _count: {
              select: {
                products: {
                  where: { status: 'ACTIVE' },
                },
              },
            },
          },
        },
        _count: {
          select: {
            products: {
              where: { status: 'ACTIVE' },
            },
            children: true,
          },
        },
        ...(includeProducts && !includeSubcategories && {
          products: {
            where: { status: 'ACTIVE' },
            skip: (page - 1) * limit,
            take: limit,
            orderBy: { createdAt: 'desc' },
            include: {
              images: {
                take: 1,
                orderBy: { sortOrder: 'asc' },
              },
              attributes: {
                where: {
                  name: { in: ['brand', 'model', 'price_range'] }, // Key attributes for listing
                },
              },
            },
          },
        }),
      },
    })

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Product category not found' },
        { status: 404 }
      )
    }

    // If including subcategories, fetch products from all subcategories
    let allProducts = category.products || []
    if (includeProducts && includeSubcategories) {
      const allCategoryIds = await getAllCategoryIds(category.id)

      allProducts = await prisma.product.findMany({
        where: {
          status: 'ACTIVE',
          categoryId: { in: allCategoryIds }
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          category: true,
          images: {
            take: 1,
            orderBy: { sortOrder: 'asc' },
          },
          attributes: {
            where: {
              name: { in: ['brand', 'model', 'price_range'] },
            },
          },
        },
      })
    }

    // Build breadcrumb path
    const breadcrumb = []
    let currentCategory = category
    while (currentCategory.parent) {
      breadcrumb.unshift({
        id: currentCategory.parent.id,
        name: currentCategory.parent.name,
        slug: currentCategory.parent.slug,
      })
      currentCategory = currentCategory.parent
    }

    // Calculate total product count including subcategories
    const totalProductCount = await getTotalProductCount(category.id)

    // Calculate total product count for each child category
    const childrenWithTotalCounts = await Promise.all(
      category.children.map(async (child) => ({
        ...child,
        _count: {
          ...child._count,
          totalProducts: await getTotalProductCount(child.id),
        },
      }))
    )

    const response: any = {
      ...category,
      breadcrumb,
      children: childrenWithTotalCounts,
      products: allProducts, // Use the correct product list
      _count: {
        ...category._count,
        products: category._count.products, // Direct products only
        totalProducts: totalProductCount, // Including subcategories
      },
    }

    // Add pagination info if products are included
    if (includeProducts) {
      const totalProductsForPagination = includeSubcategories ? totalProductCount : category._count.products
      const totalPages = Math.ceil(totalProductsForPagination / limit)

      response.pagination = {
        page,
        limit,
        total: totalProductsForPagination,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      }
    }

    return NextResponse.json({
      success: true,
      data: response,
    })
  } catch (error) {
    console.error('Get product category by slug error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
