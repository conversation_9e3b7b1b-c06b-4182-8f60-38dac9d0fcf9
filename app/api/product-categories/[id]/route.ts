import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug } from '@/app/lib/utils'
import { Role } from '@/app/types'

// GET /api/product-categories/[id] - Get single product category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('products') === 'true'

    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        parent: true,
        children: {
          orderBy: { name: 'asc' },
          include: {
            _count: {
              select: {
                products: {
                  where: { status: 'ACTIVE' },
                },
              },
            },
          },
        },
        _count: {
          select: {
            products: {
              where: { status: 'ACTIVE' },
            },
            children: true,
          },
        },
        ...(includeProducts && {
          products: {
            where: { status: 'ACTIVE' },
            orderBy: { createdAt: 'desc' },
            include: {
              images: {
                take: 1,
                orderBy: { sortOrder: 'asc' },
              },
            },
          },
        }),
      },
    })

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Product category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: category,
    })
  } catch (error) {
    console.error('Get product category error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/product-categories/[id] - Update product category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params
    const body = await request.json()

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
    })

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Product category not found' },
        { status: 404 }
      )
    }

    const {
      name,
      description,
      image,
      parentId,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body

    // Validation
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      )
    }

    // Prevent setting self as parent
    if (parentId === id) {
      return NextResponse.json(
        { success: false, error: 'Category cannot be its own parent' },
        { status: 400 }
      )
    }

    // Validate parent category exists if provided
    if (parentId) {
      const parentCategory = await prisma.productCategory.findUnique({
        where: { id: parentId },
      })
      if (!parentCategory) {
        return NextResponse.json(
          { success: false, error: 'Parent category not found' },
          { status: 400 }
        )
      }

      // Prevent circular references (check if parentId is a descendant)
      const isDescendant = await checkIfDescendant(id, parentId)
      if (isDescendant) {
        return NextResponse.json(
          { success: false, error: 'Cannot set a descendant category as parent' },
          { status: 400 }
        )
      }
    }

    // Generate new slug if name changed
    let slug = generateSlug(name)
    let slugExists = await prisma.productCategory.findFirst({
      where: { slug, NOT: { id } },
    })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(name)}-${counter}`
      slugExists = await prisma.productCategory.findFirst({
        where: { slug, NOT: { id } },
      })
      counter++
    }

    // Check if name already exists (excluding current category)
    const existingName = await prisma.productCategory.findFirst({
      where: { name, NOT: { id } },
    })

    if (existingName) {
      return NextResponse.json(
        { success: false, error: 'Product category with this name already exists' },
        { status: 400 }
      )
    }

    // Update category
    const category = await prisma.productCategory.update({
      where: { id },
      data: {
        name,
        slug,
        description,
        image,
        parentId: parentId || null,
        seoTitle,
        seoDescription,
        seoKeywords,
      },
      include: {
        parent: true,
        children: {
          orderBy: { name: 'asc' },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: category,
      message: 'Product category updated successfully',
    })
  } catch (error) {
    console.error('Update product category error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/product-categories/[id] - Delete product category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { id } = await params

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        children: true,
        products: true,
      },
    })

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Product category not found' },
        { status: 404 }
      )
    }

    // Check if category has children
    if (existingCategory.children.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category with subcategories' },
        { status: 400 }
      )
    }

    // Check if category has products
    if (existingCategory.products.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category with products' },
        { status: 400 }
      )
    }

    // Delete category
    await prisma.productCategory.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: 'Product category deleted successfully',
    })
  } catch (error) {
    console.error('Delete product category error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to check if a category is a descendant of another
async function checkIfDescendant(categoryId: string, potentialAncestorId: string): Promise<boolean> {
  const descendants = await prisma.productCategory.findMany({
    where: { parentId: categoryId },
    select: { id: true },
  })

  for (const descendant of descendants) {
    if (descendant.id === potentialAncestorId) {
      return true
    }
    if (await checkIfDescendant(descendant.id, potentialAncestorId)) {
      return true
    }
  }

  return false
}
