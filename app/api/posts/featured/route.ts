import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role } from '@/app/types'

// GET /api/posts/featured - Get current featured post
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const featuredPost = await prisma.post.findFirst({
      where: { featured: true },
      select: {
        id: true,
        title: true,
        slug: true,
        status: true,
        publishedAt: true,
        author: {
          select: {
            name: true,
            username: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: featuredPost,
    })
  } catch (error) {
    console.error('Get featured post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/posts/featured - Set featured post
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const body = await request.json()
    const { postId } = body

    if (!postId) {
      return NextResponse.json(
        { success: false, error: 'Post ID is required' },
        { status: 400 }
      )
    }

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: { id: true, title: true },
    })

    if (!post) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      )
    }

    // Remove featured status from all posts
    await prisma.post.updateMany({
      where: { featured: true },
      data: { featured: false },
    })

    // Set the new featured post
    const updatedPost = await prisma.post.update({
      where: { id: postId },
      data: { featured: true },
      select: {
        id: true,
        title: true,
        slug: true,
        status: true,
        publishedAt: true,
        author: {
          select: {
            name: true,
            username: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedPost,
      message: 'Featured post updated successfully',
    })
  } catch (error) {
    console.error('Set featured post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/posts/featured - Remove featured status
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    // Remove featured status from all posts
    await prisma.post.updateMany({
      where: { featured: true },
      data: { featured: false },
    })

    return NextResponse.json({
      success: true,
      message: 'Featured post status removed successfully',
    })
  } catch (error) {
    console.error('Remove featured post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
