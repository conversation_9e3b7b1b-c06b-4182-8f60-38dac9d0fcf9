import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug, extractExcerpt } from '@/app/lib/utils'
import { PostStatus, Role } from '@/app/types'

// GET /api/posts/[id] - Get single post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const includeComments = searchParams.get('comments') === 'true'

    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
            bio: true,
          },
        },
        category: true,
        tags: true,
        comments: includeComments ? {
          where: { status: 'APPROVED' },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              },
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        } : false,
        _count: {
          select: {
            comments: {
              where: { status: 'APPROVED' },
            },
          },
        },
      },
    })

    if (!post) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: post,
    })
  } catch (error) {
    console.error('Get post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/posts/[id] - Update post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { user } = authResult
    const { id } = await params
    const body = await request.json()

    // Check if post exists and user has permission
    const existingPost = await prisma.post.findUnique({
      where: { id },
      select: { authorId: true },
    })

    if (!existingPost) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      )
    }

    // Only author or admin can edit
    if (existingPost.authorId !== user.id && user.role !== Role.ADMIN) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      )
    }

    const {
      title,
      content,
      excerpt,
      featuredImage,
      featured,
      status,
      categoryId,
      tagIds,
      metaTitle,
      metaDescription,
    } = body

    // Validation
    if (!title || !content) {
      return NextResponse.json(
        { success: false, error: 'Title and content are required' },
        { status: 400 }
      )
    }

    // Generate new slug if title changed
    let slug = generateSlug(title)
    let slugExists = await prisma.post.findFirst({
      where: { slug, NOT: { id } },
    })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(title)}-${counter}`
      slugExists = await prisma.post.findFirst({
        where: { slug, NOT: { id } },
      })
      counter++
    }

    // Generate excerpt if not provided
    const postExcerpt = excerpt || extractExcerpt(content)

    // Update post
    const post = await prisma.post.update({
      where: { id },
      data: {
        title,
        slug,
        content,
        excerpt: postExcerpt,
        featuredImage,
        featured,
        status,
        publishedAt: status === PostStatus.PUBLISHED ? new Date() : null,
        metaTitle,
        metaDescription,
        categoryId: categoryId || null,
        tags: {
          set: [], // Clear existing tags
          connect: tagIds?.map((tagId: string) => ({ id: tagId })) || [],
        },
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          },
        },
        category: true,
        tags: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Post updated successfully',
    })
  } catch (error) {
    console.error('Update post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/posts/[id] - Delete post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { user } = authResult
    const { id } = await params

    // Check if post exists and user has permission
    const existingPost = await prisma.post.findUnique({
      where: { id },
      select: { authorId: true },
    })

    if (!existingPost) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      )
    }

    // Only author or admin can delete
    if (existingPost.authorId !== user.id && user.role !== Role.ADMIN) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Delete post
    await prisma.post.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: 'Post deleted successfully',
    })
  } catch (error) {
    console.error('Delete post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
