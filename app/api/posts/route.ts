import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { generateSlug, extractExcerpt, getPaginationData } from '@/app/lib/utils'
import { PostStatus, Role } from '@/app/types'

// GET /api/posts - Get all posts with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') as string
    const categoryId = searchParams.get('categoryId') as string
    const category = searchParams.get('category') as string // category slug
    const tag = searchParams.get('tag') as string // tag slug
    const authorId = searchParams.get('authorId') as string
    const search = searchParams.get('search') as string
    const published = searchParams.get('published') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (published) {
      where.status = PostStatus.PUBLISHED
      where.publishedAt = { lte: new Date() }
    } else if (status) {
      where.status = status
    }

    if (categoryId) {
      where.categoryId = categoryId
    }

    if (category) {
      where.category = {
        slug: category
      }
    }

    if (tag) {
      where.tags = {
        some: {
          slug: tag
        }
      }
    }

    if (authorId) {
      where.authorId = authorId
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Get posts with relations
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          author: {
            select: {
              id: true,
              username: true,
              name: true,
              avatar: true,
            },
          },
          category: true,
          tags: true,
          _count: {
            select: {
              comments: {
                where: { status: 'APPROVED' },
              },
            },
          },
        },
      }),
      prisma.post.count({ where }),
    ])

    const pagination = getPaginationData(page, limit, total)

    return NextResponse.json({
      success: true,
      data: posts,
      pagination,
    })
  } catch (error) {
    console.error('Get posts error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/posts - Create new post
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    const { user } = authResult
    const body = await request.json()
    const {
      title,
      content,
      excerpt,
      featuredImage,
      featured = false,
      status = PostStatus.DRAFT,
      categoryId,
      tagIds = [],
      metaTitle,
      metaDescription,
    } = body

    // Validation
    if (!title || !content) {
      return NextResponse.json(
        { success: false, error: 'Title and content are required' },
        { status: 400 }
      )
    }

    // Generate slug
    let slug = generateSlug(title)
    
    // Ensure slug is unique
    let slugExists = await prisma.post.findUnique({ where: { slug } })
    let counter = 1
    while (slugExists) {
      slug = `${generateSlug(title)}-${counter}`
      slugExists = await prisma.post.findUnique({ where: { slug } })
      counter++
    }

    // Generate excerpt if not provided
    const postExcerpt = excerpt || extractExcerpt(content)

    // Create post
    const post = await prisma.post.create({
      data: {
        title,
        slug,
        content,
        excerpt: postExcerpt,
        featuredImage,
        featured,
        status,
        publishedAt: status === PostStatus.PUBLISHED ? new Date() : null,
        metaTitle,
        metaDescription,
        authorId: user.id,
        categoryId: categoryId || null,
        tags: {
          connect: tagIds.map((id: string) => ({ id })),
        },
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          },
        },
        category: true,
        tags: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Post created successfully',
    })
  } catch (error) {
    console.error('Create post error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
