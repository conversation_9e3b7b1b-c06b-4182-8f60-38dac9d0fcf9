import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/lib/prisma'
import { requireAuth } from '@/app/lib/auth'
import { Role } from '@/app/types'

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, Role.EDITOR)
    if ('error' in authResult) {
      return NextResponse.json(authResult, { status: authResult.status })
    }

    // Get all statistics in parallel for better performance
    const [
      totalPosts,
      totalUsers,
      totalComments,
      totalCategories,
      totalProducts,
      totalInquiries,
      totalMedia,
      recentPosts,
      recentUsers,
      recentComments,
      recentInquiries,
      publishedPosts,
      draftPosts,
      newInquiries,
      contactedInquiries,
    ] = await Promise.all([
      // Basic counts
      prisma.post.count(),
      prisma.user.count(),
      prisma.comment.count(),
      prisma.category.count(),
      prisma.product.count(),
      prisma.inquiry.count(),
      prisma.media.count(),
      
      // Recent activity (last 7 days)
      prisma.post.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.comment.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.inquiry.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // Status breakdowns
      prisma.post.count({ where: { status: 'PUBLISHED' } }),
      prisma.post.count({ where: { status: 'DRAFT' } }),
      prisma.inquiry.count({ where: { status: 'NEW' } }),
      prisma.inquiry.count({ where: { status: 'CONTACTED' } }),
    ])

    // Get recent activity items
    const [recentPostsData, recentUsersData, recentCommentsData, recentInquiriesData] = await Promise.all([
      prisma.post.findMany({
        take: 3,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          createdAt: true,
          status: true,
          author: {
            select: { name: true }
          }
        }
      }),
      prisma.user.findMany({
        take: 3,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true,
          role: true
        }
      }),
      prisma.comment.findMany({
        take: 3,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          content: true,
          createdAt: true,
          author: {
            select: { name: true }
          },
          post: {
            select: { title: true }
          }
        }
      }),
      prisma.inquiry.findMany({
        take: 3,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          company: true,
          createdAt: true,
          status: true
        }
      })
    ])

    const stats = {
      // Basic statistics
      totalPosts,
      totalUsers,
      totalComments,
      totalCategories,
      totalProducts,
      totalInquiries,
      totalMedia,
      
      // Recent activity counts
      recentPosts,
      recentUsers,
      recentComments,
      recentInquiries,
      
      // Status breakdowns
      publishedPosts,
      draftPosts,
      newInquiries,
      contactedInquiries,
      
      // Recent activity data
      recentActivity: {
        posts: recentPostsData,
        users: recentUsersData,
        comments: recentCommentsData,
        inquiries: recentInquiriesData,
      },
      
      // Growth calculations (simplified - comparing with previous period)
      growth: {
        posts: recentPosts > 0 ? `+${recentPosts}` : '0',
        users: recentUsers > 0 ? `+${recentUsers}` : '0',
        comments: recentComments > 0 ? `+${recentComments}` : '0',
        inquiries: recentInquiries > 0 ? `+${recentInquiries}` : '0',
      }
    }

    return NextResponse.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
