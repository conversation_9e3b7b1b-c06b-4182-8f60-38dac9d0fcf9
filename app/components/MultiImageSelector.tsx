'use client'

import { useState } from 'react'
import Image from 'next/image'
import MediaSelector from './MediaSelector'
import { MediaWithRelations, ProductImageForm } from '@/app/types'

interface MultiImageSelectorProps {
  images: ProductImageForm[]
  onChange: (images: ProductImageForm[]) => void
  maxImages?: number
  title?: string
  description?: string
}

export function MultiImageSelector({
  images,
  onChange,
  maxImages = 10,
  title = "Product Images",
  description = "Add images for your product. You can reorder them by dragging."
}: MultiImageSelectorProps) {
  const [showMediaSelector, setShowMediaSelector] = useState(false)

  const handleAddImage = (media: MediaWithRelations) => {
    const newImage: ProductImageForm = {
      url: media.url,
      alt: media.alt || '',
      caption: media.caption || '',
      sortOrder: images.length
    }
    onChange([...images, newImage])
  }

  const handleRemoveImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    // Update sort orders
    const reorderedImages = newImages.map((img, i) => ({
      ...img,
      sortOrder: i
    }))
    onChange(reorderedImages)
  }

  const handleUpdateImage = (index: number, updates: Partial<ProductImageForm>) => {
    const newImages = images.map((img, i) => 
      i === index ? { ...img, ...updates } : img
    )
    onChange(newImages)
  }

  const handleMoveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images]
    const [movedImage] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, movedImage)
    
    // Update sort orders
    const reorderedImages = newImages.map((img, i) => ({
      ...img,
      sortOrder: i
    }))
    onChange(reorderedImages)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>

      {/* Add Image Button */}
      <div className="flex items-center justify-between">
        <button
          type="button"
          onClick={() => setShowMediaSelector(true)}
          disabled={images.length >= maxImages}
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Image
        </button>
        <span className="text-sm text-gray-500">
          {images.length} / {maxImages} images
        </span>
      </div>

      {/* Images Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative group border border-gray-200 rounded-lg p-3 bg-white">
              {/* Image Preview */}
              <div className="aspect-square relative mb-3 bg-gray-100 rounded overflow-hidden">
                <Image
                  src={image.url}
                  alt={image.alt || `Product image ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  unoptimized={image.url.startsWith('http')}
                />
                
                {/* Sort Order Badge */}
                <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                  #{index + 1}
                </div>

                {/* Remove Button */}
                <button
                  type="button"
                  onClick={() => handleRemoveImage(index)}
                  className="absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                  title="Remove image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Image Details */}
              <div className="space-y-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700">Alt Text</label>
                  <input
                    type="text"
                    value={image.alt || ''}
                    onChange={(e) => handleUpdateImage(index, { alt: e.target.value })}
                    className="mt-1 block w-full text-xs border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe this image"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700">Caption</label>
                  <input
                    type="text"
                    value={image.caption || ''}
                    onChange={(e) => handleUpdateImage(index, { caption: e.target.value })}
                    className="mt-1 block w-full text-xs border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Optional caption"
                  />
                </div>
              </div>

              {/* Move Buttons */}
              <div className="flex justify-between mt-3">
                <button
                  type="button"
                  onClick={() => handleMoveImage(index, Math.max(0, index - 1))}
                  disabled={index === 0}
                  className="text-xs text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ← Move Left
                </button>
                <button
                  type="button"
                  onClick={() => handleMoveImage(index, Math.min(images.length - 1, index + 1))}
                  disabled={index === images.length - 1}
                  className="text-xs text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Move Right →
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No images</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by adding your first product image.</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setShowMediaSelector(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Image
            </button>
          </div>
        </div>
      )}

      {/* Media Selector Modal */}
      <MediaSelector
        isOpen={showMediaSelector}
        onClose={() => setShowMediaSelector(false)}
        onSelect={handleAddImage}
        title="Select Product Image"
        allowedTypes={['image/*']}
      />
    </div>
  )
}
