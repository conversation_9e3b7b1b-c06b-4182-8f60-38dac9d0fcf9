'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react'

interface SEOAnalysisResult {
  score: number
  issues: SEOIssue[]
  recommendations: string[]
}

interface SEOIssue {
  type: 'error' | 'warning' | 'info'
  category: string
  message: string
  element?: string
}

interface SEOAnalyzerProps {
  url?: string
  title?: string
  description?: string
  keywords?: string
  content?: string
  className?: string
}

export default function SEOAnalyzer({
  url,
  title,
  description,
  keywords,
  content,
  className = ''
}: SEOAnalyzerProps) {
  const [analysis, setAnalysis] = useState<SEOAnalysisResult | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (title || description || content) {
      analyzeContent()
    }
  }, [title, description, keywords, content])

  const analyzeContent = () => {
    setLoading(true)
    
    // Simulate analysis delay
    setTimeout(() => {
      const issues: SEOIssue[] = []
      let score = 100

      // Title analysis
      if (!title) {
        issues.push({
          type: 'error',
          category: 'Title',
          message: 'Missing page title',
        })
        score -= 20
      } else {
        if (title.length < 30) {
          issues.push({
            type: 'warning',
            category: 'Title',
            message: 'Title is too short (recommended: 30-60 characters)',
          })
          score -= 10
        } else if (title.length > 60) {
          issues.push({
            type: 'warning',
            category: 'Title',
            message: 'Title is too long (recommended: 30-60 characters)',
          })
          score -= 10
        }
      }

      // Description analysis
      if (!description) {
        issues.push({
          type: 'error',
          category: 'Meta Description',
          message: 'Missing meta description',
        })
        score -= 15
      } else {
        if (description.length < 120) {
          issues.push({
            type: 'warning',
            category: 'Meta Description',
            message: 'Meta description is too short (recommended: 120-160 characters)',
          })
          score -= 8
        } else if (description.length > 160) {
          issues.push({
            type: 'warning',
            category: 'Meta Description',
            message: 'Meta description is too long (recommended: 120-160 characters)',
          })
          score -= 8
        }
      }

      // Keywords analysis
      if (!keywords) {
        issues.push({
          type: 'info',
          category: 'Keywords',
          message: 'No meta keywords specified (optional but recommended)',
        })
        score -= 5
      }

      // Content analysis
      if (content) {
        const wordCount = content.split(/\s+/).length
        if (wordCount < 300) {
          issues.push({
            type: 'warning',
            category: 'Content',
            message: 'Content is too short (recommended: 300+ words)',
          })
          score -= 10
        }

        // Check for headings
        const hasH1 = /<h1[^>]*>/.test(content)
        if (!hasH1) {
          issues.push({
            type: 'warning',
            category: 'Content Structure',
            message: 'Missing H1 heading',
          })
          score -= 8
        }

        // Check for images without alt text
        const imgWithoutAlt = /<img(?![^>]*alt=)[^>]*>/gi.test(content)
        if (imgWithoutAlt) {
          issues.push({
            type: 'warning',
            category: 'Accessibility',
            message: 'Images without alt text found',
          })
          score -= 5
        }
      }

      // URL analysis
      if (url) {
        if (url.length > 100) {
          issues.push({
            type: 'warning',
            category: 'URL',
            message: 'URL is too long (recommended: under 100 characters)',
          })
          score -= 5
        }

        if (!/^[a-z0-9\-\/]+$/.test(url.replace(/^https?:\/\/[^\/]+/, ''))) {
          issues.push({
            type: 'info',
            category: 'URL',
            message: 'URL contains special characters (consider using only lowercase letters, numbers, and hyphens)',
          })
          score -= 3
        }
      }

      // Generate recommendations
      const recommendations: string[] = []
      
      if (score < 80) {
        recommendations.push('Focus on fixing critical SEO issues first')
      }
      if (!title || title.length < 30) {
        recommendations.push('Create a compelling, descriptive title between 30-60 characters')
      }
      if (!description || description.length < 120) {
        recommendations.push('Write a detailed meta description between 120-160 characters')
      }
      if (content && content.split(/\s+/).length < 300) {
        recommendations.push('Expand content to at least 300 words for better SEO')
      }

      setAnalysis({
        score: Math.max(0, score),
        issues,
        recommendations,
      })
      setLoading(false)
    }, 500)
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-100'
    if (score >= 70) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  const getIssueIcon = (type: SEOIssue['type']) => {
    switch (type) {
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />
    }
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">SEO Analysis</h3>
        <p className="text-gray-500">Provide content to analyze SEO performance.</p>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">SEO Analysis</h3>
        <div className={`px-3 py-1 rounded-full ${getScoreBackground(analysis.score)}`}>
          <span className={`text-sm font-medium ${getScoreColor(analysis.score)}`}>
            Score: {analysis.score}/100
          </span>
        </div>
      </div>

      {/* Issues */}
      {analysis.issues.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-3">Issues Found</h4>
          <div className="space-y-2">
            {analysis.issues.map((issue, index) => (
              <div key={index} className="flex items-start space-x-2 p-2 rounded-md bg-gray-50">
                {getIssueIcon(issue.type)}
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-900">{issue.category}:</span>
                  <span className="text-sm text-gray-600 ml-1">{issue.message}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {analysis.recommendations.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Recommendations</h4>
          <ul className="space-y-1">
            {analysis.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-600">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {analysis.issues.length === 0 && (
        <div className="text-center py-4">
          <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <p className="text-green-600 font-medium">Great! No major SEO issues found.</p>
        </div>
      )}
    </div>
  )
}
