'use client'

import { useFeaturedPost } from '@/app/hooks/useFeaturedPost'
import Link from 'next/link'

interface FeaturedPostManagerProps {
  currentPostId?: string
  featured: boolean
  onFeaturedChange: (featured: boolean) => void
  disabled?: boolean
}

export default function FeaturedPostManager({
  currentPostId,
  featured,
  onFeaturedChange,
  disabled = false
}: FeaturedPostManagerProps) {
  const { featuredPost, loading } = useFeaturedPost()

  const isCurrentPostFeatured = featuredPost?.id === currentPostId
  const hasOtherFeaturedPost = featuredPost && featuredPost.id !== currentPostId

  return (
    <div className="space-y-3">
      <label className="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          checked={featured || false}
          onChange={(e) => onFeaturedChange(e.target.checked)}
          disabled={disabled || loading}
          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
        />
        <span className="text-sm font-medium text-gray-700">
          Featured Blog Post
        </span>
      </label>

      {/* Current Featured Post Info */}
      {!loading && (
        <div className="space-y-2">
          {/* Show current featured post if exists and it's not this post */}
          {hasOtherFeaturedPost && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="text-blue-600 mt-0.5">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-xs font-medium text-blue-800">
                    Current Featured Post
                  </p>
                  <p className="text-xs text-blue-700 mt-1">
                    <Link 
                      href={`/admin/posts/${featuredPost.id}/edit`}
                      className="font-medium hover:underline"
                    >
                      "{featuredPost.title}"
                    </Link>
                    {' '}by {featuredPost.author.name}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Show if this post is currently featured */}
          {isCurrentPostFeatured && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="text-green-600 mt-0.5">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="text-xs font-medium text-green-800">
                    This Post is Currently Featured
                  </p>
                  <p className="text-xs text-green-700 mt-1">
                    This post is currently displayed prominently on the blog homepage.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Warning about uniqueness */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="text-amber-600 mt-0.5">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-xs font-medium text-amber-800">
                  Featured Post Uniqueness
                </p>
                <p className="text-xs text-amber-700 mt-1">
                  Only one post can be featured at a time. Setting this post as featured will automatically remove the featured status from any other post.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <p className="text-xs text-gray-600">Loading featured post information...</p>
          </div>
        </div>
      )}
    </div>
  )
}
