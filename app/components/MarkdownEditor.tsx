'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import '@uiw/react-md-editor/markdown-editor.css'
import '@uiw/react-markdown-preview/markdown.css'

// 动态导入MDEditor以避免SSR问题
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default),
  { ssr: false }
)

interface MarkdownEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  height?: number
  preview?: 'live' | 'edit' | 'preview'
  hideToolbar?: boolean
  className?: string
  disabled?: boolean
}

export default function MarkdownEditor({
  value,
  onChange,
  placeholder = 'Write your content here...',
  height = 400,
  preview = 'live',
  hideToolbar = false,
  className = '',
  disabled = false,
}: MarkdownEditorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // 在服务端渲染时显示一个简单的textarea作为fallback
    return (
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`w-full border border-gray-300 rounded-md p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
        style={{ height: `${height}px` }}
        disabled={disabled}
      />
    )
  }

  return (
    <div className={`markdown-editor-wrapper ${className}`}>
      <MDEditor
        value={value}
        onChange={(val) => onChange(val || '')}
        preview={preview}
        hideToolbar={hideToolbar}
        height={height}
        data-color-mode="light"
        textareaProps={{
          placeholder,
          disabled,
          style: {
            fontSize: 14,
            lineHeight: 1.5,
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
          },
        }}
        previewOptions={{
          rehypePlugins: [],
          remarkPlugins: [],
        }}
      />
      
      <style jsx global>{`
        .markdown-editor-wrapper .w-md-editor {
          background-color: #ffffff;
        }
        
        .markdown-editor-wrapper .w-md-editor-text-pre,
        .markdown-editor-wrapper .w-md-editor-text-input,
        .markdown-editor-wrapper .w-md-editor-text {
          font-size: 14px !important;
          line-height: 1.5 !important;
          font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar {
          border-bottom: 1px solid #e5e7eb;
          background-color: #f9fafb;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar-divider {
          background-color: #e5e7eb;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar ul > li button {
          color: #374151;
          border-radius: 4px;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar ul > li button:hover {
          background-color: #e5e7eb;
          color: #111827;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar ul > li button.active {
          background-color: #3b82f6;
          color: white;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview {
          background-color: #ffffff;
          padding: 16px;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview h1 {
          font-size: 2rem;
          font-weight: 800;
          color: #111827;
          margin-top: 0;
          margin-bottom: 1rem;
          line-height: 1.25;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview h2 {
          font-size: 1.5rem;
          font-weight: 700;
          color: #111827;
          margin-top: 2rem;
          margin-bottom: 1rem;
          line-height: 1.375;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #111827;
          margin-top: 1.5rem;
          margin-bottom: 0.75rem;
          line-height: 1.5;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview p {
          color: #374151;
          line-height: 1.75;
          margin-bottom: 1rem;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview code {
          background-color: #f3f4f6;
          color: #db2777;
          padding: 0.125rem 0.25rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          font-weight: 600;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview pre {
          background-color: #1f2937;
          color: #e5e7eb;
          padding: 1rem;
          border-radius: 0.5rem;
          overflow-x: auto;
          margin: 1rem 0;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview pre code {
          background-color: transparent;
          color: inherit;
          padding: 0;
          font-weight: normal;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview blockquote {
          border-left: 4px solid #3b82f6;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #6b7280;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview ul,
        .markdown-editor-wrapper .w-md-editor-preview ol {
          padding-left: 1.5rem;
          margin-bottom: 1rem;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview li {
          margin-bottom: 0.25rem;
          color: #374151;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview a {
          color: #3b82f6;
          text-decoration: underline;
          font-weight: 500;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview a:hover {
          color: #1d4ed8;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview table {
          width: 100%;
          border-collapse: collapse;
          margin: 1rem 0;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview th,
        .markdown-editor-wrapper .w-md-editor-preview td {
          border: 1px solid #e5e7eb;
          padding: 0.5rem;
          text-align: left;
        }
        
        .markdown-editor-wrapper .w-md-editor-preview th {
          background-color: #f9fafb;
          font-weight: 600;
        }
      `}</style>
    </div>
  )
}
