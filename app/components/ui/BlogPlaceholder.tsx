interface BlogPlaceholderProps {
  variant?: 'default' | 'featured' | 'category' | 'tag'
  className?: string
  category?: string
  tag?: string
}

const placeholderIcons = ['📝', '✍️', '📄', '📰', '📖', '💭', '🔍', '💡']
const placeholderColors = [
  'from-blue-100 to-blue-200',
  'from-green-100 to-green-200', 
  'from-purple-100 to-purple-200',
  'from-orange-100 to-orange-200',
  'from-pink-100 to-pink-200',
  'from-indigo-100 to-indigo-200'
]

export default function BlogPlaceholder({ 
  variant = 'default', 
  className = '',
  category,
  tag 
}: BlogPlaceholderProps) {
  // 根据内容生成一致的随机索引
  const getConsistentIndex = (seed: string) => {
    let hash = 0
    for (let i = 0; i < seed.length; i++) {
      const char = seed.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    return Math.abs(hash)
  }

  const seed = category || tag || 'default'
  const iconIndex = getConsistentIndex(seed) % placeholderIcons.length
  const colorIndex = getConsistentIndex(seed + 'color') % placeholderColors.length

  const icon = placeholderIcons[iconIndex]
  const colorClass = placeholderColors[colorIndex]

  const getContent = () => {
    switch (variant) {
      case 'featured':
        return {
          icon: '📝',
          iconSize: 'text-5xl',
          text: 'Featured Article',
          textSize: 'text-sm',
          textColor: 'text-blue-700',
          bgColor: 'from-blue-100 to-blue-200'
        }
      case 'category':
        return {
          icon,
          iconSize: 'text-4xl',
          text: category ? `${category} Article` : 'Category Article',
          textSize: 'text-xs',
          textColor: 'text-gray-500',
          bgColor: colorClass
        }
      case 'tag':
        return {
          icon,
          iconSize: 'text-4xl', 
          text: tag ? `#${tag}` : 'Tagged Article',
          textSize: 'text-xs',
          textColor: 'text-gray-500',
          bgColor: colorClass
        }
      default:
        return {
          icon,
          iconSize: 'text-4xl',
          text: 'Blog Post',
          textSize: 'text-xs',
          textColor: 'text-gray-500',
          bgColor: colorClass
        }
    }
  }

  const content = getContent()

  return (
    <div className={`w-full h-full bg-gradient-to-br ${content.bgColor} flex items-center justify-center ${className}`}>
      <div className="text-center">
        <div className={`${content.iconSize} text-gray-400 mb-2`}>
          {content.icon}
        </div>
        <div className={`${content.textSize} ${content.textColor} font-medium`}>
          {content.text}
        </div>
      </div>
    </div>
  )
}
