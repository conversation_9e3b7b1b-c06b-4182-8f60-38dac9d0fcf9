import { ReactNode } from 'react'

interface ModernCardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  gradient?: boolean
  glass?: boolean
}

export function ModernCard({ 
  children, 
  className = '', 
  hover = true, 
  gradient = false,
  glass = true 
}: ModernCardProps) {
  const baseClasses = `
    rounded-2xl shadow-lg border border-slate-200/60 p-6
    ${glass ? 'bg-white/80 backdrop-blur-sm' : 'bg-white'}
    ${hover ? 'hover:shadow-xl transition-all duration-300' : ''}
    ${gradient ? 'bg-gradient-to-br from-white to-slate-50/50' : ''}
    ${className}
  `

  return (
    <div className={baseClasses.trim()}>
      {children}
    </div>
  )
}

interface StatCardProps {
  title: string
  value: string | number
  icon: ReactNode
  trend?: {
    value: string
    direction: 'up' | 'down' | 'neutral'
    description: string
  }
  gradient: string
}

export function StatCard({ title, value, icon, trend, gradient }: StatCardProps) {
  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-slate-600'
  }

  const trendIcon = {
    up: (
      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
      </svg>
    ),
    down: (
      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    ),
    neutral: (
      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
      </svg>
    )
  }

  return (
    <ModernCard className="group">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-slate-600 text-sm font-medium">{title}</p>
          <p className="text-3xl font-bold text-slate-900 mt-1">{value}</p>
          {trend && (
            <p className={`text-sm mt-2 flex items-center ${trendColors[trend.direction]}`}>
              {trendIcon[trend.direction]}
              {trend.value} {trend.description}
            </p>
          )}
        </div>
        <div className={`w-12 h-12 bg-gradient-to-r ${gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
          {icon}
        </div>
      </div>
    </ModernCard>
  )
}

interface ActionCardProps {
  title: string
  description?: string
  icon: ReactNode
  href: string
  gradient: string
  onClick?: () => void
}

export function ActionCard({ title, description, icon, href, gradient, onClick }: ActionCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault()
      onClick()
    }
  }

  return (
    <a
      href={href}
      onClick={handleClick}
      className={`group flex items-center p-4 bg-gradient-to-r ${gradient} text-white rounded-xl hover:shadow-lg transition-all duration-200 transform hover:scale-105 block`}
    >
      <div className="mr-3">
        {icon}
      </div>
      <div className="flex-1">
        <h4 className="font-medium">{title}</h4>
        {description && (
          <p className="text-sm opacity-90 mt-1">{description}</p>
        )}
      </div>
      <svg className="w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </a>
  )
}

interface ActivityItemProps {
  title: string
  description: string
  time: string
  type: 'success' | 'info' | 'warning' | 'error'
  icon?: ReactNode
}

export function ActivityItem({ title, description, time, type, icon }: ActivityItemProps) {
  const typeStyles = {
    success: {
      bg: 'bg-green-50/80',
      border: 'border-green-200/60',
      iconBg: 'from-green-500 to-green-600',
      defaultIcon: (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )
    },
    info: {
      bg: 'bg-blue-50/80',
      border: 'border-blue-200/60',
      iconBg: 'from-blue-500 to-blue-600',
      defaultIcon: (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    warning: {
      bg: 'bg-yellow-50/80',
      border: 'border-yellow-200/60',
      iconBg: 'from-yellow-500 to-yellow-600',
      defaultIcon: (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      )
    },
    error: {
      bg: 'bg-red-50/80',
      border: 'border-red-200/60',
      iconBg: 'from-red-500 to-red-600',
      defaultIcon: (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      )
    }
  }

  const style = typeStyles[type]

  return (
    <div className={`flex items-center space-x-4 p-3 ${style.bg} rounded-xl border ${style.border}`}>
      <div className={`w-10 h-10 bg-gradient-to-r ${style.iconBg} rounded-xl flex items-center justify-center`}>
        {icon || style.defaultIcon}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-slate-900">{title}</p>
        <p className="text-xs text-slate-500">{description}</p>
      </div>
      <div className="text-xs text-slate-400">{time}</div>
    </div>
  )
}

interface StatusIndicatorProps {
  label: string
  status: 'operational' | 'degraded' | 'down'
  uptime?: string
}

export function StatusIndicator({ label, status, uptime }: StatusIndicatorProps) {
  const statusStyles = {
    operational: {
      dot: 'bg-green-500',
      text: 'text-green-600',
      bg: 'bg-green-50/80',
      border: 'border-green-200/60',
      label: 'Operational'
    },
    degraded: {
      dot: 'bg-yellow-500',
      text: 'text-yellow-600',
      bg: 'bg-yellow-50/80',
      border: 'border-yellow-200/60',
      label: 'Degraded'
    },
    down: {
      dot: 'bg-red-500',
      text: 'text-red-600',
      bg: 'bg-red-50/80',
      border: 'border-red-200/60',
      label: 'Down'
    }
  }

  const style = statusStyles[status]

  return (
    <div className={`flex items-center justify-between p-3 ${style.bg} rounded-xl border ${style.border}`}>
      <div className="flex items-center space-x-3">
        <div className={`w-3 h-3 ${style.dot} rounded-full animate-pulse`}></div>
        <span className="text-sm font-medium text-slate-900">{label}</span>
      </div>
      <span className={`text-xs font-medium ${style.text}`}>
        {uptime || style.label}
      </span>
    </div>
  )
}
