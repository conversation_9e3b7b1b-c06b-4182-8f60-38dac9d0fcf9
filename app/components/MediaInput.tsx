'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import MediaSelector from './MediaSelector'
import { MediaWithRelations } from '@/app/types'

interface MediaInputProps {
  label: string
  value: string
  onChange: (url: string) => void
  placeholder?: string
  description?: string
  allowedTypes?: string[]
  required?: boolean
  disabled?: boolean
}

export default function MediaInput({
  label,
  value,
  onChange,
  placeholder = "Select or enter media URL",
  description,
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  required = false,
  disabled = false
}: MediaInputProps) {
  const [showSelector, setShowSelector] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const handleMediaSelect = (media: MediaWithRelations) => {
    onChange(media.url)
  }

  const handleClear = () => {
    onChange('')
  }

  const isImage = (url: string) => {
    if (!url) return false
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const urlLower = url.toLowerCase()
    return imageExtensions.some(ext => urlLower.includes(ext)) || url.startsWith('data:image/')
  }

  return (
    <div className="space-y-3">
      {/* Label */}
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {/* Preview */}
      {value && isImage(value) && (
        <div className="relative w-full max-w-md">
          <div
            className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden border shadow-sm cursor-pointer group"
            onClick={() => setShowPreview(true)}
          >
            <Image
              src={value}
              alt="Preview"
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              sizes="(max-width: 768px) 100vw, 448px"
              onError={() => {
                // If image fails to load, we could show a placeholder or remove the preview
                console.warn('Failed to load image:', value)
              }}
              unoptimized={value.startsWith('http')}
            />
            {/* Overlay with actions */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowPreview(true)
                  }}
                  className="px-3 py-1 bg-white bg-opacity-90 text-gray-800 rounded text-sm font-medium hover:bg-opacity-100 transition-all"
                  title="Preview full size"
                >
                  👁️ Preview
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(value, '_blank')
                  }}
                  className="px-3 py-1 bg-blue-500 bg-opacity-90 text-white rounded text-sm font-medium hover:bg-opacity-100 transition-all"
                  title="Open in new tab"
                >
                  Open
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleClear()
                  }}
                  className="px-3 py-1 bg-red-500 bg-opacity-90 text-white rounded text-sm font-medium hover:bg-opacity-100 transition-all"
                  title="Remove image"
                >
                  Remove
                </button>
              </div>
            </div>
          </div>

          {/* Image info */}
          <div className="mt-2 text-xs text-gray-500">
            <p className="truncate font-medium">{value.split('/').pop() || 'Selected image'}</p>
            <p className="text-gray-400">Click image to preview • Hover for actions</p>
          </div>
        </div>
      )}

      {/* Input and Buttons */}
      <div className="flex space-x-2">
        <div className="flex-1">
          <Input
            type="url"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full"
          />
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={() => setShowSelector(true)}
          disabled={disabled}
          className="whitespace-nowrap"
        >
          Browse Media
        </Button>
      </div>

      {/* Description */}
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}

      {/* File info for non-images */}
      {value && !isImage(value) && (
        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border">
          <div className="flex-shrink-0">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {value.split('/').pop() || 'Selected file'}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {value}
            </p>
          </div>
          <button
            type="button"
            onClick={handleClear}
            className="flex-shrink-0 text-red-500 hover:text-red-700 text-sm"
          >
            Remove
          </button>
        </div>
      )}

      {/* Media Selector Modal */}
      <MediaSelector
        isOpen={showSelector}
        onClose={() => setShowSelector(false)}
        onSelect={handleMediaSelect}
        selectedUrl={value}
        title={`Select ${label}`}
        allowedTypes={allowedTypes}
      />

      {/* Image Preview Modal */}
      {showPreview && value && isImage(value) && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[70] p-4">
          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            {/* Close button */}
            <button
              onClick={() => setShowPreview(false)}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-xl hover:bg-opacity-70 transition-all"
              title="Close preview"
            >
              ×
            </button>

            {/* Image */}
            <div className="relative w-full h-full flex items-center justify-center">
              <Image
                src={value}
                alt="Preview"
                fill
                className="object-contain"
                sizes="(max-width: 768px) 100vw, 1024px"
                unoptimized={value.startsWith('http')}
              />
            </div>

            {/* Image info overlay */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-4 rounded-lg">
              <h3 className="font-medium text-lg mb-2">{value.split('/').pop() || 'Image Preview'}</h3>
              <div className="text-sm text-gray-300 mb-3">
                {value}
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSelector(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  Change Image
                </button>
                <button
                  onClick={() => window.open(value, '_blank')}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors text-sm font-medium"
                >
                  Open in New Tab
                </button>
                <button
                  onClick={() => {
                    handleClear()
                    setShowPreview(false)
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm font-medium"
                >
                  Remove Image
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
