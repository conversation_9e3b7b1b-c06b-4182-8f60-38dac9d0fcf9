'use client'

import { useState, useRef } from 'react'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'

interface LogoConfig {
  type: 'text' | 'image' | 'icon'
  value: string
  alt?: string
}

interface LogoSelectorProps {
  value: LogoConfig
  onChange: (value: LogoConfig) => void
  label?: string
}

const iconCategories = {
  business: ['🏢', '🏠', '🏭', '🏪', '🏬', '🏦', '🏛️', '🏗️'],
  office: ['💼', '📊', '📈', '📉', '💻', '🖥️', '📱', '⌨️'],
  tools: ['🔧', '⚙️', '🛠️', '🔩', '⚡', '🔋', '💡', '🔌'],
  creative: ['🌟', '⭐', '✨', '💫', '🌙', '☀️', '🌈', '🔥'],
  transport: ['🚀', '✈️', '🛸', '🎯', '🎪', '🎨', '🎭', '🎪'],
  letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
}

const commonIcons = Object.values(iconCategories).flat()

export default function LogoSelector({ value, onChange, label = 'Logo' }: LogoSelectorProps) {
  const [showIconPicker, setShowIconPicker] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleTypeChange = (type: 'text' | 'image' | 'icon') => {
    let newValue = value.value
    if (type === 'text' && value.type !== 'text') {
      newValue = 'A'
    } else if (type === 'icon' && value.type !== 'icon') {
      newValue = '🏢'
    } else if (type === 'image' && value.type !== 'image') {
      newValue = ''
    }
    
    onChange({
      type,
      value: newValue,
      alt: value.alt
    })
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // 在实际应用中，这里应该上传文件到服务器并获取URL
      // 现在我们使用本地URL作为演示
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        onChange({
          type: 'image',
          value: result,
          alt: value.alt || file.name
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleIconSelect = (icon: string) => {
    onChange({
      type: value.type,
      value: icon,
      alt: value.alt
    })
    setShowIconPicker(false)
  }

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      
      {/* Type Selector */}
      <div className="flex space-x-2">
        <Button
          type="button"
          size="sm"
          variant={value.type === 'text' ? 'default' : 'outline'}
          onClick={() => handleTypeChange('text')}
        >
          📝 Text
        </Button>
        <Button
          type="button"
          size="sm"
          variant={value.type === 'icon' ? 'default' : 'outline'}
          onClick={() => handleTypeChange('icon')}
        >
          😀 Icon
        </Button>
        <Button
          type="button"
          size="sm"
          variant={value.type === 'image' ? 'default' : 'outline'}
          onClick={() => handleTypeChange('image')}
        >
          🖼️ Image
        </Button>
      </div>

      {/* Preview */}
      <div className="flex items-center space-x-3">
        {value.type === 'image' ? (
          // Image logo preview - no background, full image display
          <div className="w-12 h-12 flex items-center justify-center border border-gray-200 rounded-lg">
            {value.value ? (
              <img
                src={value.value}
                alt={value.alt || 'Logo'}
                className="max-w-12 max-h-12 object-contain"
              />
            ) : (
              <span className="text-gray-400 text-xs">No Image</span>
            )}
          </div>
        ) : (
          // Text and Icon logos - with blue background
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
            {value.type === 'text' && (
              <span className="text-white font-bold text-lg">{value.value}</span>
            )}
            {value.type === 'icon' && (
              <span className="text-white text-lg">{value.value}</span>
            )}
          </div>
        )}
        <div className="text-sm text-gray-600">Preview</div>
      </div>

      {/* Value Input */}
      {value.type === 'text' && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Text</label>
          <Input
            type="text"
            value={value.value}
            onChange={(e) => onChange({
              ...value,
              value: e.target.value
            })}
            placeholder="e.g., A, Logo, 公司"
            maxLength={3}
          />
        </div>
      )}

      {value.type === 'icon' && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700">Icon</label>
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={() => setShowIconPicker(!showIconPicker)}
            >
              Choose Icon
            </Button>
          </div>
          <Input
            type="text"
            value={value.value}
            onChange={(e) => onChange({
              ...value,
              value: e.target.value
            })}
            placeholder="e.g., 🏢, 💼, A"
          />
          
          {showIconPicker && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white shadow-lg max-h-64 overflow-y-auto">
              <div className="space-y-4">
                {Object.entries(iconCategories).map(([category, icons]) => (
                  <div key={category}>
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                      {category === 'business' && '🏢 Business'}
                      {category === 'office' && '💼 Office'}
                      {category === 'tools' && '🔧 Tools'}
                      {category === 'creative' && '🌟 Creative'}
                      {category === 'transport' && '🚀 Transport'}
                      {category === 'letters' && '🔤 Letters'}
                    </h4>
                    <div className="grid grid-cols-8 gap-2">
                      {icons.map((icon, index) => (
                        <button
                          key={`${category}-${index}`}
                          type="button"
                          onClick={() => handleIconSelect(icon)}
                          className="w-8 h-8 flex items-center justify-center hover:bg-blue-50 hover:border-blue-300 rounded border border-gray-200 text-lg transition-colors"
                        >
                          {icon}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {value.type === 'image' && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Image</label>
          <div className="flex space-x-2">
            <Input
              type="url"
              value={value.value}
              onChange={(e) => onChange({
                ...value,
                value: e.target.value
              })}
              placeholder="https://example.com/logo.png"
              className="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
            >
              Upload
            </Button>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Alt Text</label>
            <Input
              type="text"
              value={value.alt || ''}
              onChange={(e) => onChange({
                ...value,
                alt: e.target.value
              })}
              placeholder="e.g., Company Logo"
            />
          </div>
        </div>
      )}
    </div>
  )
}
