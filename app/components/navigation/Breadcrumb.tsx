import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

export interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  showHome?: boolean
}

export default function Breadcrumb({ 
  items, 
  className = '', 
  showHome = true 
}: BreadcrumbProps) {
  // Add home item if showHome is true and not already present
  const breadcrumbItems = showHome && items[0]?.label !== 'Home' 
    ? [{ label: 'Home', href: '/' }, ...items]
    : items

  // Generate JSON-LD structured data for breadcrumbs
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbItems.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.label,
      ...(item.href && { item: item.href }),
    })),
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      {/* Breadcrumb Navigation */}
      <nav 
        className={`flex ${className}`} 
        aria-label="Breadcrumb"
      >
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          {breadcrumbItems.map((item, index) => {
            const isLast = index === breadcrumbItems.length - 1
            const isFirst = index === 0

            return (
              <li key={index} className="inline-flex items-center">
                {/* Separator (except for first item) */}
                {!isFirst && (
                  <ChevronRight
                    className="w-4 h-4 text-gray-400 mx-1 md:mx-2"
                    aria-hidden="true"
                  />
                )}

                {/* Breadcrumb Item */}
                <div className="flex items-center">
                  {isFirst && showHome && (
                    <Home
                      className="w-4 h-4 mr-1 text-gray-400"
                      aria-hidden="true"
                    />
                  )}
                  
                  {item.href && !isLast ? (
                    <Link
                      href={item.href}
                      className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
                    >
                      {item.label}
                    </Link>
                  ) : (
                    <span 
                      className={`text-sm font-medium ${
                        isLast 
                          ? 'text-gray-500 cursor-default' 
                          : 'text-gray-700'
                      }`}
                      aria-current={isLast ? 'page' : undefined}
                    >
                      {item.label}
                    </span>
                  )}
                </div>
              </li>
            )
          })}
        </ol>
      </nav>
    </>
  )
}

// Utility function to generate breadcrumbs from pathname
export function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  // Build breadcrumbs based on URL segments
  segments.forEach((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/')
    const isLast = index === segments.length - 1
    
    // Convert segment to readable label
    let label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')

    // Special cases for known routes
    switch (segment) {
      case 'products':
        label = 'Products'
        break
      case 'category':
        label = 'Category'
        break
      case 'blog':
        label = 'Blog'
        break
      case 'contact':
        label = 'Contact'
        break
      case 'about':
        label = 'About'
        break
      default:
        // For dynamic segments, keep the formatted version
        break
    }

    breadcrumbs.push({
      label,
      href: isLast ? undefined : href,
      current: isLast,
    })
  })

  return breadcrumbs
}

// Hook for easy breadcrumb generation in pages
export function useBreadcrumbs(customItems?: BreadcrumbItem[]) {
  if (typeof window === 'undefined') {
    return customItems || []
  }

  if (customItems) {
    return customItems
  }

  return generateBreadcrumbs(window.location.pathname)
}
