'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { MediaWithRelations, ApiResponse, PaginatedResponse } from '@/app/types'

interface MediaSelectorProps {
  isOpen: boolean
  onClose: () => void
  onSelect: (media: MediaWithRelations) => void
  selectedUrl?: string
  title?: string
  allowedTypes?: string[]
}

export default function MediaSelector({
  isOpen,
  onClose,
  onSelect,
  selectedUrl,
  title = 'Select Media',
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
}: MediaSelectorProps) {
  const [media, setMedia] = useState<MediaWithRelations[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [search, setSearch] = useState('')
  const [filter, setFilter] = useState('all')
  const [selectedMedia, setSelectedMedia] = useState<MediaWithRelations | null>(null)
  const [previewMedia, setPreviewMedia] = useState<MediaWithRelations | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    if (isOpen) {
      fetchMedia()
    }
  }, [isOpen, search, filter, pagination.page])

  useEffect(() => {
    if (selectedUrl && media.length > 0) {
      const found = media.find(m => m.url === selectedUrl)
      if (found) {
        setSelectedMedia(found)
      }
    }
  }, [selectedUrl, media])

  const fetchMedia = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      })
      
      if (search) {
        params.append('search', search)
      }
      if (filter !== 'all') {
        params.append('type', filter)
      }

      const response = await fetch(`/api/media?${params.toString()}`, {
        credentials: 'include'
      })
      const result: PaginatedResponse<MediaWithRelations> = await response.json()

      if (result.success && result.data) {
        setMedia(result.data)
        if (result.pagination) {
          setPagination(result.pagination)
        }
      } else {
        console.error('Error fetching media:', result.error)
        setMedia([])
      }
    } catch (error) {
      console.error('Error fetching media:', error)
      setMedia([])
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setUploading(true)

    try {
      for (const file of Array.from(files)) {
        // Check file type
        if (!allowedTypes.includes(file.type)) {
          alert(`File type ${file.type} is not allowed`)
          continue
        }

        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('/api/upload', {
          method: 'POST',
          credentials: 'include',
          body: formData,
        })

        const result: ApiResponse<MediaWithRelations> = await response.json()
        if (!result.success) {
          console.error('Upload failed:', result.error)
          alert(`Failed to upload ${file.name}: ${result.error}`)
        }
      }

      // Refresh the media list
      fetchMedia()
    } catch (error) {
      console.error('Upload error:', error)
      alert('Upload failed')
    } finally {
      setUploading(false)
      // Reset the input
      event.target.value = ''
    }
  }

  const handleSelect = () => {
    if (selectedMedia) {
      onSelect(selectedMedia)
      onClose()
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isImage = (mimeType: string) => {
    return mimeType.startsWith('image/')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b flex-shrink-0">
          <h2 className="text-xl font-semibold">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Upload and Search */}
          <div className="p-6 pb-4 border-b flex-shrink-0">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Upload Button */}
              <div className="relative">
                <input
                  type="file"
                  multiple
                  accept={allowedTypes.join(',')}
                  onChange={handleFileUpload}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  disabled={uploading}
                />
                <Button
                  variant="outline"
                  disabled={uploading}
                  className="w-full sm:w-auto"
                >
                  {uploading ? 'Uploading...' : 'Upload Files'}
                </Button>
              </div>

              {/* Search */}
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search media..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Filter */}
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Files</option>
                <option value="image">Images</option>
                <option value="document">Documents</option>
              </select>
            </div>
          </div>

          {/* Media Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : media.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No media files found.</p>
                <p className="text-sm text-gray-400 mt-2">Upload some files to get started.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {media.map((file) => (
                  <div
                    key={file.id}
                    className={`relative bg-gray-50 rounded-lg p-2 cursor-pointer transition-all border-2 group ${
                      selectedMedia?.id === file.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-transparent hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedMedia(file)}
                  >
                    {/* Media Preview */}
                    <div className="aspect-square relative mb-2 bg-white rounded overflow-hidden">
                      {isImage(file.mimeType) ? (
                        <>
                          <Image
                            src={file.url}
                            alt={file.alt || file.originalName}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 16vw"
                            unoptimized={file.url.startsWith('http')}
                          />
                          {/* Preview overlay */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                setPreviewMedia(file)
                              }}
                              className="px-2 py-1 bg-white bg-opacity-90 text-gray-800 rounded text-xs font-medium hover:bg-opacity-100 transition-all"
                              title="Preview full size"
                            >
                              👁️ Preview
                            </button>
                          </div>
                        </>
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-100">
                          <div className="text-center">
                            <div className="text-2xl mb-1">📄</div>
                            <div className="text-xs text-gray-500 truncate">
                              {file.originalName.split('.').pop()?.toUpperCase()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* File Info */}
                    <div className="text-xs">
                      <p className="font-medium truncate" title={file.originalName}>
                        {file.originalName}
                      </p>
                      <p className="text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>

                    {/* Selection Indicator */}
                    {selectedMedia?.id === file.id && (
                      <div className="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t flex items-center justify-between flex-shrink-0">
              <div className="text-sm text-gray-700">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} files
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Fixed Actions at Bottom */}
        <div className="flex-shrink-0 border-t bg-white">
          {/* Selected Media Preview - Inside fixed bottom area */}
          {selectedMedia && (
            <div className="p-3 bg-blue-50 border-b border-blue-200">
              <div className="flex items-center space-x-3">
                {/* Preview */}
                <div className="flex-shrink-0">
                  {isImage(selectedMedia.mimeType) ? (
                    <div className="relative w-10 h-10 bg-white rounded overflow-hidden border">
                      <Image
                        src={selectedMedia.url}
                        alt={selectedMedia.alt || selectedMedia.originalName}
                        fill
                        className="object-cover"
                        sizes="40px"
                        unoptimized={selectedMedia.url.startsWith('http')}
                      />
                    </div>
                  ) : (
                    <div className="w-10 h-10 bg-white rounded border flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-sm">📄</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    Selected: {selectedMedia.originalName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(selectedMedia.size)} • {selectedMedia.mimeType}
                  </p>
                </div>

                {/* Quick Actions */}
                <div className="flex-shrink-0">
                  {isImage(selectedMedia.mimeType) && (
                    <button
                      type="button"
                      onClick={() => setPreviewMedia(selectedMedia)}
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      👁️ Preview
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="p-6">
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleSelect}
                disabled={!selectedMedia}
              >
                Select {selectedMedia ? `"${selectedMedia.originalName}"` : ''}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {previewMedia && isImage(previewMedia.mimeType) && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[60] p-4">
          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            {/* Close button */}
            <button
              onClick={() => setPreviewMedia(null)}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-xl hover:bg-opacity-70 transition-all"
              title="Close preview"
            >
              ×
            </button>

            {/* Image */}
            <div className="relative w-full h-full flex items-center justify-center">
              <Image
                src={previewMedia.url}
                alt={previewMedia.alt || previewMedia.originalName}
                fill
                className="object-contain"
                sizes="(max-width: 768px) 100vw, 1024px"
                unoptimized={previewMedia.url.startsWith('http')}
              />
            </div>

            {/* Image info overlay */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-4 rounded-lg">
              <h3 className="font-medium text-lg mb-2">{previewMedia.originalName}</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-300">Size:</span> {formatFileSize(previewMedia.size)}
                </div>
                <div>
                  <span className="text-gray-300">Type:</span> {previewMedia.mimeType}
                </div>
                {previewMedia.alt && (
                  <div className="sm:col-span-2">
                    <span className="text-gray-300">Alt text:</span> {previewMedia.alt}
                  </div>
                )}
                {previewMedia.caption && (
                  <div className="sm:col-span-2">
                    <span className="text-gray-300">Caption:</span> {previewMedia.caption}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="mt-3 flex space-x-3">
                <button
                  onClick={() => {
                    setSelectedMedia(previewMedia)
                    setPreviewMedia(null)
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  Select This Image
                </button>
                <button
                  onClick={() => window.open(previewMedia.url, '_blank')}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors text-sm font-medium"
                >
                  Open in New Tab
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
