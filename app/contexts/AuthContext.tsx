'use client'

import React, { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { api } from '@/app/lib/api-client'

interface User {
  id: string
  name: string
  email: string
  role: string
  avatar?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const checkAuth = async () => {
    try {
      const response = await api.me()
      if (response.success && response.data) {
        setUser(response.data)
      } else {
        setUser(null)
        // Clear any stored tokens
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth-token')
          document.cookie = 'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await api.login({ email, password })
      
      if (response.success && response.data) {
        const { user: userData, token } = response.data
        
        // Store token
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth-token', token)
          // Also set as cookie for SSR
          document.cookie = `auth-token=${token}; path=/; max-age=${7 * 24 * 60 * 60}` // 7 days
        }
        
        setUser(userData)
        return true
      } else {
        console.error('Login failed:', response.error)
        return false
      }
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  const logout = async () => {
    try {
      await api.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear user state and tokens
      setUser(null)
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth-token')
        document.cookie = 'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      }
      router.push('/auth/login')
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  const value = {
    user,
    loading,
    login,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protecting admin routes
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  requiredRole?: string
) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!loading) {
        if (!user) {
          router.push('/auth/login')
          return
        }

        if (requiredRole && user.role !== requiredRole && user.role !== 'ADMIN') {
          router.push('/admin') // Redirect to dashboard if insufficient permissions
          return
        }
      }
    }, [user, loading, router])

    if (loading) {
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )
    }

    if (!user) {
      return null // Will redirect to login
    }

    if (requiredRole && user.role !== requiredRole && user.role !== 'ADMIN') {
      return null // Will redirect to dashboard
    }

    return <WrappedComponent {...props} />
  }
}
