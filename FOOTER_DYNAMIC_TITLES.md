# 底部分组标题动态化功能

## 🎯 功能概述

为了进一步提升底部配置的灵活性，我们为Product和Support分组添加了动态标题功能。现在管理员可以：

- **自定义Product分组标题**：将"Product"改为"产品"、"服务"、"解决方案"等
- **自定义Support分组标题**：将"Support"改为"支持"、"帮助"、"客服"等
- **多语言支持**：支持中英文混合或其他语言的标题
- **品牌定制**：根据企业特色定制分组名称

## 🔧 新增功能

### 1. **Product分组标题自定义** ✅
- **动态标题**：可以自定义Product分组的显示标题
- **实时预览**：修改后立即在前台生效
- **多语言支持**：支持中文、英文或其他语言
- **品牌适配**：可以改为"产品"、"服务"、"解决方案"等

### 2. **Support分组标题自定义** ✅
- **动态标题**：可以自定义Support分组的显示标题
- **实时预览**：修改后立即在前台生效
- **多语言支持**：支持中文、英文或其他语言
- **服务适配**：可以改为"支持"、"帮助"、"客服"、"服务"等

## 🎨 管理界面升级

### Product Links Settings
```
📦 Product Links Settings
┌─────────────────────────────────────────────────────┐
│ Section Title: [产品与服务                    ]     │
│                                                     │
│ Product Links:                                      │
│ ☑ 功能特性      /features                     [×] │
│ ☑ 模板中心      /templates                    [×] │
│ ☑ 集成方案      /integrations                [×] │
│ ☑ 价格方案      /pricing                      [×] │
│                                    [+ Add Product Link] │
└─────────────────────────────────────────────────────┘
```

### Support Links Settings
```
🛠️ Support Links Settings
┌─────────────────────────────────────────────────────┐
│ Section Title: [客户支持                      ]     │
│                                                     │
│ Support Links:                                      │
│ ☑ 帮助中心      /help                         [×] │
│ ☑ 使用文档      /docs                         [×] │
│ ☑ 联系客服      /contact                      [×] │
│ ☑ 系统状态      /status                       [×] │
│                                    [+ Add Support Link] │
└─────────────────────────────────────────────────────┘
```

## 🌟 前台显示效果

### 中文标题示例
```
┌─────────────────────────────────────────────────────┐
│ 产品与服务           客户支持                        │
│ • 功能特性           • 帮助中心                      │
│ • 模板中心           • 使用文档                      │
│ • 集成方案           • 联系客服                      │
│ • 价格方案           • 系统状态                      │
│                                                     │
│ 公司信息             开发资源                        │
│ • 关于我们           • 技术博客                      │
│ • 加入我们           • 案例研究                      │
│ • 新闻动态           • 白皮书                        │
└─────────────────────────────────────────────────────┘
```

### 英文标题示例
```
┌─────────────────────────────────────────────────────┐
│ SOLUTIONS            CUSTOMER CARE                  │
│ • Enterprise         • Help Desk                    │
│ • Small Business     • Documentation                │
│ • Developers         • Live Chat                    │
│ • Agencies           • Status Page                  │
│                                                     │
│ COMPANY              RESOURCES                      │
│ • About Us           • Blog                         │
│ • Careers            • Case Studies                 │
│ • Press              • Whitepapers                  │
└─────────────────────────────────────────────────────┘
```

### 混合语言示例
```
┌─────────────────────────────────────────────────────┐
│ Products             支持服务                        │
│ • Features           • 帮助中心                      │
│ • Templates          • 在线文档                      │
│ • Integrations       • 技术支持                      │
│ • Pricing            • 服务状态                      │
└─────────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 数据结构升级
```typescript
// 旧结构
interface FooterConfig {
  productLinks: Array<{
    name: string
    href: string
    enabled: boolean
  }>
  supportLinks: Array<{
    name: string
    href: string
    enabled: boolean
  }>
}

// 新结构
interface FooterConfig {
  productLinks: {
    title: string  // 新增：分组标题
    links: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }
  supportLinks: {
    title: string  // 新增：分组标题
    links: Array<{
      name: string
      href: string
      enabled: boolean
    }>
  }
}
```

### 默认配置
```typescript
const DEFAULT_THEME_CONFIG = {
  footer: {
    productLinks: {
      title: 'Product',  // 默认英文标题
      links: [
        { name: 'Features', href: '/products', enabled: true },
        { name: 'Templates', href: '/products', enabled: true },
        { name: 'Integrations', href: '/products', enabled: true },
        { name: 'Pricing', href: '/contact', enabled: true }
      ]
    },
    supportLinks: {
      title: 'Support',  // 默认英文标题
      links: [
        { name: 'Help Center', href: '/contact', enabled: true },
        { name: 'Documentation', href: '/blog', enabled: true },
        { name: 'Contact Support', href: '/contact', enabled: true },
        { name: 'System Status', href: '/about', enabled: true }
      ]
    }
  }
}
```

### 前台渲染逻辑
```typescript
// Product分组渲染
<div>
  <h4 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
    {config.footer.productLinks.title}
  </h4>
  <ul className="space-y-2 text-sm">
    {config.footer.productLinks.links
      .filter(link => link.enabled)
      .map((link, index) => (
        <li key={index}>
          <Link href={link.href}>{link.name}</Link>
        </li>
      ))}
  </ul>
</div>

// Support分组渲染
<div>
  <h4 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
    {config.footer.supportLinks.title}
  </h4>
  <ul className="space-y-2 text-sm">
    {config.footer.supportLinks.links
      .filter(link => link.enabled)
      .map((link, index) => (
        <li key={index}>
          <Link href={link.href}>{link.name}</Link>
        </li>
      ))}
  </ul>
</div>
```

### 管理界面实现
```typescript
// Product标题编辑
<Input
  type="text"
  value={config.footer?.productLinks?.title || ''}
  onChange={(e) => {
    const updatedConfig = { ...config }
    if (!updatedConfig.footer.productLinks) {
      updatedConfig.footer.productLinks = { title: '', links: [] }
    }
    updatedConfig.footer.productLinks.title = e.target.value
    setConfig(updatedConfig)
  }}
  placeholder="e.g., Product"
/>

// Support标题编辑
<Input
  type="text"
  value={config.footer?.supportLinks?.title || ''}
  onChange={(e) => {
    const updatedConfig = { ...config }
    if (!updatedConfig.footer.supportLinks) {
      updatedConfig.footer.supportLinks = { title: '', links: [] }
    }
    updatedConfig.footer.supportLinks.title = e.target.value
    setConfig(updatedConfig)
  }}
  placeholder="e.g., Support"
/>
```

## 🎯 使用场景

### 1. **中文网站**
- **产品分组**：产品与服务、解决方案、功能特性
- **支持分组**：客户支持、帮助中心、服务保障

### 2. **英文网站**
- **产品分组**：Solutions, Products, Services
- **支持分组**：Customer Care, Help & Support, Resources

### 3. **多语言网站**
- **混合使用**：Products + 客户支持
- **本地化**：根据目标市场调整标题

### 4. **行业定制**
- **SaaS平台**：Solutions + Developer Support
- **电商网站**：商品分类 + 购物帮助
- **企业官网**：业务服务 + 客户服务

## 🚀 操作指南

### 管理员操作流程
1. **访问管理后台**：`/admin/themes` → 底部标签
2. **找到分组设置**：
   - 📦 Product Links Settings
   - 🛠️ Support Links Settings
3. **编辑分组标题**：
   - 在"Section Title"输入框中输入新标题
   - 支持中文、英文或其他语言
4. **管理分组链接**：
   - 添加、编辑或删除分组内的链接
   - 控制每个链接的启用状态
5. **保存配置**：点击"Save Changes"
6. **前台验证**：在前台页面查看标题更改效果

### 标题设置建议
- **简洁明了**：标题应该简洁且易于理解
- **品牌一致**：与整体品牌风格保持一致
- **用户友好**：使用用户熟悉的术语
- **SEO友好**：考虑搜索引擎优化

## 📊 配置示例

### 中文企业网站配置
```json
{
  "footer": {
    "productLinks": {
      "title": "产品与服务",
      "links": [
        { "name": "企业版", "href": "/enterprise", "enabled": true },
        { "name": "专业版", "href": "/professional", "enabled": true },
        { "name": "定制服务", "href": "/custom", "enabled": true },
        { "name": "价格方案", "href": "/pricing", "enabled": true }
      ]
    },
    "supportLinks": {
      "title": "客户支持",
      "links": [
        { "name": "帮助中心", "href": "/help", "enabled": true },
        { "name": "在线客服", "href": "/chat", "enabled": true },
        { "name": "技术文档", "href": "/docs", "enabled": true },
        { "name": "服务状态", "href": "/status", "enabled": true }
      ]
    }
  }
}
```

### 英文SaaS平台配置
```json
{
  "footer": {
    "productLinks": {
      "title": "Solutions",
      "links": [
        { "name": "Enterprise", "href": "/enterprise", "enabled": true },
        { "name": "Small Business", "href": "/smb", "enabled": true },
        { "name": "Developers", "href": "/developers", "enabled": true },
        { "name": "Pricing", "href": "/pricing", "enabled": true }
      ]
    },
    "supportLinks": {
      "title": "Developer Support",
      "links": [
        { "name": "API Docs", "href": "/api", "enabled": true },
        { "name": "SDK", "href": "/sdk", "enabled": true },
        { "name": "Community", "href": "/community", "enabled": true },
        { "name": "Status", "href": "/status", "enabled": true }
      ]
    }
  }
}
```

## 🌟 核心优势

### ✅ **完全自定义**
- 分组标题完全可自定义
- 支持任意语言和字符
- 实时预览和生效

### ✅ **多语言友好**
- 支持中文、英文混合使用
- 适应不同地区的用户习惯
- 便于国际化网站建设

### ✅ **品牌一致性**
- 可以使用企业专有术语
- 保持品牌语言风格一致
- 提升用户体验

### ✅ **向后兼容**
- 自动处理旧格式数据
- 平滑升级，无需重新配置
- 保护现有配置不丢失

### ✅ **SEO优化**
- 标题作为HTML标签渲染
- 有利于搜索引擎理解网站结构
- 提升页面语义化程度

现在Product和Support分组的标题也完全动态化了！管理员可以根据业务特色、目标用户和品牌风格自由定制分组标题，创建更加个性化和本地化的网站底部。🎉
