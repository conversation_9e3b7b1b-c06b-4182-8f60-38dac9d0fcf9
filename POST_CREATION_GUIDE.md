# 文章创建页面使用指南

## 概述

新创建的文章创建页面 (`/admin/posts/new`) 提供了一个完整的、用户友好的界面来创建和发布博客文章。

## 功能特性

### 🎨 现代化UI设计
- 响应式布局，支持桌面和移动设备
- 清晰的三栏布局：主内容区、侧边栏
- 现代化的卡片设计和表单元素

### 📝 完整的文章编辑功能
- **基本信息**：标题、URL slug、摘要、特色图片
- **内容编辑**：大型文本区域，支持长篇内容
- **分类管理**：从现有分类中选择
- **标签系统**：多选标签，支持颜色显示
- **SEO优化**：Meta标题和描述

### 🚀 智能功能
- **自动Slug生成**：根据标题自动生成URL友好的slug
- **自动摘要**：如果未提供摘要，系统会从内容中自动提取
- **实时验证**：表单字段的实时错误提示
- **双重发布选项**：保存为草稿或直接发布

## 页面结构

### 主内容区
1. **基本信息卡片**
   - 标题（必填）
   - Slug（自动生成，可编辑）
   - 摘要（可选）
   - 特色图片URL（可选）

2. **内容卡片**
   - 大型文本编辑器
   - 支持长篇内容编写

### 侧边栏
1. **发布操作**
   - 保存为草稿按钮
   - 立即发布按钮
   - 错误信息显示

2. **分类选择**
   - 下拉选择框
   - 显示所有可用分类

3. **标签管理**
   - 复选框列表
   - 支持多选
   - 显示标签颜色

4. **SEO设置**
   - Meta标题（可选）
   - Meta描述（可选）

## 使用流程

### 1. 访问页面
```
http://localhost:3000/admin/posts/new
```

### 2. 填写基本信息
- 输入文章标题（系统会自动生成slug）
- 可选择性地添加摘要和特色图片

### 3. 编写内容
- 在内容区域编写文章正文
- 支持长篇内容

### 4. 设置分类和标签
- 从侧边栏选择合适的分类
- 勾选相关的标签

### 5. SEO优化（可选）
- 设置Meta标题和描述
- 如果不设置，系统会使用默认值

### 6. 发布文章
- **保存为草稿**：文章保存但不公开
- **立即发布**：文章立即发布并公开

## 数据验证

系统会验证以下必填字段：
- 标题
- 内容
- Slug

如果验证失败，会显示相应的错误信息。

## API集成

页面与以下API端点集成：
- `GET /api/categories` - 获取分类列表
- `GET /api/tags` - 获取标签列表
- `POST /api/posts` - 创建新文章

## 技术实现

### 前端技术栈
- **React 18** + **Next.js 15**
- **TypeScript** 类型安全
- **Tailwind CSS** 样式框架
- **自定义UI组件** (Button, Input, Card等)

### 状态管理
- React useState hooks
- 表单状态管理
- 错误状态处理
- 加载状态指示

### 用户体验优化
- 实时表单验证
- 加载状态指示
- 错误信息提示
- 响应式设计

## 示例数据

系统已预置以下示例数据：

### 分类
- Technology (技术)
- Business (商业)
- Lifestyle (生活方式)
- Health (健康)

### 标签
- JavaScript, React, Next.js
- AI, Machine Learning
- Web Development, Mobile
- Design, Marketing, Productivity

## 故障排除

### 常见问题

1. **页面无法加载**
   - 确保开发服务器正在运行
   - 检查控制台错误信息

2. **分类/标签不显示**
   - 运行种子脚本：`node scripts/seed-categories-tags.js`
   - 检查数据库连接

3. **表单提交失败**
   - 检查网络连接
   - 确保用户已登录且有权限
   - 查看浏览器控制台错误

### 开发调试

```bash
# 启动开发服务器
npm run dev

# 添加示例数据
node scripts/seed-categories-tags.js

# 检查API状态
curl http://localhost:3000/api/categories
curl http://localhost:3000/api/tags
```

## 下一步计划

- [ ] 添加富文本编辑器
- [ ] 图片上传功能
- [ ] 文章预览功能
- [ ] 自动保存草稿
- [ ] 文章模板系统
