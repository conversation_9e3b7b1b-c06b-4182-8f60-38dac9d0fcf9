# 媒体选择器功能完整指南

## 🎯 概述

已成功为文章创建和编辑页面集成了完整的媒体选择器功能，包括：
- 🖼️ **媒体浏览器** - 可视化选择已上传的媒体文件
- ⬆️ **文件上传** - 直接在选择器中上传新文件
- 🔍 **搜索和筛选** - 快速找到需要的媒体文件
- 📱 **响应式设计** - 完美适配各种设备

## ✨ 主要功能特性

### 1. 媒体输入组件 (MediaInput)
- **智能预览** - 图片文件自动显示预览
- **URL输入** - 支持手动输入媒体URL
- **一键浏览** - 点击"Browse Media"打开选择器
- **文件信息** - 显示非图片文件的详细信息

### 2. 媒体选择器 (MediaSelector)
- **网格布局** - 清晰的媒体文件网格显示
- **文件类型支持** - 图片、文档等多种文件类型
- **实时上传** - 拖拽或点击上传新文件
- **搜索功能** - 按文件名、alt文本搜索
- **类型筛选** - 按图片、文档等类型筛选
- **分页浏览** - 支持大量文件的分页显示

### 3. 文件上传功能
- **多文件上传** - 支持同时上传多个文件
- **类型验证** - 自动验证文件类型
- **大小限制** - 10MB文件大小限制
- **进度指示** - 上传过程的状态显示

## 🔧 技术实现

### 组件架构
```
MediaInput (主输入组件)
├── 预览区域 (图片预览/文件信息)
├── 输入框 (URL手动输入)
├── 浏览按钮 (打开选择器)
└── MediaSelector (媒体选择器弹框)
    ├── 上传区域
    ├── 搜索和筛选
    ├── 媒体网格
    └── 分页控件
```

### API集成
- **GET /api/media** - 获取媒体文件列表
- **POST /api/upload** - 上传新文件
- **GET /api/media/[id]** - 获取单个媒体文件
- **DELETE /api/media/[id]** - 删除媒体文件

### 数据库模型
```typescript
model Media {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  url          String
  alt          String?
  caption      String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relations
  posts        Post[]
  products     Product[]
}
```

## 📝 使用方法

### 在文章创建/编辑页面

1. **选择特色图片**
   - 在"Featured Image"字段中点击"Browse Media"
   - 浏览现有媒体文件或上传新文件
   - 点击选择所需图片
   - 图片将自动显示预览

2. **上传新文件**
   - 在媒体选择器中点击"Upload Files"
   - 选择一个或多个文件
   - 系统自动验证文件类型和大小
   - 上传完成后文件出现在列表中

3. **搜索媒体文件**
   - 在搜索框中输入关键词
   - 系统会搜索文件名、alt文本等
   - 实时显示搜索结果

4. **筛选文件类型**
   - 使用下拉菜单选择文件类型
   - "All Files" - 显示所有文件
   - "Images" - 只显示图片文件
   - "Documents" - 只显示文档文件

## 🎨 用户界面特性

### 现代化设计
- **卡片式布局** - 清晰的视觉层次
- **悬停效果** - 交互反馈
- **选中状态** - 明确的选择指示
- **加载状态** - 操作过程的视觉反馈

### 响应式适配
- **桌面端** - 6列网格布局
- **平板端** - 4列网格布局
- **手机端** - 2列网格布局
- **弹框适配** - 自动调整大小

### 文件预览
- **图片文件** - 缩略图预览
- **文档文件** - 文件类型图标
- **文件信息** - 名称、大小显示
- **选择指示** - 蓝色选中标记

## 🔒 安全特性

### 文件验证
- **类型检查** - 只允许指定的文件类型
- **大小限制** - 最大10MB文件大小
- **扩展名验证** - 防止恶意文件上传

### 权限控制
- **登录验证** - 需要用户登录
- **角色检查** - 需要EDITOR或以上权限
- **API保护** - 所有媒体API都有权限验证

## 📊 支持的文件类型

### 图片文件
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### 文档文件
- PDF (.pdf)
- Word文档 (.doc, .docx)
- 文本文件 (.txt)

### 扩展支持
可以通过修改API配置添加更多文件类型支持。

## 🚀 性能优化

### 前端优化
- **懒加载** - 图片按需加载
- **分页加载** - 避免一次性加载大量文件
- **缓存策略** - 合理的数据缓存
- **图片优化** - Next.js Image组件优化

### 后端优化
- **分页查询** - 限制单次查询数据量
- **索引优化** - 数据库查询优化
- **文件存储** - 本地存储或CDN

## 🛠️ 配置选项

### MediaInput组件配置
```typescript
<MediaInput
  label="Featured Image"
  value={imageUrl}
  onChange={handleChange}
  placeholder="Select or enter image URL"
  description="Choose an image to represent this post"
  allowedTypes={['image/jpeg', 'image/png']}
  required={false}
  disabled={false}
/>
```

### MediaSelector组件配置
```typescript
<MediaSelector
  isOpen={showSelector}
  onClose={handleClose}
  onSelect={handleSelect}
  selectedUrl={currentUrl}
  title="Select Media"
  allowedTypes={['image/jpeg', 'image/png']}
/>
```

## 🔍 故障排除

### 常见问题

1. **媒体选择器不显示文件**
   - 检查用户是否已登录
   - 确认用户有EDITOR权限
   - 查看浏览器控制台错误

2. **文件上传失败**
   - 检查文件类型是否支持
   - 确认文件大小不超过10MB
   - 检查uploads目录权限

3. **图片预览不显示**
   - 确认图片URL可访问
   - 检查图片文件是否损坏
   - 查看网络连接状态

### 调试命令
```bash
# 检查媒体API（需要登录）
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/media

# 检查uploads目录
ls -la public/uploads/

# 查看媒体数据
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.media.findMany().then(console.log);
"
```

## 📈 使用统计

### 示例数据
系统已预置8个示例媒体文件：
- 6个高质量图片（来自Unsplash）
- 2个文档文件示例

### 功能覆盖
- ✅ 图片选择和预览
- ✅ 文件上传和验证
- ✅ 搜索和筛选
- ✅ 分页浏览
- ✅ 响应式设计
- ✅ 权限控制

## 🎯 下一步计划

- [ ] 图片编辑功能（裁剪、调整大小）
- [ ] 批量上传和管理
- [ ] 文件夹组织功能
- [ ] CDN集成
- [ ] 图片压缩和优化
- [ ] 拖拽上传界面
- [ ] 媒体使用统计
- [ ] 自动alt文本生成

## 📞 技术支持

媒体选择器现已完全集成到文章管理系统中，提供了完整的媒体管理功能。如遇问题，请检查：

1. 开发服务器运行状态
2. 用户登录和权限
3. 文件类型和大小限制
4. 网络连接状态

🎉 **媒体选择器功能已完全就绪，可以开始使用！**
