# 部署指南 | Deployment Guide

## 🚀 生产环境部署 | Production Deployment

### 环境准备 | Environment Preparation

#### 系统要求 | System Requirements
- **Node.js**: 18.0.0 或更高版本 | 18.0.0 or higher
- **PostgreSQL**: 14.0 或更高版本 | 14.0 or higher
- **内存**: 最少 2GB RAM | Minimum 2GB RAM
- **存储**: 最少 10GB 可用空间 | Minimum 10GB available space

#### 环境变量配置 | Environment Variables Configuration

创建 `.env.production` 文件 | Create `.env.production` file:

```env
# 应用配置 | Application Configuration
NODE_ENV=production
PORT=3000
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-super-secure-nextauth-secret-here

# 数据库配置 | Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/instai_cms_prod

# JWT配置 | JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-here

# 邮件配置 | Email Configuration (可选 | Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 文件上传配置 | File Upload Configuration
UPLOAD_DIR=/var/www/uploads
MAX_FILE_SIZE=10485760

# 缓存配置 | Cache Configuration (可选 | Optional)
REDIS_URL=redis://localhost:6379
```

### 数据库设置 | Database Setup

#### PostgreSQL安装和配置 | PostgreSQL Installation and Configuration

1. **安装PostgreSQL | Install PostgreSQL**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

2. **创建数据库和用户 | Create Database and User**
```sql
-- 连接到PostgreSQL | Connect to PostgreSQL
sudo -u postgres psql

-- 创建数据库 | Create database
CREATE DATABASE instai_cms_prod;

-- 创建用户 | Create user
CREATE USER cms_user WITH ENCRYPTED PASSWORD 'secure_password_here';

-- 授予权限 | Grant permissions
GRANT ALL PRIVILEGES ON DATABASE instai_cms_prod TO cms_user;

-- 退出 | Exit
\q
```

3. **数据库迁移 | Database Migration**
```bash
# 生成Prisma客户端 | Generate Prisma client
npx prisma generate

# 运行数据库迁移 | Run database migrations
npx prisma migrate deploy

# 种子数据（可选）| Seed data (optional)
npx prisma db seed
```

### 应用部署 | Application Deployment

#### 方法1: 直接部署 | Method 1: Direct Deployment

1. **克隆代码 | Clone Code**
```bash
git clone <your-repository-url>
cd instai_cms
```

2. **安装依赖 | Install Dependencies**
```bash
npm ci --only=production
```

3. **构建应用 | Build Application**
```bash
npm run build
```

4. **启动应用 | Start Application**
```bash
# 使用PM2管理进程 | Use PM2 for process management
npm install -g pm2
pm2 start npm --name "instai-cms" -- start
pm2 save
pm2 startup
```

#### 方法2: Docker部署 | Method 2: Docker Deployment

1. **创建Dockerfile | Create Dockerfile**
```dockerfile
FROM node:18-alpine AS base

# 安装依赖阶段 | Install dependencies stage
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# 构建阶段 | Build stage
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 生成Prisma客户端 | Generate Prisma client
RUN npx prisma generate

# 构建应用 | Build application
RUN npm run build

# 运行阶段 | Runtime stage
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

2. **创建docker-compose.yml | Create docker-compose.yml**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************/instai_cms_prod
      - JWT_SECRET=your-jwt-secret
      - NEXTAUTH_SECRET=your-nextauth-secret
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=instai_cms_prod
      - POSTGRES_USER=cms_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

3. **启动服务 | Start Services**
```bash
docker-compose up -d
```

### Nginx反向代理配置 | Nginx Reverse Proxy Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL配置 | SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头 | Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 文件上传大小限制 | File upload size limit
    client_max_body_size 10M;

    # 反向代理到Next.js应用 | Reverse proxy to Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件缓存 | Static file caching
    location /_next/static {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # 上传文件服务 | Uploaded files serving
    location /uploads {
        alias /var/www/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## ☁️ 云平台部署 | Cloud Platform Deployment

### Vercel部署 | Vercel Deployment

1. **连接GitHub仓库 | Connect GitHub Repository**
   - 登录Vercel控制台 | Login to Vercel dashboard
   - 导入GitHub仓库 | Import GitHub repository
   - 选择instai_cms项目 | Select instai_cms project

2. **配置环境变量 | Configure Environment Variables**
   在Vercel项目设置中添加 | Add in Vercel project settings:
   ```
   DATABASE_URL=your-postgresql-connection-string
   JWT_SECRET=your-jwt-secret
   NEXTAUTH_SECRET=your-nextauth-secret
   NEXTAUTH_URL=https://your-vercel-domain.vercel.app
   ```

3. **配置构建设置 | Configure Build Settings**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "installCommand": "npm install"
   }
   ```

4. **部署 | Deploy**
   - 推送代码到GitHub | Push code to GitHub
   - Vercel自动部署 | Vercel auto-deploys

### AWS部署 | AWS Deployment

#### 使用AWS EC2 | Using AWS EC2

1. **创建EC2实例 | Create EC2 Instance**
   - 选择Ubuntu 22.04 LTS | Choose Ubuntu 22.04 LTS
   - 实例类型：t3.medium或更高 | Instance type: t3.medium or higher
   - 配置安全组开放端口80, 443, 22 | Configure security group for ports 80, 443, 22

2. **安装必要软件 | Install Required Software**
```bash
# 更新系统 | Update system
sudo apt update && sudo apt upgrade -y

# 安装Node.js | Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PostgreSQL | Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# 安装Nginx | Install Nginx
sudo apt install nginx -y

# 安装PM2 | Install PM2
sudo npm install -g pm2
```

3. **部署应用 | Deploy Application**
   按照上述直接部署方法进行 | Follow the direct deployment method above

#### 使用AWS RDS | Using AWS RDS

1. **创建RDS PostgreSQL实例 | Create RDS PostgreSQL Instance**
   - 引擎：PostgreSQL 14 | Engine: PostgreSQL 14
   - 实例类型：db.t3.micro（开发）或db.t3.small（生产）| Instance type: db.t3.micro (dev) or db.t3.small (prod)
   - 配置安全组允许EC2访问 | Configure security group to allow EC2 access

2. **更新数据库连接 | Update Database Connection**
```env
DATABASE_URL=*****************************************************/instai_cms
```

## 🔧 性能优化 | Performance Optimization

### 缓存配置 | Cache Configuration

#### Redis缓存 | Redis Cache
```bash
# 安装Redis | Install Redis
sudo apt install redis-server -y

# 配置Redis | Configure Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### CDN配置 | CDN Configuration
- 使用CloudFlare或AWS CloudFront | Use CloudFlare or AWS CloudFront
- 缓存静态资源 | Cache static assets
- 启用Gzip压缩 | Enable Gzip compression

### 数据库优化 | Database Optimization

```sql
-- 创建索引 | Create indexes
CREATE INDEX idx_posts_status ON "Post"(status);
CREATE INDEX idx_posts_category ON "Post"("categoryId");
CREATE INDEX idx_products_active ON "Product"(active);
CREATE INDEX idx_inquiries_status ON "Inquiry"(status);

-- 配置连接池 | Configure connection pool
-- 在DATABASE_URL中添加 | Add to DATABASE_URL:
-- ?connection_limit=10&pool_timeout=20
```

## 📊 监控和日志 | Monitoring and Logging

### 应用监控 | Application Monitoring

1. **PM2监控 | PM2 Monitoring**
```bash
# 查看应用状态 | Check application status
pm2 status

# 查看日志 | View logs
pm2 logs instai-cms

# 监控面板 | Monitoring dashboard
pm2 monit
```

2. **系统监控 | System Monitoring**
```bash
# 安装htop | Install htop
sudo apt install htop -y

# 监控系统资源 | Monitor system resources
htop
```

### 日志管理 | Log Management

```bash
# 配置日志轮转 | Configure log rotation
sudo nano /etc/logrotate.d/instai-cms

# 内容 | Content:
/var/log/instai-cms/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload instai-cms
    endscript
}
```

## 🔒 安全配置 | Security Configuration

### 防火墙配置 | Firewall Configuration
```bash
# 启用UFW | Enable UFW
sudo ufw enable

# 允许SSH | Allow SSH
sudo ufw allow 22

# 允许HTTP/HTTPS | Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态 | Check status
sudo ufw status
```

### SSL证书 | SSL Certificate
```bash
# 安装Certbot | Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书 | Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 自动续期 | Auto-renewal
sudo crontab -e
# 添加 | Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔄 备份和恢复 | Backup and Recovery

### 数据库备份 | Database Backup
```bash
# 创建备份脚本 | Create backup script
#!/bin/bash
BACKUP_DIR="/var/backups/instai-cms"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# 备份数据库 | Backup database
pg_dump -h localhost -U cms_user instai_cms_prod > $BACKUP_DIR/db_backup_$DATE.sql

# 备份上传文件 | Backup uploaded files
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz /var/www/uploads

# 删除7天前的备份 | Remove backups older than 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 自动备份 | Automated Backup
```bash
# 添加到crontab | Add to crontab
sudo crontab -e

# 每天凌晨2点备份 | Backup daily at 2 AM
0 2 * * * /path/to/backup-script.sh
```

---

**部署完成后，请访问系统状态页面检查所有功能是否正常工作：**
**After deployment, please visit the system status page to check if all functions are working properly:**

`https://yourdomain.com/admin/system-status`
