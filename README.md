# 企业官网CMS系统 | Enterprise Website CMS

一个功能完整的企业级内容管理系统，基于 Next.js 15 构建，提供现代化的网站管理解决方案。

A comprehensive enterprise-level Content Management System built with Next.js 15, providing modern website management solutions.

## 🚀 系统概述 | System Overview

### 核心特性 | Core Features
- **现代化技术栈** - Next.js 15 + TypeScript + Prisma + Tailwind CSS
- **企业级功能** - 完整的内容管理、用户权限、SEO优化
- **安全认证** - JWT + Cookie 双重认证机制
- **响应式设计** - 支持桌面端和移动端访问
- **SEO友好** - 自动生成sitemap、robots.txt，结构化数据支持

### 系统架构 | System Architecture
```
├── 前端展示层 (公开访问) | Frontend Display Layer (Public Access)
│   ├── 首页 (/) | Homepage
│   ├── 博客 (/blog) | Blog
│   ├── 产品展示 (/products) | Products
│   └── 联系页面 (/contact) | Contact
├── 管理后台 (/admin) | Admin Dashboard
│   ├── 内容管理 | Content Management
│   ├── 用户管理 | User Management
│   ├── 客户关系 | Customer Relations
│   ├── 系统设置 | System Settings
│   └── 系统监控 | System Monitoring
└── API接口层 (/api) | API Layer
    ├── 认证接口 | Authentication APIs
    ├── 内容接口 | Content APIs
    └── 系统接口 | System APIs
```

## 📋 完整功能清单 | Complete Feature List

### ✅ 内容管理系统 | Content Management System
- **博客文章管理 | Blog Management**
  - 创建、编辑、发布文章 | Create, edit, publish articles
  - 富文本编辑器支持 | Rich text editor support
  - 文章分类和标签 | Article categories and tags
  - 发布状态控制（草稿/已发布）| Publication status control
  - SEO元数据管理 | SEO metadata management

- **产品管理 | Product Management**
  - 产品信息管理 | Product information management
  - 产品分类体系（支持层级分类）| Product category system
  - 产品属性和规格 | Product attributes and specifications
  - 库存状态管理 | Inventory status management
  - 产品SEO优化 | Product SEO optimization

- **媒体文件管理 | Media Management**
  - 图片上传和管理 | Image upload and management
  - 文件组织和分类 | File organization and categorization
  - 批量操作支持 | Batch operation support
  - 图片优化和压缩 | Image optimization and compression

### ✅ 用户权限管理 | User Permission Management
- **多角色权限控制 | Multi-role Permission Control**
  - ADMIN（管理员）：完全访问权限 | Full access permissions
  - EDITOR（编辑员）：内容编辑权限 | Content editing permissions
  - USER（普通用户）：基础访问权限 | Basic access permissions

- **用户管理功能 | User Management Features**
  - 用户注册和登录 | User registration and login
  - 用户信息管理 | User information management
  - 密码安全策略 | Password security policies
  - 会话管理 | Session management

### ✅ 客户关系管理 | Customer Relationship Management
- **询盘系统 | Inquiry System**
  - 客户询价表单 | Customer inquiry forms
  - 询盘状态跟踪 | Inquiry status tracking
  - 客户信息管理 | Customer information management
  - 询盘统计分析 | Inquiry statistics and analysis

- **评论系统 | Comment System**
  - 文章评论功能 | Article comment functionality
  - 评论审核机制 | Comment moderation mechanism
  - 垃圾评论过滤 | Spam comment filtering
  - 评论回复支持 | Comment reply support

### ✅ SEO优化功能 | SEO Optimization Features
- **搜索引擎优化 | Search Engine Optimization**
  - 自动生成sitemap.xml | Auto-generate sitemap.xml
  - 智能robots.txt管理 | Smart robots.txt management
  - 结构化数据支持 | Structured data support
  - 页面元数据优化 | Page metadata optimization

- **SEO分析工具 | SEO Analysis Tools**
  - 页面SEO评分 | Page SEO scoring
  - 关键词密度分析 | Keyword density analysis
  - 优化建议提供 | Optimization recommendations

### ✅ 主题和定制 | Themes and Customization
- **可视化主题配置 | Visual Theme Configuration**
  - 颜色主题自定义 | Custom color themes
  - 字体样式选择 | Font style selection
  - 布局配置 | Layout configuration
  - 品牌元素管理 | Brand element management

- **网站配置 | Website Configuration**
  - 网站基本信息 | Basic website information
  - 联系方式设置 | Contact information settings
  - 社交媒体链接 | Social media links
  - 邮件服务配置 | Email service configuration

### ✅ 系统管理 | System Management
- **系统监控 | System Monitoring**
  - 系统状态检查 | System status checks
  - 性能监控 | Performance monitoring
  - 错误日志记录 | Error logging
  - 健康检查报告 | Health check reports

- **数据管理 | Data Management**
  - 数据库备份 | Database backup
  - 数据导入导出 | Data import/export
  - 系统配置管理 | System configuration management

## 🔐 认证和安全 | Authentication and Security

### 认证机制 | Authentication Mechanism
- **双重认证** - JWT Token + HTTP Cookie | Dual authentication
- **会话管理** - 自动过期和刷新 | Session management with auto-expiry
- **权限验证** - API级别和页面级别保护 | Permission verification at API and page levels

### 安全特性 | Security Features
- **密码加密** - bcrypt哈希加密 | Password encryption with bcrypt
- **SQL注入防护** - Prisma ORM保护 | SQL injection protection via Prisma ORM
- **XSS防护** - 输入验证和输出转义 | XSS protection with input validation
- **CSRF保护** - Token验证机制 | CSRF protection with token verification

### 访问控制 | Access Control
- **管理后台保护** - 只有登录用户可访问 | Admin dashboard protection
- **API权限控制** - 基于角色的访问控制 | Role-based API access control
- **页面级权限** - 动态权限检查 | Page-level permission checks

## 🛠️ 技术栈详情 | Tech Stack Details

### 前端技术 | Frontend Technologies
- **框架** - Next.js 15 (App Router) | Framework
- **语言** - TypeScript | Language
- **样式** - Tailwind CSS | Styling
- **图标** - Lucide React | Icons
- **状态管理** - React Context API | State Management

### 后端技术 | Backend Technologies
- **运行时** - Node.js | Runtime
- **框架** - Next.js API Routes | Framework
- **数据库** - PostgreSQL | Database
- **ORM** - Prisma | Object-Relational Mapping
- **认证** - JWT + bcrypt | Authentication

### 开发工具 | Development Tools
- **包管理** - npm | Package Manager
- **代码规范** - ESLint + Prettier | Code Standards
- **类型检查** - TypeScript | Type Checking
- **数据库工具** - Prisma Studio | Database Tools

## 🎯 快速开始 | Quick Start

### 环境要求 | Requirements
- Node.js 18+
- PostgreSQL 14+
- npm 或 yarn | npm or yarn

### 安装步骤 | Installation Steps

1. **克隆项目 | Clone the repository**
   ```bash
   git clone <repository-url>
   cd instai_cms
   ```

2. **安装依赖 | Install dependencies**
   ```bash
   npm install
   ```

3. **环境配置 | Environment configuration**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，配置数据库连接等
   # Edit .env file, configure database connection, etc.
   ```

   更新以下环境变量 | Update the following environment variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/instai_cms"
   JWT_SECRET="your-secure-jwt-secret-here"
   NEXTAUTH_SECRET="your-nextauth-secret-here"
   NEXTAUTH_URL="http://localhost:3000"
   ```

4. **数据库设置 | Database setup**
   ```bash
   npx prisma migrate dev
   npx prisma db seed
   ```

5. **启动开发服务器 | Start development server**
   ```bash
   npm run dev
   ```

6. **访问系统 | Access the system**
   - 前端网站 | Frontend: http://localhost:3000
   - 管理后台 | Admin: http://localhost:3000/admin
   - 登录页面 | Login: http://localhost:3000/auth/login

### 演示账号 | Demo Accounts

- **管理员账号 | Admin Account**
  - 邮箱 | Email: `<EMAIL>`
  - 密码 | Password: `admin123`
  - 权限 | Permissions: 完全访问权限 | Full access permissions

- **编辑员账号 | Editor Account**
  - 邮箱 | Email: `<EMAIL>`
  - 密码 | Password: `editor123`
  - 权限 | Permissions: 内容编辑权限 | Content editing permissions

## 📊 系统统计 | System Statistics

### 页面统计 | Page Statistics
- **管理后台页面 | Admin Dashboard Pages**: 13个
  - Dashboard、Posts、Categories、Tags
  - Products、Product Categories、Inquiries
  - Users、Media、Comments、Themes、Settings、System Status

- **前端公开页面 | Frontend Public Pages**: 6个
  - 首页、博客、产品展示、联系页面 | Homepage, Blog, Products, Contact
  - 网站地图、Robots.txt | Sitemap, Robots.txt

### API接口统计 | API Statistics
- **接口组数量 | API Groups**: 12个
  - Auth、Posts、Categories、Tags、Products
  - Product Categories、Inquiries、Users、Comments
  - Settings、Themes、Media

- **总接口数量 | Total APIs**: 50+ 个RESTful API

### 数据库表统计 | Database Tables
- **核心数据表 | Core Tables**: 12个
  - User、Post、Category、Tag、Product
  - ProductCategory、Inquiry、Comment、Media
  - Setting、Theme、Session

## 🚪 退出登录功能 | Logout Functionality

### 退出按钮位置 | Logout Button Location
- **位置** | Location: 管理后台页面右上角 | Top-right corner of admin dashboard
- **外观** | Appearance: 红色背景，白色文字"Logout" | Red background, white "Logout" text
- **交互** | Interaction: 鼠标悬停时变为深红色 | Darker red on hover

### 退出登录流程 | Logout Process
1. 用户点击"Logout"按钮 | User clicks "Logout" button
2. 调用 `/api/auth/logout` API | Call logout API
3. 清除服务端cookie | Clear server-side cookies
4. 清除客户端localStorage | Clear client-side localStorage
5. 重置用户状态 | Reset user state
6. 自动跳转到登录页面 | Auto-redirect to login page

### 访问控制 | Access Control
- **未登录用户** | Unauthenticated users: 显示"Access Denied"页面 | Show "Access Denied" page
- **已登录用户** | Authenticated users: 正常显示管理界面和退出按钮 | Show admin interface and logout button

## 📁 项目结构 | Project Structure

```
instai_cms/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理后台页面 | Admin dashboard pages (13个)
│   │   ├── page.tsx       # 仪表板 | Dashboard
│   │   ├── posts/         # 文章管理 | Posts management
│   │   ├── categories/    # 分类管理 | Categories management
│   │   ├── tags/          # 标签管理 | Tags management
│   │   ├── products/      # 产品管理 | Products management
│   │   ├── product-categories/ # 产品分类 | Product categories
│   │   ├── inquiries/     # 询盘管理 | Inquiries management
│   │   ├── users/         # 用户管理 | Users management
│   │   ├── comments/      # 评论管理 | Comments management
│   │   ├── media/         # 媒体管理 | Media management
│   │   ├── themes/        # 主题配置 | Theme configuration
│   │   ├── settings/      # 系统设置 | System settings
│   │   └── system-status/ # 系统状态 | System status
│   ├── api/               # API路由 | API routes (12个接口组)
│   │   ├── auth/          # 认证接口 | Authentication APIs
│   │   ├── posts/         # 文章接口 | Posts APIs
│   │   ├── categories/    # 分类接口 | Categories APIs
│   │   ├── tags/          # 标签接口 | Tags APIs
│   │   ├── products/      # 产品接口 | Products APIs
│   │   ├── product-categories/ # 产品分类接口 | Product categories APIs
│   │   ├── inquiries/     # 询盘接口 | Inquiries APIs
│   │   ├── users/         # 用户接口 | Users APIs
│   │   ├── comments/      # 评论接口 | Comments APIs
│   │   ├── media/         # 媒体接口 | Media APIs
│   │   ├── settings/      # 设置接口 | Settings APIs
│   │   └── themes/        # 主题接口 | Themes APIs
│   ├── auth/              # 认证页面 | Authentication pages
│   ├── blog/              # 博客页面 | Blog pages
│   ├── products/          # 产品页面 | Products pages
│   ├── contact/           # 联系页面 | Contact page
│   ├── components/        # 可复用组件 | Reusable components
│   ├── contexts/          # React上下文 | React contexts
│   ├── lib/               # 工具库 | Utility libraries
│   └── types/             # TypeScript类型定义 | TypeScript type definitions
├── prisma/                # 数据库相关 | Database related
│   ├── schema.prisma      # 数据库模式 | Database schema
│   └── seed.ts           # 数据库种子 | Database seeding script
├── public/                # 静态资源 | Static assets
└── uploads/              # 文件上传目录 | File uploads directory
```

## 🔧 可用脚本 | Available Scripts

- `npm run dev` - 启动开发服务器 | Start development server
- `npm run build` - 构建生产版本 | Build for production
- `npm run start` - 启动生产服务器 | Start production server
- `npm run lint` - 运行ESLint | Run ESLint
- `npm run db:seed` - 数据库种子数据 | Seed the database with sample data
- `npm run db:reset` - 重置数据库 | Reset database and reseed

## 📚 完整API文档 | Complete API Documentation

### 认证接口 | Authentication Endpoints
- `POST /api/auth/login` - 用户登录 | User login
- `POST /api/auth/logout` - 用户退出 | User logout
- `GET /api/auth/me` - 获取当前用户 | Get current user

### 内容管理接口 | Content Management Endpoints
- `GET /api/posts` - 获取所有文章 | Get all posts
- `POST /api/posts` - 创建新文章 | Create new post
- `GET /api/posts/[id]` - 获取单篇文章 | Get single post
- `PUT /api/posts/[id]` - 更新文章 | Update post
- `DELETE /api/posts/[id]` - 删除文章 | Delete post

### 分类和标签接口 | Category & Tag Endpoints
- `GET /api/categories` - 获取所有分类 | Get all categories
- `POST /api/categories` - 创建分类 | Create category
- `PUT /api/categories/[id]` - 更新分类 | Update category
- `DELETE /api/categories/[id]` - 删除分类 | Delete category
- `GET /api/tags` - 获取所有标签 | Get all tags
- `POST /api/tags` - 创建标签 | Create tag
- `PUT /api/tags/[id]` - 更新标签 | Update tag
- `DELETE /api/tags/[id]` - 删除标签 | Delete tag

### 产品管理接口 | Product Management Endpoints
- `GET /api/products` - 获取所有产品 | Get all products
- `POST /api/products` - 创建产品 | Create product
- `PUT /api/products/[id]` - 更新产品 | Update product
- `DELETE /api/products/[id]` - 删除产品 | Delete product
- `GET /api/product-categories` - 获取产品分类 | Get product categories
- `POST /api/product-categories` - 创建产品分类 | Create product category

### 客户关系接口 | Customer Relations Endpoints
- `GET /api/inquiries` - 获取询盘列表 | Get inquiries list
- `POST /api/inquiries` - 创建询盘 | Create inquiry
- `PUT /api/inquiries/[id]` - 更新询盘状态 | Update inquiry status
- `GET /api/inquiries/stats` - 获取询盘统计 | Get inquiry statistics
- `GET /api/comments` - 获取评论列表 | Get comments list
- `POST /api/comments` - 创建评论 | Create comment
- `PUT /api/comments/[id]` - 更新评论状态 | Update comment status

### 用户管理接口 | User Management Endpoints
- `GET /api/users` - 获取用户列表 | Get users list
- `POST /api/users` - 创建用户 | Create user
- `PUT /api/users/[id]` - 更新用户信息 | Update user info
- `DELETE /api/users/[id]` - 删除用户 | Delete user

### 媒体管理接口 | Media Management Endpoints
- `GET /api/media` - 获取媒体文件 | Get media files
- `POST /api/media/upload` - 上传文件 | Upload files
- `DELETE /api/media/[id]` - 删除媒体文件 | Delete media file

### 系统设置接口 | System Settings Endpoints
- `GET /api/settings` - 获取系统设置 | Get system settings
- `PUT /api/settings` - 更新系统设置 | Update system settings
- `GET /api/themes/config` - 获取主题配置 | Get theme configuration
- `PUT /api/themes/config` - 更新主题配置 | Update theme configuration

## 📈 性能优化 | Performance Optimization

### 前端优化 | Frontend Optimization
- **代码分割** - 动态导入和懒加载 | Code splitting with dynamic imports
- **图片优化** - Next.js Image组件 | Image optimization with Next.js Image
- **缓存策略** - 静态资源缓存 | Caching strategy for static assets
- **SEO优化** - 服务端渲染(SSR) | SEO optimization with SSR

### 后端优化 | Backend Optimization
- **数据库优化** - 索引优化和查询优化 | Database optimization with indexes
- **API缓存** - 响应缓存机制 | API response caching
- **连接池** - 数据库连接池管理 | Database connection pooling
- **错误处理** - 统一错误处理机制 | Unified error handling

## 🚀 部署指南 | Deployment Guide

### 生产环境部署 | Production Deployment

1. **构建项目 | Build the project**
   ```bash
   npm run build
   ```

2. **环境变量配置 | Environment variables**
   ```bash
   # 设置生产环境变量 | Set production environment variables
   NODE_ENV=production
   DATABASE_URL=<production-database-url>
   JWT_SECRET=<secure-jwt-secret>
   NEXTAUTH_SECRET=<secure-nextauth-secret>
   ```

3. **数据库迁移 | Database migration**
   ```bash
   npx prisma migrate deploy
   npx prisma db seed
   ```

4. **启动生产服务器 | Start production server**
   ```bash
   npm start
   ```

### Vercel部署 | Vercel Deployment (推荐 | Recommended)
1. 将代码推送到GitHub | Push code to GitHub
2. 连接仓库到Vercel | Connect repository to Vercel
3. 在Vercel控制台设置环境变量 | Set environment variables in Vercel dashboard
4. 自动部署 | Automatic deployment

### Docker部署 | Docker Deployment
```dockerfile
# Dockerfile 配置 | Dockerfile configuration
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

```bash
# 构建镜像 | Build image
docker build -t enterprise-cms .

# 运行容器 | Run container
docker run -p 3000:3000 enterprise-cms
```

## 📞 支持和维护 | Support and Maintenance

### 系统监控 | System Monitoring
- **健康检查** | Health checks: `/admin/system-status`
- **API状态** | API status: 实时API响应监控 | Real-time API monitoring
- **错误日志** | Error logs: 系统错误记录和分析 | System error logging and analysis

### 备份策略 | Backup Strategy
- **数据库备份** | Database backup: 定期自动备份 | Regular automated backups
- **文件备份** | File backup: 媒体文件备份 | Media file backups
- **配置备份** | Configuration backup: 系统配置导出 | System configuration export

### 更新维护 | Updates and Maintenance
- **版本控制** | Version control: Git版本管理 | Git version management
- **依赖更新** | Dependency updates: 定期更新依赖包 | Regular dependency updates
- **安全补丁** | Security patches: 及时应用安全更新 | Timely security updates

## 🎨 自定义指南 | Customization Guide

### 样式定制 | Styling Customization
项目使用Tailwind CSS进行样式设计，您可以通过以下方式自定义：
- 修改 `tailwind.config.js` | Modify `tailwind.config.js`
- 更新 `app/globals.css` 中的CSS变量 | Update CSS variables in `app/globals.css`
- 在 `app/components/` 中创建自定义组件 | Create custom components in `app/components/`

### 数据库模式 | Database Schema
要修改数据库模式 | To modify the database schema:
1. 更新 `prisma/schema.prisma` | Update `prisma/schema.prisma`
2. 运行 `npx prisma db push` 应用更改 | Run `npx prisma db push` to apply changes
3. 更新 `app/types/` 中的TypeScript类型 | Update TypeScript types in `app/types/`

## 🤝 贡献指南 | Contributing Guide

1. Fork 仓库 | Fork the repository
2. 创建功能分支 | Create a feature branch (`git checkout -b feature/amazing-feature`)
3. 提交更改 | Commit your changes (`git commit -m 'Add amazing feature'`)
4. 推送到分支 | Push to the branch (`git push origin feature/amazing-feature`)
5. 打开Pull Request | Open a Pull Request

## 📄 许可证 | License

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 致谢 | Acknowledgments

- [Next.js](https://nextjs.org/) - React框架 | The React framework
- [Prisma](https://prisma.io/) - 数据库工具包 | Database toolkit
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架 | CSS framework
- [Lucide React](https://lucide.dev/) - 图标库 | Icon library

## 📞 技术支持 | Technical Support

如果您有任何问题或需要帮助，请 | If you have any questions or need help, please:
- 在GitHub上提交issue | Open an issue on GitHub
- 查看文档 | Check the documentation
- 加入我们的社区讨论 | Join our community discussions

---

**企业官网CMS系统** - 现代化、安全、高效的企业级内容管理解决方案
**Enterprise Website CMS** - Modern, secure, and efficient enterprise-level content management solution

Built with ❤️ using Next.js 15 and TypeScript
