# API文档 | API Documentation

## 🔐 认证说明 | Authentication

### 认证方式 | Authentication Methods
- **JWT Token**: 存储在localStorage中 | Stored in localStorage
- **HTTP Cookie**: 服务端会话管理 | Server-side session management
- **Authorization Header**: `Bearer <token>` 格式 | Bearer token format

### 权限级别 | Permission Levels
- **PUBLIC**: 公开访问，无需认证 | Public access, no authentication required
- **USER**: 需要用户登录 | Requires user login
- **EDITOR**: 需要编辑员权限 | Requires editor permissions
- **ADMIN**: 需要管理员权限 | Requires admin permissions

## 📚 API接口列表 | API Endpoints

### 🔑 认证接口 | Authentication APIs

#### POST /api/auth/login
**用户登录 | User Login**
- **权限**: PUBLIC
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```
- **响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "Admin User",
      "role": "ADMIN"
    },
    "token": "jwt-token-here"
  }
}
```

#### POST /api/auth/logout
**用户退出 | User Logout**
- **权限**: USER
- **响应**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### GET /api/auth/me
**获取当前用户信息 | Get Current User**
- **权限**: USER
- **响应**:
```json
{
  "success": true,
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "Admin User",
    "role": "ADMIN"
  }
}
```

### 📝 文章接口 | Posts APIs

#### GET /api/posts
**获取文章列表 | Get Posts List**
- **权限**: PUBLIC
- **查询参数**:
  - `page`: 页码 | Page number (默认: 1)
  - `limit`: 每页数量 | Items per page (默认: 10)
  - `category`: 分类ID | Category ID
  - `tag`: 标签ID | Tag ID
  - `status`: 发布状态 | Publication status
- **响应**:
```json
{
  "success": true,
  "data": {
    "posts": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

#### POST /api/posts
**创建文章 | Create Post**
- **权限**: EDITOR
- **请求体**:
```json
{
  "title": "文章标题",
  "content": "文章内容",
  "excerpt": "文章摘要",
  "categoryId": "category-id",
  "tagIds": ["tag-id-1", "tag-id-2"],
  "status": "PUBLISHED",
  "seo": {
    "metaTitle": "SEO标题",
    "metaDescription": "SEO描述"
  }
}
```

#### GET /api/posts/[id]
**获取单篇文章 | Get Single Post**
- **权限**: PUBLIC
- **响应**: 返回完整文章信息 | Returns complete post information

#### PUT /api/posts/[id]
**更新文章 | Update Post**
- **权限**: EDITOR
- **请求体**: 与创建文章相同 | Same as create post

#### DELETE /api/posts/[id]
**删除文章 | Delete Post**
- **权限**: ADMIN

### 🏷️ 分类接口 | Categories APIs

#### GET /api/categories
**获取分类列表 | Get Categories List**
- **权限**: PUBLIC
- **查询参数**:
  - `tree`: 是否返回树形结构 | Return tree structure (true/false)
- **响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "category-id",
      "name": "分类名称",
      "slug": "category-slug",
      "description": "分类描述",
      "parentId": null,
      "children": [...]
    }
  ]
}
```

#### POST /api/categories
**创建分类 | Create Category**
- **权限**: EDITOR
- **请求体**:
```json
{
  "name": "分类名称",
  "slug": "category-slug",
  "description": "分类描述",
  "parentId": "parent-category-id"
}
```

### 🏷️ 标签接口 | Tags APIs

#### GET /api/tags
**获取标签列表 | Get Tags List**
- **权限**: PUBLIC

#### POST /api/tags
**创建标签 | Create Tag**
- **权限**: EDITOR
- **请求体**:
```json
{
  "name": "标签名称",
  "slug": "tag-slug",
  "description": "标签描述"
}
```

### 🛍️ 产品接口 | Products APIs

#### GET /api/products
**获取产品列表 | Get Products List**
- **权限**: PUBLIC
- **查询参数**:
  - `page`: 页码 | Page number
  - `limit`: 每页数量 | Items per page
  - `category`: 产品分类ID | Product category ID
  - `active`: 是否激活 | Active status (true/false)
- **响应**:
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "product-id",
        "name": "产品名称",
        "description": "产品描述",
        "price": 99.99,
        "images": ["image-url-1", "image-url-2"],
        "category": {
          "id": "category-id",
          "name": "分类名称"
        },
        "active": true
      }
    ],
    "pagination": {...}
  }
}
```

#### POST /api/products
**创建产品 | Create Product**
- **权限**: EDITOR
- **请求体**:
```json
{
  "name": "产品名称",
  "description": "产品描述",
  "price": 99.99,
  "categoryId": "category-id",
  "images": ["image-url-1"],
  "specifications": {
    "size": "Large",
    "color": "Blue"
  },
  "active": true
}
```

### 📞 询盘接口 | Inquiries APIs

#### GET /api/inquiries
**获取询盘列表 | Get Inquiries List**
- **权限**: EDITOR
- **查询参数**:
  - `status`: 询盘状态 | Inquiry status
  - `page`: 页码 | Page number

#### POST /api/inquiries
**创建询盘 | Create Inquiry**
- **权限**: PUBLIC
- **请求体**:
```json
{
  "name": "客户姓名",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "公司名称",
  "subject": "询盘主题",
  "message": "询盘内容",
  "productId": "product-id"
}
```

#### GET /api/inquiries/stats
**获取询盘统计 | Get Inquiry Statistics**
- **权限**: EDITOR
- **响应**:
```json
{
  "success": true,
  "data": {
    "total": 100,
    "pending": 20,
    "processing": 30,
    "completed": 50,
    "thisMonth": 15,
    "lastMonth": 25
  }
}
```

### 👥 用户接口 | Users APIs

#### GET /api/users
**获取用户列表 | Get Users List**
- **权限**: ADMIN
- **查询参数**:
  - `role`: 用户角色 | User role
  - `active`: 激活状态 | Active status

#### POST /api/users
**创建用户 | Create User**
- **权限**: ADMIN
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "name": "用户姓名",
  "password": "secure-password",
  "role": "EDITOR"
}
```

### 💬 评论接口 | Comments APIs

#### GET /api/comments
**获取评论列表 | Get Comments List**
- **权限**: EDITOR
- **查询参数**:
  - `postId`: 文章ID | Post ID
  - `status`: 评论状态 | Comment status

#### POST /api/comments
**创建评论 | Create Comment**
- **权限**: PUBLIC
- **请求体**:
```json
{
  "postId": "post-id",
  "name": "评论者姓名",
  "email": "<EMAIL>",
  "content": "评论内容",
  "parentId": "parent-comment-id"
}
```

### 📁 媒体接口 | Media APIs

#### GET /api/media
**获取媒体文件列表 | Get Media Files List**
- **权限**: EDITOR

#### POST /api/media/upload
**上传文件 | Upload File**
- **权限**: EDITOR
- **请求**: multipart/form-data
- **响应**:
```json
{
  "success": true,
  "data": {
    "id": "media-id",
    "filename": "image.jpg",
    "url": "/uploads/image.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg"
  }
}
```

### ⚙️ 设置接口 | Settings APIs

#### GET /api/settings
**获取系统设置 | Get System Settings**
- **权限**: ADMIN

#### PUT /api/settings
**更新系统设置 | Update System Settings**
- **权限**: ADMIN
- **请求体**:
```json
{
  "siteName": "网站名称",
  "siteDescription": "网站描述",
  "contactEmail": "<EMAIL>",
  "socialMedia": {
    "facebook": "facebook-url",
    "twitter": "twitter-url"
  }
}
```

### 🎨 主题接口 | Themes APIs

#### GET /api/themes/config
**获取主题配置 | Get Theme Configuration**
- **权限**: PUBLIC

#### PUT /api/themes/config
**更新主题配置 | Update Theme Configuration**
- **权限**: ADMIN
- **请求体**:
```json
{
  "primaryColor": "#3B82F6",
  "secondaryColor": "#10B981",
  "fontFamily": "Inter",
  "layout": "modern"
}
```

## 🚨 错误处理 | Error Handling

### 标准错误响应 | Standard Error Response
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE",
  "details": {...}
}
```

### 常见错误码 | Common Error Codes
- **401**: 未认证 | Unauthorized
- **403**: 权限不足 | Forbidden
- **404**: 资源不存在 | Not Found
- **422**: 验证失败 | Validation Failed
- **500**: 服务器错误 | Internal Server Error

## 📊 分页格式 | Pagination Format

### 分页参数 | Pagination Parameters
- `page`: 页码，从1开始 | Page number, starts from 1
- `limit`: 每页数量，默认10，最大100 | Items per page, default 10, max 100

### 分页响应 | Pagination Response
```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

---

**API版本**: v1
**基础URL**: `http://localhost:3000/api`
**内容类型**: `application/json`
