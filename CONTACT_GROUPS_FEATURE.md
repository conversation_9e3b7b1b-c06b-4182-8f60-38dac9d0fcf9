# 联系页面多联系方式组功能

## 🎯 功能概述

为联系页面添加了多联系方式组功能，支持按部门或业务类型组织多个电话和邮箱，提供更专业和结构化的企业联系信息展示。

## 📋 核心特性

### 1. 联系组管理
- **多组支持**：可以创建多个联系组（如销售部、技术支持、客服等）
- **组信息配置**：每个组包含名称、描述和联系方式
- **动态管理**：支持添加、删除、编辑联系组

### 2. 多联系方式
- **多电话支持**：每个组可以配置多个电话号码
- **多邮箱支持**：每个组可以配置多个邮箱地址
- **标签系统**：每个联系方式都有标签和描述

### 3. 管理界面
- **直观配置**：清晰的分组管理界面
- **实时编辑**：支持实时添加、删除联系方式
- **批量操作**：支持批量管理多个联系组

## 🔧 技术实现

### 数据结构
```typescript
interface ContactConfig {
  info: {
    address: string
    workingHours: string
    contactGroups: Array<{
      id: string
      name: string
      description: string
      phones: Array<{
        label: string
        number: string
        description?: string
      }>
      emails: Array<{
        label: string
        address: string
        description?: string
      }>
    }>
  }
}
```

### 默认配置示例
```json
{
  "contactGroups": [
    {
      "id": "general",
      "name": "General Support",
      "description": "For general inquiries and support",
      "phones": [
        {
          "label": "Main Line",
          "number": "+****************",
          "description": "Toll-free support line"
        }
      ],
      "emails": [
        {
          "label": "General Support",
          "address": "<EMAIL>",
          "description": "24-hour response guarantee"
        }
      ]
    },
    {
      "id": "sales",
      "name": "Sales Department",
      "description": "For sales inquiries and product information",
      "phones": [
        {
          "label": "Sales Hotline",
          "number": "+****************",
          "description": "Direct sales line"
        }
      ],
      "emails": [
        {
          "label": "Sales Team",
          "address": "<EMAIL>",
          "description": "Product inquiries and quotes"
        }
      ]
    }
  ]
}
```

## 🎨 前端展示

### 联系组展示
- **分组显示**：每个联系组独立展示
- **卡片布局**：联系方式以卡片形式展示
- **响应式设计**：适配桌面和移动设备
- **交互优化**：电话和邮箱可直接点击

### 视觉设计
- **清晰分组**：使用分割线和标题区分不同组
- **图标标识**：电话和邮箱使用不同图标
- **颜色区分**：使用颜色区分不同类型的联系方式
- **悬停效果**：链接悬停时的视觉反馈

## 🚀 管理功能

### 联系组管理
1. **添加组**：点击"Add Group"按钮创建新联系组
2. **编辑组**：修改组名称和描述
3. **删除组**：移除不需要的联系组
4. **排序**：支持联系组的排序

### 联系方式管理
1. **添加电话**：为每个组添加多个电话号码
2. **添加邮箱**：为每个组添加多个邮箱地址
3. **编辑信息**：修改标签、号码/地址、描述
4. **删除条目**：移除不需要的联系方式

### 配置界面特性
- **网格布局**：使用12列网格系统布局
- **实时预览**：配置更改立即生效
- **表单验证**：确保输入数据的有效性
- **用户友好**：直观的操作界面和提示

## 📝 使用场景

### 企业多部门
```json
{
  "contactGroups": [
    {
      "name": "Sales Department",
      "phones": [
        {"label": "Sales Manager", "number": "+****************"},
        {"label": "Sales Hotline", "number": "+****************"}
      ],
      "emails": [
        {"label": "Sales Team", "address": "<EMAIL>"},
        {"label": "Sales Manager", "address": "<EMAIL>"}
      ]
    },
    {
      "name": "Technical Support",
      "phones": [
        {"label": "Tech Support", "number": "+****************"},
        {"label": "Emergency Line", "number": "+****************"}
      ],
      "emails": [
        {"label": "Tech Support", "address": "<EMAIL>"},
        {"label": "Bug Reports", "address": "<EMAIL>"}
      ]
    }
  ]
}
```

### 地区分支
```json
{
  "contactGroups": [
    {
      "name": "North America Office",
      "phones": [
        {"label": "Main Office", "number": "+****************"},
        {"label": "Customer Service", "number": "+****************"}
      ]
    },
    {
      "name": "Europe Office",
      "phones": [
        {"label": "London Office", "number": "+44 20 7000 0001"},
        {"label": "Paris Office", "number": "+33 1 40 00 00 01"}
      ]
    }
  ]
}
```

## 🔄 向后兼容

### 数据迁移
- **自动转换**：旧的单一电话/邮箱自动转换为联系组
- **默认组**：为现有配置创建默认的"General Support"组
- **无缝升级**：现有配置不会丢失

### API兼容性
- **扩展结构**：新的数据结构向后兼容
- **默认值**：提供合理的默认配置
- **错误处理**：优雅处理配置缺失的情况

## 📊 优势特性

### 用户体验
- 🎯 **专业展示**：按部门组织联系信息更专业
- 📱 **响应式设计**：在所有设备上都有良好显示
- 🔗 **直接联系**：电话和邮箱可直接点击
- 🎨 **视觉清晰**：清晰的分组和标识

### 管理便利
- ⚡ **实时编辑**：配置更改立即生效
- 🔧 **灵活配置**：支持任意数量的组和联系方式
- 📝 **批量管理**：高效的批量操作功能
- 🔄 **动态更新**：无需重启即可更新配置

### 企业级功能
- 🏢 **多部门支持**：适合大型企业的组织结构
- 🌍 **多地区支持**：支持不同地区的联系方式
- 📞 **多渠道联系**：电话、邮箱等多种联系方式
- 🎯 **精准分流**：客户可以直接联系相关部门

现在联系页面具备了企业级的多联系方式组织能力，可以更好地服务于复杂的企业联系需求！
