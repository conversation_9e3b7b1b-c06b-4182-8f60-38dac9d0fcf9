{"name": "nextjs-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma db push --force-reset && npm run db:seed"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@uiw/react-md-editor": "^4.0.8", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.0.0", "next-auth": "^4.24.11", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "15.0.0", "postcss": "^8.5.6", "prisma": "^5.0.0", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}}